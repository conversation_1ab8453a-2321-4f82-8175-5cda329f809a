import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminFunction, AdminFunctionVersion, UserFunction, UserFunctionVersion } from '../src/modules/data/functions/entities';
import { FunctionStatusEnum } from '../src/modules/data/functions/constants';
import { AccessTypeEnum } from '../src/modules/data/functions/constants';
import { JwtService } from '@nestjs/jwt';

describe('Function Clone (e2e)', () => {
  let app: INestApplication;
  let adminFunctionRepository: Repository<AdminFunction>;
  let adminVersionRepository: Repository<AdminFunctionVersion>;
  let userFunctionRepository: Repository<UserFunction>;
  let userVersionRepository: Repository<UserFunctionVersion>;
  let jwtService: JwtService;
  
  // Test data
  const testUserId = 1;
  const testAdminFunctionId = 'test-admin-function-id';
  const testAdminVersionId = 'test-admin-version-id';
  
  // JWT token for authentication
  let authToken: string;
  
  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    
    adminFunctionRepository = moduleFixture.get<Repository<AdminFunction>>(
      getRepositoryToken(AdminFunction),
    );
    adminVersionRepository = moduleFixture.get<Repository<AdminFunctionVersion>>(
      getRepositoryToken(AdminFunctionVersion),
    );
    userFunctionRepository = moduleFixture.get<Repository<UserFunction>>(
      getRepositoryToken(UserFunction),
    );
    userVersionRepository = moduleFixture.get<Repository<UserFunctionVersion>>(
      getRepositoryToken(UserFunctionVersion),
    );
    jwtService = moduleFixture.get<JwtService>(JwtService);
    
    // Create JWT token for authentication
    authToken = jwtService.sign({ id: testUserId, type: 'user' });
    
    // Setup test data
    await setupTestData();
  });
  
  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
    await app.close();
  });
  
  async function setupTestData() {
    // Create test admin function
    const adminFunction = adminFunctionRepository.create({
      id: testAdminFunctionId,
      name: 'Test Admin Function',
      description: 'Test Admin Description',
      accessType: AccessTypeEnum.PUBLIC,
      status: FunctionStatusEnum.APPROVED,
      versionDefault: testAdminVersionId,
      createdBy: 1,
      updatedBy: 1,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    await adminFunctionRepository.save(adminFunction);
    
    // Create test admin function version
    const adminVersion = adminVersionRepository.create({
      id: testAdminVersionId,
      functionId: testAdminFunctionId,
      versionNumber: 1,
      functionName: 'testAdminFunction',
      functionDescription: 'Test Admin Description',
      parameters: { type: 'object', properties: {} },
      changeDescription: 'Initial admin version',
      status: FunctionStatusEnum.APPROVED,
      createdBy: 1,
      updatedBy: 1,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    await adminVersionRepository.save(adminVersion);
  }
  
  async function cleanupTestData() {
    // Delete any user functions and versions created during tests
    await userVersionRepository.delete({ userId: testUserId });
    await userFunctionRepository.delete({ userId: testUserId });
    
    // Delete admin test data
    await adminVersionRepository.delete({ id: testAdminVersionId });
    await adminFunctionRepository.delete({ id: testAdminFunctionId });
  }
  
  describe('/user/functions/clone (POST)', () => {
    it('should clone an admin function with user_id in function name', async () => {
      return request(app.getHttpServer())
        .post('/user/functions/clone')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          adminFunctionId: testAdminFunctionId,
        })
        .expect(200)
        .then(response => {
          expect(response.body.success).toBe(true);
          expect(response.body.message).toContain('Clone Function thành công');
          expect(response.body.data).toBeDefined();
          expect(response.body.data.function).toBeDefined();
          expect(response.body.data.function.originalId).toBe(testAdminFunctionId);
          
          // Get the created function ID
          const userFunctionId = response.body.data.function.id;
          
          // Verify that the function was created in the database
          return userFunctionRepository.findOne({ where: { id: userFunctionId } });
        })
        .then(userFunction => {
          expect(userFunction).toBeDefined();
          expect(userFunction.originalId).toBe(testAdminFunctionId);
          
          // Verify that a version was created with user_id in the name
          return userVersionRepository.findOne({ where: { originalFunctionId: userFunction.id } });
        })
        .then(userVersion => {
          expect(userVersion).toBeDefined();
          expect(userVersion.functionName).toContain(`_u${testUserId}`);
        });
    });
    
    it('should return 404 when admin function not found', async () => {
      return request(app.getHttpServer())
        .post('/user/functions/clone')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          adminFunctionId: 'non-existent-id',
        })
        .expect(404);
    });
    
    it('should return 401 when not authenticated', async () => {
      return request(app.getHttpServer())
        .post('/user/functions/clone')
        .send({
          adminFunctionId: testAdminFunctionId,
        })
        .expect(401);
    });
  });
  
  describe('/user/functions/clone-all-public (POST)', () => {
    it('should clone all public admin functions', async () => {
      return request(app.getHttpServer())
        .post('/user/functions/clone-all-public')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(200)
        .then(response => {
          expect(response.body.success).toBe(true);
          expect(response.body.message).toContain('Clone tất cả Function công khai thành công');
          expect(response.body.data).toBeDefined();
          expect(response.body.data.clonedCount).toBeGreaterThanOrEqual(0);
          expect(response.body.data.skippedCount).toBeGreaterThanOrEqual(0);
        });
    });
    
    it('should return 401 when not authenticated', async () => {
      return request(app.getHttpServer())
        .post('/user/functions/clone-all-public')
        .send({})
        .expect(401);
    });
  });
});
