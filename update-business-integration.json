{"productId": 21, "product": {"name": "iPhone 16 Pro Max - <PERSON><PERSON><PERSON> bản đặc biệt", "price": {"listPrice": 37000000, "salePrice": 34000000, "currency": "VND"}, "typePrice": "HAS_PRICE", "description": "iPhone 16 Pro Max phiên bản đặc biệt với chip A18 Pro, camera 48MP cải tiến, màn hình Super Retina XDR 6.7 inch, pin dung lượng cao và iOS 18. Tặng kèm bộ phụ kiện cao cấp.", "images": [{"operation": "DELETE", "key": "business/IMAGE/2025/05/1746873833036-7f877269-40dc-4bb1-be68-80234fa6813e"}, {"operation": "ADD", "mimeType": "image/jpeg", "size": 180000, "name": "iphone16promax-special.jpg"}, {"operation": "ADD", "mimeType": "image/png", "size": 220000, "name": "iphone16promax-features.png"}], "tags": ["smartphone", "apple", "iphone", "iphone16promax", "ios", "special-edition"], "shipmentConfig": {"widthCm": 7.8, "heightCm": 1, "lengthCm": 16, "weightGram": 220}}, "groupFormId": 13, "groupForm": {"label": "Thông tin sản phẩm iPhone 16 Pro Max - <PERSON><PERSON><PERSON> bản đặc biệt"}, "customFields": [{"id": 20, "field": {"component": "Text Input", "label": "Mã model", "type": "text", "required": true, "configJson": {"size": "small", "variant": "outlined", "validation": {"pattern": "^[A-Z0-9]+$", "maxLength": 25, "minLength": 5}, "placeholder": "Nhập mã model (VD: A2999)"}}}, {"id": 18, "field": {"component": "Select Dropdown", "label": "<PERSON><PERSON> lư<PERSON><PERSON> bộ nhớ", "type": "select", "required": true, "configJson": {"size": "medium", "options": ["128GB", "256GB", "512GB", "1TB", "2TB"], "variant": "filled", "validation": {"maxLength": 15, "minLength": 2}, "placeholder": "<PERSON><PERSON> lòng chọn dung lượng"}}}, {"id": 21, "field": {"component": "Radio Button", "label": "<PERSON><PERSON><PERSON>", "type": "radio", "required": true, "configJson": {"size": "small", "options": ["Titan Tự <PERSON>", "<PERSON>", "Titan Trắng", "Titan Xanh", "Titan Vàng <PERSON>"], "variant": "outlined", "validation": {}}}}, {"field": {"component": "Checkbox", "configId": "warranty-options", "label": "<PERSON><PERSON><PERSON> ch<PERSON> b<PERSON><PERSON> h<PERSON>nh", "type": "checkbox", "required": false, "configJson": {"size": "small", "options": ["<PERSON><PERSON><PERSON> 12 tháng tiê<PERSON> ch<PERSON>n", "<PERSON><PERSON><PERSON> hành mở rộng 24 tháng", "<PERSON><PERSON><PERSON> h<PERSON> r<PERSON>i vỡ 12 tháng"], "variant": "outlined", "validation": {}}}}], "customFieldsToDelete": [23], "classifications": [{"id": 1, "type": "<PERSON><PERSON><PERSON>", "price": {"listPrice": 37000000, "salePrice": 34000000, "currency": "VND"}, "customFields": [{"customFieldId": 21, "value": {"value": "Titan Tự <PERSON>"}}]}, {"type": "<PERSON><PERSON> l<PERSON>", "price": {"listPrice": 42000000, "salePrice": 39000000, "currency": "VND"}, "customFields": [{"customFieldId": 18, "value": {"value": "1TB"}}]}]}