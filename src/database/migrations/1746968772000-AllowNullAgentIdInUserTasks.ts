import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để cho phép agent_id là null trong bảng user_tasks
 */
export class AllowNullAgentIdInUserTasks1746968772000 implements MigrationInterface {
  /**
   * Thực hiện migration
   * @param queryRunner QueryRunner
   */
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE user_tasks
      ALTER COLUMN agent_id DROP NOT NULL;
    `);
  }

  /**
   * Hoàn tác migration
   * @param queryRunner QueryRunner
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE user_tasks
      ALTER COLUMN agent_id SET NOT NULL;
    `);
  }
}
