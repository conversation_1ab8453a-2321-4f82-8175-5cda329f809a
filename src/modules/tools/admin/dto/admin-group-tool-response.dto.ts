import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AdminGroupTool } from '../../entities/admin-group-tool.entity';
import { AdminTool } from '../../entities/admin-tool.entity';
import { EmployeeInfoDto } from './employee-info.dto';

/**
 * DTO cho việc trả về thông tin nhóm tool của admin
 */
export class AdminGroupToolResponseDto {
  /**
   * ID duy nhất của nhóm tool
   * @example 1
   */
  @ApiProperty({
    description: 'ID duy nhất của nhóm tool',
    example: 1
  })
  id: number;

  /**
   * ID của loại agent
   * @example 1
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
    nullable: true
  })
  typeAgentId: number | null;

  /**
   * Tên của loại agent
   * @example "Content Writer"
   */
  @ApiProperty({
    description: 'Tên của loại agent',
    example: 'Content Writer',
    nullable: true
  })
  typeAgentName: string | null;

  /**
   * Tên của nhóm tool
   * @example "Nhóm công cụ SEO"
   */
  @ApiProperty({
    description: 'Tên của nhóm tool',
    example: 'Nhóm công cụ SEO'
  })
  name: string;

  /**
   * Mô tả chi tiết về nhóm tool
   * @example "Các công cụ hỗ trợ tối ưu hóa SEO cho website"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về nhóm tool',
    example: 'Các công cụ hỗ trợ tối ưu hóa SEO cho website',
    nullable: true
  })
  description: string | null;

  /**
   * Thông tin người tạo nhóm tool
   */
  @ApiProperty({
    description: 'Thông tin người tạo nhóm tool',
    type: EmployeeInfoDto,
    nullable: true
  })
  createdByInfo: EmployeeInfoDto | null;

  /**
   * Thông tin người cập nhật nhóm tool
   */
  @ApiProperty({
    description: 'Thông tin người cập nhật nhóm tool',
    type: EmployeeInfoDto,
    nullable: true
  })
  updatedByInfo: EmployeeInfoDto | null;

  /**
   * Thời điểm tạo nhóm tool, tính bằng millisecond
   * @example 1682841600000
   */
  @ApiProperty({
    description: 'Thời điểm tạo nhóm tool, tính bằng millisecond',
    example: 1682841600000
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối, tính bằng millisecond
   * @example 1682841600000
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật cuối, tính bằng millisecond',
    example: 1682841600000
  })
  updatedAt: number;

  /**
   * Danh sách các tool trong nhóm
   */
  @ApiProperty({
    description: 'Danh sách các tool trong nhóm',
    type: [Object],
    required: false
  })
  tools?: any[];

  /**
   * Chuyển đổi từ entity sang DTO
   * @param entity Entity AdminGroupTool
   * @returns DTO AdminGroupToolResponseDto
   */
  static fromEntity(entity: AdminGroupTool & {
    tools?: any[],
    typeAgentInfo?: {
      id: number | null,
      name: string | null
    },
    createdByInfo?: EmployeeInfoDto | null,
    updatedByInfo?: EmployeeInfoDto | null
  }): AdminGroupToolResponseDto {
    const dto = new AdminGroupToolResponseDto();

    // Gán tất cả các trường từ entity
    dto.id = entity.id;
    dto.name = entity.name;
    dto.description = entity.description;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;

    // Cập nhật typeAgentId và typeAgentName từ typeAgentInfo nếu có
    if (entity.typeAgentInfo) {
      dto.typeAgentId = entity.typeAgentInfo.id;
      dto.typeAgentName = entity.typeAgentInfo.name;
    } else {
      // Giá trị mặc định khi không có dữ liệu
      dto.typeAgentId = null;
      dto.typeAgentName = null;
    }

    // Thêm thông tin người tạo và người cập nhật nếu có
    if (entity.createdByInfo) {
      dto.createdByInfo = entity.createdByInfo;
    }

    if (entity.updatedByInfo) {
      dto.updatedByInfo = entity.updatedByInfo;
    }

    // Đảm bảo luôn có trường tools, nếu không có thì trả về mảng rỗng
    dto.tools = entity.tools || [];

    return dto;
  }
}
