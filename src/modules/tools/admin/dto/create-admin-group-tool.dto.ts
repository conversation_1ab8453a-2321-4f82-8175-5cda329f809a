import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

/**
 * DTO cho việc tạo mới nhóm tool của admin
 */
export class CreateAdminGroupToolDto {
  /**
   * ID của loại agent
   * @example 1
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'typeAgentId phải là một số' })
  typeAgentId?: number;

  /**
   * Danh sách ID của các tool cần thêm vào nhóm
   * @example ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]
   */
  @ApiProperty({
    description: 'Danh sách ID của các tool cần thêm vào nhóm',
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray({ message: 'toolIds phải là một mảng' })
  @IsUUID('4', { each: true, message: 'Mỗi toolId phải là một UUID hợp lệ' })
  toolIds?: string[];

  /**
   * Tên của nhóm tool
   * @example "Nhóm công cụ SEO"
   */
  @ApiProperty({
    description: 'Tên của nhóm tool',
    example: 'Nhóm công cụ SEO',
    required: true,
    maxLength: 100
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  /**
   * Mô tả chi tiết về nhóm tool
   * @example "Các công cụ hỗ trợ tối ưu hóa SEO cho website"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về nhóm tool',
    example: 'Các công cụ hỗ trợ tối ưu hóa SEO cho website',
    required: false
  })
  @IsString()
  @IsOptional()
  description?: string;
}
