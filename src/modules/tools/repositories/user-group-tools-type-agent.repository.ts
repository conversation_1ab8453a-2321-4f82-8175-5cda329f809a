import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { UserGroupToolsTypeAgent } from '../entities/user-group-tools-type-agent.entity';

@Injectable()
export class UserGroupToolsTypeAgentRepository extends Repository<UserGroupToolsTypeAgent> {
  private readonly logger = new Logger(UserGroupToolsTypeAgentRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserGroupToolsTypeAgent, dataSource.createEntityManager());
  }

  /**
   * Tìm tất cả các mapping theo group ID
   * @param groupId ID của nhóm công cụ
   * @returns Danh sách các mapping
   */
  async findByGroupId(groupId: number): Promise<UserGroupToolsTypeAgent[]> {
    return this.find({
      where: { groupId }
    });
  }

  /**
   * Tì<PERSON> tất cả các mapping theo type agent ID
   * @param typeAgentId ID của loại agent
   * @returns Danh sách các mapping
   */
  async findByTypeAgentId(typeAgentId: number): Promise<UserGroupToolsTypeAgent[]> {
    return this.find({
      where: { typeAgentId }
    });
  }

  /**
   * Tìm mapping theo group ID và type agent ID
   * @param groupId ID của nhóm công cụ
   * @param typeAgentId ID của loại agent
   * @returns Mapping nếu tìm thấy, null nếu không tìm thấy
   */
  async findByGroupIdAndTypeAgentId(groupId: number, typeAgentId: number): Promise<UserGroupToolsTypeAgent | null> {
    return this.findOne({
      where: { groupId, typeAgentId }
    });
  }

  /**
   * Xóa tất cả các mapping theo group ID
   * @param groupId ID của nhóm công cụ
   */
  async deleteByGroupId(groupId: number): Promise<void> {
    await this.delete({ groupId });
  }

  /**
   * Xóa tất cả các mapping theo type agent ID
   * @param typeAgentId ID của loại agent
   */
  async deleteByTypeAgentId(typeAgentId: number): Promise<void> {
    await this.delete({ typeAgentId });
  }
}
