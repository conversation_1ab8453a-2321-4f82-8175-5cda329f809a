import { Injectable, Logger } from '@nestjs/common';
import { In } from 'typeorm';
import { AppException } from '@common/exceptions';
import { UserGroupTool, UserGroupToolsTypeAgent, UserGroupToolMapping, UserGroupToolCustomMapping } from '../../entities';
import {
  UserGroupToolRepository,
  UserGroupToolsTypeAgentRepository,
  TypeAgentRepository,
  UserGroupToolMappingRepository,
  UserToolRepository,
  UserToolsCustomRepository,
  UserGroupToolCustomMappingRepository,
} from '../../repositories';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Transactional } from 'typeorm-transactional';
import {
  CreateUserGroupToolDto,
  QueryUserGroupToolDto,
  UpdateUserGroupToolDto,
  UserGroupToolDetailDto
} from '../dto';

@Injectable()
export class UserGroupToolService {
  private readonly logger = new Logger(UserGroupToolService.name);

  constructor(
    private readonly userGroupToolRepository: UserGroupToolRepository,
    private readonly userGroupToolsTypeAgentRepository: UserGroupToolsTypeAgentRepository,
    private readonly userGroupToolMappingRepository: UserGroupToolMappingRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly userToolRepository: UserToolRepository,
    private readonly userToolsCustomRepository: UserToolsCustomRepository,
    private readonly userGroupToolCustomMappingRepository: UserGroupToolCustomMappingRepository,
  ) {}

  /**
   * Tạo mới nhóm tool
   * @param userId ID của người dùng
   * @param dto Thông tin nhóm tool cần tạo
   * @returns Nhóm tool đã được tạo
   */
  @Transactional()
  async createGroupTool(
    userId: number,
    dto: CreateUserGroupToolDto,
  ): Promise<UserGroupTool> {
    try {
      // Kiểm tra xem loại agent có tồn tại không (nếu có)
      if (dto.typeAgentId) {
        const typeAgent = await this.typeAgentRepository.findTypeAgentById(dto.typeAgentId);
        if (!typeAgent) {
          throw new AppException(TOOLS_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
        }
      }

      // Kiểm tra xem tên nhóm tool đã tồn tại chưa
      const existingGroup = await this.userGroupToolRepository.findGroupByName(dto.name, userId);
      if (existingGroup) {
        throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NAME_EXISTS);
      }

      // Kiểm tra toolIds nếu có
      if (dto.toolIds && dto.toolIds.length > 0) {
        // Kiểm tra xem có ID trùng nhau không
        const uniqueToolIds = [...new Set(dto.toolIds)];
        if (uniqueToolIds.length !== dto.toolIds.length) {
          throw new AppException(TOOLS_ERROR_CODES.INVALID_TOOL_IDS, 'Danh sách toolIds không được chứa ID trùng nhau');
        }

        // Phân loại toolIds thành hai nhóm: thông thường và tùy chỉnh
        // Giả định: UUID của công cụ thông thường và tùy chỉnh có thể phân biệt được
        // Trong thực tế, cần có cách phân biệt rõ ràng hơn
        const regularToolIds = [...dto.toolIds];
        const customToolIds = [...dto.toolIds];

        // Kiểm tra xem các công cụ thông thường có tồn tại không
        const regularValidationResult = await this.userToolRepository.validateToolIds(regularToolIds, userId);

        // Kiểm tra xem các công cụ tùy chỉnh có tồn tại không
        const customValidationResult = await this.userToolsCustomRepository.validateToolIds(customToolIds, userId);

        // Tổng hợp kết quả kiểm tra
        const existingIds = [
          ...regularValidationResult.existingIds,
          ...customValidationResult.existingIds
        ];

        // Lọc ra các ID không tồn tại trong cả hai loại
        const nonExistingIds = dto.toolIds.filter(id => !existingIds.includes(id));

        if (nonExistingIds.length > 0) {
          throw new AppException(
            TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
            `Không tìm thấy các công cụ với ID: ${nonExistingIds.join(', ')}`
          );
        }
      }

      // Tạo mới nhóm tool
      const groupTool = new UserGroupTool();
      groupTool.name = dto.name;
      groupTool.description = dto.description || null;
      groupTool.userId = userId;

      // Lưu nhóm tool
      const savedGroupTool = await this.userGroupToolRepository.save(groupTool);

      // Tạo liên kết với loại agent (nếu có)
      if (dto.typeAgentId) {
        const groupToolTypeAgent = new UserGroupToolsTypeAgent();
        groupToolTypeAgent.groupId = savedGroupTool.id;
        groupToolTypeAgent.typeAgentId = dto.typeAgentId;
        await this.userGroupToolsTypeAgentRepository.save(groupToolTypeAgent);
      }

      // Tạo liên kết với các tool (nếu có)
      if (dto.toolIds && dto.toolIds.length > 0) {
        // Phân loại toolIds thành hai nhóm: thông thường và tùy chỉnh
        const regularToolIds = [...dto.toolIds];
        const customToolIds = [...dto.toolIds];

        // Kiểm tra xem các công cụ thông thường có tồn tại không
        const regularValidationResult = await this.userToolRepository.validateToolIds(regularToolIds, userId);

        // Kiểm tra xem các công cụ tùy chỉnh có tồn tại không
        const customValidationResult = await this.userToolsCustomRepository.validateToolIds(customToolIds, userId);

        // Tạo liên kết với các công cụ thông thường
        if (regularValidationResult.existingIds.length > 0) {
          const regularMappings = regularValidationResult.existingIds.map(toolId => {
            const mapping = new UserGroupToolMapping();
            mapping.groupId = savedGroupTool.id;
            mapping.toolId = toolId;
            return mapping;
          });
          await this.userGroupToolMappingRepository.save(regularMappings);
        }

        // Tạo liên kết với các công cụ tùy chỉnh
        if (customValidationResult.existingIds.length > 0) {
          const customMappings = customValidationResult.existingIds.map(toolId => {
            const mapping = new UserGroupToolCustomMapping();
            mapping.groupId = savedGroupTool.id;
            mapping.toolId = toolId;
            return mapping;
          });
          await this.userGroupToolCustomMappingRepository.save(customMappings);
        }
      }

      return savedGroupTool;
    } catch (error) {
      this.logger.error(`Failed to create group tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_CREATE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật thông tin nhóm tool
   * @param id ID của nhóm tool cần cập nhật
   * @param userId ID của người dùng
   * @param dto Thông tin cần cập nhật
   * @returns Nhóm tool đã được cập nhật
   */
  @Transactional()
  async updateGroupTool(
    id: number,
    userId: number,
    dto: UpdateUserGroupToolDto,
  ): Promise<UserGroupTool> {
    try {
      // Kiểm tra xem nhóm tool có tồn tại không
      const groupTool = await this.userGroupToolRepository.findGroupById(id, userId);
      if (!groupTool) {
        throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NOT_FOUND);
      }

      // Kiểm tra xem loại agent có tồn tại không (nếu có cập nhật)
      if (dto.typeAgentId) {
        const typeAgent = await this.typeAgentRepository.findTypeAgentById(dto.typeAgentId);
        if (!typeAgent) {
          throw new AppException(TOOLS_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
        }

        // Cập nhật liên kết với loại agent
        const existingMapping = await this.userGroupToolsTypeAgentRepository.findByGroupIdAndTypeAgentId(id, dto.typeAgentId);
        if (!existingMapping) {
          // Xóa các liên kết cũ
          await this.userGroupToolsTypeAgentRepository.deleteByGroupId(id);

          // Tạo liên kết mới
          const groupToolTypeAgent = new UserGroupToolsTypeAgent();
          groupToolTypeAgent.groupId = id;
          groupToolTypeAgent.typeAgentId = dto.typeAgentId;
          await this.userGroupToolsTypeAgentRepository.save(groupToolTypeAgent);
        }
      }

      // Kiểm tra xem tên nhóm tool đã tồn tại chưa (nếu có cập nhật)
      if (dto.name && dto.name !== groupTool.name) {
        const existingGroup = await this.userGroupToolRepository.findGroupByName(dto.name, userId);
        if (existingGroup && existingGroup.id !== id) {
          throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NAME_EXISTS);
        }
        groupTool.name = dto.name;
      }

      // Cập nhật các trường khác
      if (dto.description !== undefined) {
        groupTool.description = dto.description;
      }

      groupTool.updatedAt = Date.now();

      return this.userGroupToolRepository.save(groupTool);
    } catch (error) {
      this.logger.error(`Failed to update group tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Xóa nhóm tool
   * @param id ID của nhóm tool cần xóa
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async deleteGroupTool(id: number, userId: number): Promise<boolean> {
    try {
      // Kiểm tra xem nhóm tool có tồn tại không
      const groupTool = await this.userGroupToolRepository.findGroupById(id, userId);
      if (!groupTool) {
        throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NOT_FOUND);
      }

      // Xóa các liên kết với loại agent
      await this.userGroupToolsTypeAgentRepository.deleteByGroupId(id);

      // Xóa các liên kết với tool
      await this.userGroupToolMappingRepository.deleteByGroupId(id);
      await this.userGroupToolCustomMappingRepository.deleteByGroupId(id);

      // Xóa nhóm tool
      await this.userGroupToolRepository.remove(groupTool);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete group tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_DELETE_FAILED, error.message);
    }
  }

  /**
   * Lấy thông tin chi tiết của nhóm tool
   * @param id ID của nhóm tool cần lấy thông tin
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của nhóm tool
   */
  async getGroupToolById(id: number, userId: number): Promise<UserGroupToolDetailDto> {
    try {
      const groupTool = await this.userGroupToolRepository.findGroupById(id, userId);
      if (!groupTool) {
        throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NOT_FOUND);
      }

      // Lấy thông tin loại agent
      const typeAgentMappings = await this.userGroupToolsTypeAgentRepository.findByGroupId(id);
      const typeAgentIds = typeAgentMappings.map(mapping => mapping.typeAgentId);

      // Lấy thông tin tool thông thường
      const toolMappings = await this.userGroupToolMappingRepository.findByGroupId(id);
      const regularToolIds = toolMappings.map(mapping => mapping.toolId);

      // Lấy thông tin tool tùy chỉnh
      const customToolMappings = await this.userGroupToolCustomMappingRepository.findByGroupId(id);
      const customToolIds = customToolMappings.map(mapping => mapping.toolId);

      // Kết hợp cả hai loại tool
      const toolIds = [...regularToolIds, ...customToolIds];

      const detailDto = new UserGroupToolDetailDto();
      detailDto.id = groupTool.id;
      detailDto.name = groupTool.name;
      detailDto.description = groupTool.description;
      detailDto.createdAt = groupTool.createdAt;
      detailDto.updatedAt = groupTool.updatedAt;
      detailDto.typeAgentIds = typeAgentIds;
      detailDto.regularToolIds = regularToolIds;
      detailDto.customToolIds = customToolIds;
      detailDto.toolIds = toolIds;

      return detailDto;
    } catch (error) {
      this.logger.error(`Failed to get group tool by ID: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NOT_FOUND, error.message);
    }
  }

  /**
   * Lấy danh sách nhóm tool với phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách nhóm tool với phân trang
   */
  async getGroupTools(
    userId: number,
    queryDto: QueryUserGroupToolDto,
  ): Promise<PaginatedResult<UserGroupTool>> {
    try {
      return this.userGroupToolRepository.findGroups(
        queryDto.page,
        queryDto.limit,
        userId,
        queryDto.search,
        queryDto.sortBy,
        queryDto.sortDirection,
      );
    } catch (error) {
      this.logger.error(`Failed to get group tools: ${error.message}`, error.stack);
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_FETCH_FAILED, error.message);
    }
  }

  /**
   * Cập nhật danh sách tool của nhóm tool
   * @param id ID của nhóm tool cần cập nhật
   * @param userId ID của người dùng
   * @param toolIds Danh sách ID của các tool cần thêm vào nhóm
   * @returns true nếu cập nhật thành công
   */
  @Transactional()
  async updateGroupToolTools(
    id: number,
    userId: number,
    toolIds: string[],
  ): Promise<boolean> {
    try {
      // Kiểm tra xem nhóm tool có tồn tại không
      const groupTool = await this.userGroupToolRepository.findGroupById(id, userId);
      if (!groupTool) {
        throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NOT_FOUND);
      }

      // Kiểm tra toolIds
      if (toolIds && toolIds.length > 0) {
        // Kiểm tra xem có ID trùng nhau không
        const uniqueToolIds = [...new Set(toolIds)];
        if (uniqueToolIds.length !== toolIds.length) {
          throw new AppException(TOOLS_ERROR_CODES.INVALID_TOOL_IDS, 'Danh sách toolIds không được chứa ID trùng nhau');
        }

        // Phân loại toolIds thành hai nhóm: thông thường và tùy chỉnh
        const regularToolIds = [...toolIds];
        const customToolIds = [...toolIds];

        // Kiểm tra xem các công cụ thông thường có tồn tại không
        const regularValidationResult = await this.userToolRepository.validateToolIds(regularToolIds, userId);

        // Kiểm tra xem các công cụ tùy chỉnh có tồn tại không
        const customValidationResult = await this.userToolsCustomRepository.validateToolIds(customToolIds, userId);

        // Tổng hợp kết quả kiểm tra
        const existingIds = [
          ...regularValidationResult.existingIds,
          ...customValidationResult.existingIds
        ];

        // Lọc ra các ID không tồn tại trong cả hai loại
        const nonExistingIds = toolIds.filter(id => !existingIds.includes(id));

        if (nonExistingIds.length > 0) {
          throw new AppException(
            TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
            `Không tìm thấy các công cụ với ID: ${nonExistingIds.join(', ')}`
          );
        }

        // Xóa các liên kết cũ
        await this.userGroupToolMappingRepository.deleteByGroupId(id);
        await this.userGroupToolCustomMappingRepository.deleteByGroupId(id);

        // Tạo liên kết mới cho công cụ thông thường
        if (regularValidationResult.existingIds.length > 0) {
          const regularMappings = regularValidationResult.existingIds.map(toolId => {
            const mapping = new UserGroupToolMapping();
            mapping.groupId = id;
            mapping.toolId = toolId;
            return mapping;
          });
          await this.userGroupToolMappingRepository.save(regularMappings);
        }

        // Tạo liên kết mới cho công cụ tùy chỉnh
        if (customValidationResult.existingIds.length > 0) {
          const customMappings = customValidationResult.existingIds.map(toolId => {
            const mapping = new UserGroupToolCustomMapping();
            mapping.groupId = id;
            mapping.toolId = toolId;
            return mapping;
          });
          await this.userGroupToolCustomMappingRepository.save(customMappings);
        }
      } else {
        // Nếu không có toolIds, xóa tất cả các liên kết
        await this.userGroupToolMappingRepository.deleteByGroupId(id);
        await this.userGroupToolCustomMappingRepository.deleteByGroupId(id);
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to update group tool tools: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Lấy danh sách nhóm tool theo loại agent
   * @param typeAgentId ID của loại agent
   * @param userId ID của người dùng
   * @returns Danh sách nhóm tool
   */
  async getGroupToolsByTypeAgentId(typeAgentId: number, userId: number): Promise<UserGroupTool[]> {
    try {
      // Kiểm tra xem loại agent có tồn tại không
      const typeAgent = await this.typeAgentRepository.findTypeAgentById(typeAgentId);
      if (!typeAgent) {
        throw new AppException(TOOLS_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Lấy danh sách mapping
      const mappings = await this.userGroupToolsTypeAgentRepository.findByTypeAgentId(typeAgentId);

      // Lọc các nhóm tool thuộc về người dùng
      const groupTools: UserGroupTool[] = [];

      // Lấy danh sách groupId từ mappings
      const groupIds = mappings.map(mapping => mapping.groupId);

      // Nếu có groupIds, tìm các group tool thuộc về người dùng
      if (groupIds.length > 0) {
        const userGroupTools = await this.userGroupToolRepository.find({
          where: {
            id: In(groupIds),
            userId: userId
          }
        });

        groupTools.push(...userGroupTools);
      }

      return groupTools;
    } catch (error) {
      this.logger.error(`Failed to get group tools by type agent ID: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_FETCH_FAILED, error.message);
    }
  }
}
