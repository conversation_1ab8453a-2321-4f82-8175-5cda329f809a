import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@common/exceptions';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import { UserToolService } from '../services';
import {
  EditToolVersionDto,
  QueryUserToolDto,
  RollbackToAdminVersionDto,
  ToggleToolActiveDto,
  UpdateFromAdminDto,
  UserToolDetailDto,
} from '../dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ToolListItemDto } from '@/modules/tools/admin/dto';

@ApiTags(SWAGGER_API_TAGS.USER_TOOL)
@Controller('user/tools')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserToolController {
  constructor(private readonly userToolService: UserToolService) {}

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tool của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tool của người dùng',
    type: () => ApiResponseDto<PaginatedResult<ToolListItemDto>>,
  })
  @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  async getUserTools(
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryUserToolDto,
  ) {
    const result = await this.userToolService.getUserTools(userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết tool của người dùng' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết tool của người dùng',
    type: () => ApiResponseDto.success(UserToolDetailDto),
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED
  )
  async getUserToolById(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ) {
    const tool = await this.userToolService.getUserToolById(id, userId);
    return ApiResponseDto.success(tool);
  }

  @Post('update-from-admin')
  @ApiOperation({ summary: 'Cập nhật tool từ phiên bản mới của admin' })
  @ApiResponse({
    status: 200,
    description: 'Tool đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED
  )
  async updateFromAdmin(
    @CurrentUser('id') userId: number,
    @Body() updateDto: UpdateFromAdminDto,
  ) {
    const toolId = await this.userToolService.updateFromAdmin(
      userId,
      updateDto,
    );
    return ApiResponseDto.success({ id: toolId });
  }

  @Post(':id/versions/:versionId/edit')
  @ApiOperation({ summary: 'Chỉnh sửa phiên bản tool' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiParam({ name: 'versionId', description: 'ID của phiên bản' })
  @ApiResponse({
    status: 200,
    description: 'Phiên bản đã được chỉnh sửa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED,
    TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID
  )
  async editToolVersion(
    @Param('id') toolId: string,
    @Param('versionId') versionId: string,
    @CurrentUser('id') userId: number,
    @Body() editDto: EditToolVersionDto,
  ) {
    const newVersionId = await this.userToolService.editToolVersion(
      userId,
      toolId,
      versionId,
      editDto,
    );
    return ApiResponseDto.success({ id: newVersionId });
  }

  @Post('rollback-to-admin')
  @ApiOperation({ summary: 'Khôi phục về phiên bản gốc từ admin' })
  @ApiResponse({
    status: 200,
    description: 'Tool đã được khôi phục thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED
  )
  async rollbackToAdminVersion(
    @CurrentUser('id') userId: number,
    @Body() rollbackDto: RollbackToAdminVersionDto,
  ) {
    const toolId = await this.userToolService.rollbackToAdminVersion(
      userId,
      rollbackDto,
    );
    return ApiResponseDto.success({ id: toolId });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tool (xóa mềm)' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiResponse({
    status: 200,
    description: 'Tool đã được xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED
  )
  async deleteTool(@Param('id') id: string, @CurrentUser('id') userId: number) {
    await this.userToolService.deleteTool(id, userId);
    return ApiResponseDto.success({ message: 'Tool đã được xóa thành công' });
  }

  @Put(':id/active')
  @ApiOperation({ summary: 'Bật/tắt trạng thái active của tool (đảo ngược trạng thái hiện tại)' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiResponse({
    status: 200,
    description: 'Trạng thái active đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED
  )
  async toggleToolActive(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ) {
    await this.userToolService.toggleToolActive(id, userId);
    return ApiResponseDto.success({ message: 'Trạng thái active đã được cập nhật thành công' });
  }
}
