import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { HttpMethodEnum } from '@/modules/tools/constants';

/**
 * Enum định nghĩa các trường sắp xếp cho công cụ tùy chỉnh
 */
export enum UserToolsCustomSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  TOOL_NAME = 'toolName',
  METHOD = 'method',
  BASE_URL = 'baseUrl',
}

/**
 * DTO cho các tham số truy vấn công cụ tùy chỉnh
 */
export class QueryUserToolsCustomDto extends QueryDto {

  @ApiProperty({
    description: 'Lọc theo phương thức HTTP',
    enum: HttpMethodEnum,
    example: HttpMethodEnum.GET,
    required: false,
  })
  @IsOptional()
  @IsEnum(HttpMethodEnum)
  method?: HttpMethodEnum;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: UserToolsCustomSortBy,
    example: UserToolsCustomSortBy.CREATED_AT,
    default: UserToolsCustomSortBy.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserToolsCustomSortBy)
  sortBy?: UserToolsCustomSortBy = UserToolsCustomSortBy.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
