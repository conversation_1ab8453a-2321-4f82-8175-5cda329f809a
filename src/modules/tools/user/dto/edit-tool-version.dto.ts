import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsOptional, IsString, Matches, MaxLength } from 'class-validator';

/**
 * DTO cho việc chỉnh sửa phiên bản tool của người dùng
 */
export class EditToolVersionDto {
  @ApiProperty({
    description: 'Tên tool trong định nghĩa code (chỉ chứa a-z, A-Z, 0-9, _)',
    example: 'searchToolCustom',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Tên tool chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới',
  })
  @MaxLength(64)
  toolName: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về chức năng của tool',
    example: 'Tìm kiếm thông tin từ nhiều nguồn dữ liệu với bộ lọc tùy chỉnh',
    required: false,
  })
  @IsString()
  @IsOptional()
  toolDescription?: string;

  @ApiProperty({
    description: 'Tham số của tool',
    example: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Câu truy vấn tìm kiếm',
        },
        customFilters: {
          type: 'object',
          description: 'Bộ lọc tùy chỉnh',
        },
      },
      required: ['query'],
    },
  })
  @IsObject()
  @IsNotEmpty()
  parameters: Record<string, any>;

  @ApiProperty({
    description: 'Mô tả những thay đổi so với phiên bản trước',
    example: 'Thêm bộ lọc tùy chỉnh',
    required: false,
  })
  @IsString()
  @IsOptional()
  changeDescription?: string;
}
