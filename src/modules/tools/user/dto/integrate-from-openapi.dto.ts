import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsObject, IsOptional, ValidateNested } from 'class-validator';
import { ApiKeyAuthDto, AuthConfigDto, AuthTypeEnum, NoAuthDto, OAuthAuthDto } from './auth-config.dto';
import { IsOpenApiSpec } from '../validators/openapi-spec.validator';

/**
 * DTO cho việc tích hợp từ OpenAPI - định dạng đầy đủ
 */
export class IntegrateFromOpenApiDto {
  /**
   * Đặc tả OpenAPI
   */
  @ApiProperty({
    description: 'Đặc tả OpenAPI',
    example: {
      openapi: '3.0.0',
      paths: {
        '/users': {
          get: { summary: 'L<PERSON>y danh sách người dùng' },
          post: { summary: 'Tạo người dùng mới' }
        }
      }
    }
  })
  @IsNotEmpty({ message: 'Đặc tả OpenAPI không được để trống' })
  @IsObject({ message: 'Đặc tả OpenAPI phải là một đối tượng hợp lệ' })
  @IsOpenApiSpec({ message: 'Đặc tả OpenAPI không hợp lệ. Phải là đặc tả OpenAPI 3.x.x với ít nhất một path và một phương thức HTTP' })
  openapiSpec: Record<string, any>;

  /**
   * Base URL cho API
   */
  @ApiProperty({
    description: 'Base URL cho API (nếu không cung cấp, sẽ sử dụng từ đặc tả OpenAPI hoặc giá trị mặc định)',
    example: 'https://api.example.com',
    required: false
  })
  @IsOptional()
  baseUrl?: string;

  /**
   * Cấu hình xác thực
   */
  @ApiProperty({
    description: 'Cấu hình xác thực',
    oneOf: [
      { $ref: '#/components/schemas/ApiKeyAuthDto' },
      { $ref: '#/components/schemas/OAuthAuthDto' },
      { $ref: '#/components/schemas/NoAuthDto' }
    ],
    example: {
      authType: AuthTypeEnum.API_KEY,
      schemeName: 'ApiKeyAuth',
      apiKey: 'api_key_123456',
      apiKeyLocation: 'header',
      paramName: 'X-API-KEY'
    },
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AuthConfigDto, {
    discriminator: {
      property: 'authType',
      subTypes: [
        { value: ApiKeyAuthDto, name: AuthTypeEnum.API_KEY },
        { value: OAuthAuthDto, name: AuthTypeEnum.OAUTH },
        { value: NoAuthDto, name: AuthTypeEnum.NONE }
      ]
    }
  })
  authConfig?: ApiKeyAuthDto | OAuthAuthDto | NoAuthDto;
}

/**
 * DTO cho việc tích hợp trực tiếp từ đặc tả OpenAPI
 */
export class DirectOpenApiInputDto {
  /**
   * Phiên bản OpenAPI
   */
  @ApiProperty({
    description: 'Phiên bản OpenAPI',
    example: '3.0.0'
  })
  @IsNotEmpty({ message: 'Phiên bản OpenAPI không được để trống' })
  openapi: string;

  /**
   * Đường dẫn API
   */
  @ApiProperty({
    description: 'Đường dẫn API',
    example: {
      '/users': {
        get: {
          summary: 'Lấy danh sách người dùng',
          responses: {
            '200': {
              description: 'Thành công'
            }
          }
        }
      }
    }
  })
  @IsNotEmpty({ message: 'Đường dẫn API không được để trống' })
  @IsObject({ message: 'Đường dẫn API phải là một đối tượng hợp lệ' })
  paths: Record<string, any>;

  /**
   * Base URL tùy chọn
   */
  @ApiProperty({
    description: 'Base URL cho API (tùy chọn)',
    example: 'https://api.example.com',
    required: false
  })
  @IsOptional()
  baseUrl?: string;
}
