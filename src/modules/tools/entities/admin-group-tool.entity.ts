import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_group_tools trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin về các nhóm tool do admin tạo
 */
@Entity('admin_group_tools')
export class AdminGroupTool {
  /**
   * ID duy nhất của nhóm tool
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Tên của nhóm tool
   */
  @Column({ name: 'name', length: 100 })
  name: string;

  /**
   * <PERSON>ô tả chi tiết về nhóm tool
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * ID của nhân viên tạo nhóm tool
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * ID của nhân viên cập nhật nhóm tool gần nhất
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * Thời điểm tạo nhóm tool, tính bằng millisecond
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối, tính bằng millisecond
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;
}
