import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { ToolStatusEnum } from '../constants/tool-status.enum';
import { UserTool } from './user-tool.entity';
import { MethodEnum } from '../constants/method.enum';

/**
 * Entity đại diện cho bảng user_tool_versions trong cơ sở dữ liệu
 * Bảng lưu trữ các phiên bản chỉnh sửa của người dùng, chỉ tạo khi người dùng chỉnh sửa tool công khai
 */
@Entity('user_tool_versions')
@Unique('unique_user_function_version', ['userId', 'originalFunctionId', 'versionNumber'])
export class UserToolVersion {
  /**
   * ID duy nhất của phiên bản, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * ID của người dùng chỉnh sửa, tham chiếu đến bảng users
   */
  @Column({ name: 'user_id', type: 'integer' })
  userId: number;

  /**
   * ID của tool gốc, tham chiếu đến bảng user_tools
   */
  @Column({ name: 'original_function_id', type: 'uuid' })
  originalFunctionId: string;

  /**
   * Mối quan hệ với tool gốc
   */
  @ManyToOne(() => UserTool)
  @JoinColumn({ name: 'original_function_id' })
  originalFunction: UserTool;

  /**
   * Số thứ tự phiên bản của người dùng cho tool này, tăng dần
   */
  @Column({ name: 'version_number' })
  versionNumber: number;

  /**
   * Mô tả thay đổi so với phiên bản gốc hoặc phiên bản trước
   */
  @Column({ name: 'change_description', type: 'text', nullable: true })
  changeDescription: string | null;

  /**
   * Thời điểm tạo phiên bản, tính bằng millisecond
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối, tính bằng millisecond
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Trạng thái của phiên bản: DRAFT, APPROVED, DEPRECATED
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ToolStatusEnum,
    default: ToolStatusEnum.APPROVED
  })
  status: ToolStatusEnum;

  /**
   * Tên của hàm trong định nghĩa code
   */
  @Column({ name: 'tool_name', length: 64 })
  toolName: string;

  /**
   * Mô tả chi tiết về chức năng của hàm
   */
  @Column({ name: 'tool_description', type: 'text', nullable: true })
  toolDescription: string | null;

  /**
   * Tham số của hàm, định dạng JSONB
   */
  @Column({ name: 'parameters', type: 'jsonb', default: '{}' })
  parameters: any;

  /**
   * Trạng thái sửa
   */
  @Column({ name: 'edited', default: false })
  edited: boolean;

  /**
   * Endpoint của tool
   */
  @Column({ name: 'endpoint', nullable: true, type: 'varchar', length: 255 })
  endpoint: string | null;

  /**
   * Phương thức HTTP
   */
  @Column({ name: 'method', type: 'enum', enum: MethodEnum, nullable: true })
  method: MethodEnum;
}
