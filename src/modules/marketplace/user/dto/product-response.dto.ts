import { ApiProperty } from '@nestjs/swagger';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';

/**
 * DTO cho thông tin người bán trong response sản phẩm
 */
export class SellerInfoDto {
  @ApiProperty({
    description: 'ID của người bán',
    example: 123,
    nullable: true,
  })
  id?: number;

  @ApiProperty({
    description: 'Tên người bán',
    example: 'Nguyễn Văn A',
  })
  name: string;

  @ApiProperty({
    description: 'Avatar của người bán',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string | null;

  @ApiProperty({
    description: 'Email của người bán',
    example: '<EMAIL>',
    nullable: true,
  })
  email?: string | null;

  @ApiProperty({
    description: 'Số điện thoại của người bán',
    example: '0987654321',
    nullable: true,
  })
  phoneNumber?: string | null;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> b<PERSON> (user hoặc employee)',
    example: 'user',
    enum: ['user', 'employee'],
  })
  type: 'user' | 'employee';
}

/**
 * DTO cho response trả về thông tin sản phẩm trong danh sách
 */
export class ProductResponseDto {
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'AI Chatbot Template',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'A ready-to-use chatbot template for customer service',
  })
  description: string;

  @ApiProperty({
    description: 'Giá niêm yết',
    example: 1200,
  })
  listedPrice: number;

  @ApiProperty({
    description: 'Giá sau giảm',
    example: 1000,
  })
  discountedPrice: number;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductCategory,
    example: ProductCategory.AGENT,
  })
  category: ProductCategory;

  @ApiProperty({
    description: 'Danh sách ảnh sản phẩm',
    type: 'array',
    example: [
      {
        key: 'marketplace/IMAGE/2025/05/product-image-0-1746348513323-1746348513323-b9e0b3d7-b5c4-4000-b184-12248ed75794',
        position: 0,
        url: 'https://cdn.redai.vn/marketplace/IMAGE/2025/05/product-image-0-1746348513323-1746348513323-b9e0b3d7-b5c4-4000-b184-12248ed75794'
      }
    ],
  })
  images: Array<{ key: string; position: number; url: string }>;

  @ApiProperty({
    description: 'Thông tin người bán',
    type: SellerInfoDto,
  })
  seller: SellerInfoDto;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1625097600000,
  })
  createdAt: number;
}

/**
 * DTO cho response trả về thông tin sản phẩm của người dùng
 * Mở rộng từ ProductResponseDto và thêm trạng thái
 */
export class UserProductResponseDto extends ProductResponseDto {
  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: ProductStatus,
    example: ProductStatus.APPROVED,
  })
  status: ProductStatus;

  @ApiProperty({
    description: 'Hướng dẫn sử dụng',
    example: 'https://cdn.redai.vn/marketplace/DOCUMENT/2025/05/user-manual-1746348513323-1746348513323-b9e0b3d7-b5c4-4000-b184-12248ed75794',
    nullable: true,
  })
  userManual: string | null;

  @ApiProperty({
    description: 'Thông tin chi tiết',
    example: 'https://cdn.redai.vn/marketplace/DOCUMENT/2025/05/detail-1746348513323-1746348513323-b9e0b3d7-b5c4-4000-b184-12248ed75794',
    nullable: true,
  })
  detail: string | null;
}
