import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiCreatedResponse, ApiExtraModels, ApiOkResponse, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { SwaggerApiTag } from '@common/swagger/swagger.tags';
import { ProductUserService } from '../services/product-user.service';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CreateProductDto, CreateProductResponseDto, QueryUserProductDto, UpdateProductDto, UpdateProductOption, UpdateProductResponseDto } from '../dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { CurrentUser } from '@modules/auth/decorators';
import {
  ProductDetailResponseDto,
  UserProductDetailResponseDto,
  UserProductResponseDto,
} from '@modules/marketplace/user/dto';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';

@ApiTags(SwaggerApiTag.USER_MARKETPLACE_PRODUCTS)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@ApiExtraModels(ApiResponseDto, UserProductResponseDto, ProductDetailResponseDto, UserProductDetailResponseDto, CreateProductResponseDto, UpdateProductResponseDto)
@Controller('user/marketplace/products')
export class ProductUserController {
  constructor(private readonly productUserService: ProductUserService) {}

  /**
   * Lấy danh sách sản phẩm của người dùng hiện tại
   * @param userId ID của người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @returns Danh sách sản phẩm phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách sản phẩm của người dùng hiện tại' })
  @ApiOkResponse({
    description: 'Danh sách sản phẩm phân trang',
    schema: ApiResponseDto.getPaginatedSchema(UserProductResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND
  )
  async getUserProducts(
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryUserProductDto,
  ) {
    const result = await this.productUserService.getUserProducts(userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy danh sách sản phẩm được phê duyệt, không thuộc về người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @param currentUserId ID của người dùng hiện tại
   * @returns Danh sách sản phẩm được phê duyệt với phân trang
   */
  @Get('approved')
  @ApiOperation({ summary: 'Lấy danh sách sản phẩm được phê duyệt, không thuộc về người dùng hiện tại' })
  @ApiOkResponse({
    description: 'Danh sách sản phẩm được phê duyệt, không thuộc về người dùng hiện tại',
    schema: ApiResponseDto.getPaginatedSchema(UserProductResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR
  )
  async getApprovedProducts(
    @Query() queryDto: QueryUserProductDto,
    @CurrentUser('id') currentUserId: number,
  ) {
    const result = await this.productUserService.getApprovedProducts(queryDto, currentUserId);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy thông tin chi tiết sản phẩm theo ID
   * @param productId ID của sản phẩm
   * @param currentUserId ID của người dùng hiện tại
   * @returns Thông tin chi tiết sản phẩm
   */
  @Get('detail/:id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết sản phẩm theo ID' })
  @ApiParam({ name: 'id', description: 'ID của sản phẩm', type: Number })
  @ApiOkResponse({
    description: 'Thông tin chi tiết sản phẩm',
    schema: ApiResponseDto.getSchema(ProductDetailResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.CANNOT_BUY_OWN_PRODUCT
  )
  async getProductDetail(
    @Param('id', ParseIntPipe) productId: number,
    @CurrentUser('id') currentUserId: number,
  ) {
    const result = await this.productUserService.getProductDetail(productId, currentUserId);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy thông tin chi tiết sản phẩm của người dùng hiện tại theo ID
   * @param userId ID của người dùng hiện tại
   * @param productId ID của sản phẩm
   * @returns Thông tin chi tiết sản phẩm
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết sản phẩm của người dùng hiện tại theo ID' })
  @ApiParam({ name: 'id', description: 'ID của sản phẩm', type: Number })
  @ApiOkResponse({
    description: 'Thông tin chi tiết sản phẩm',
    schema: ApiResponseDto.getSchema(UserProductDetailResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.UNAUTHORIZED
  )
  async getUserProductById(
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) productId: number,
  ) {
    const result = await this.productUserService.getUserProductById(userId, productId);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo sản phẩm mới
   * @param userId ID của người dùng hiện tại
   * @param createProductDto Dữ liệu tạo sản phẩm
   * @returns Sản phẩm đã tạo cùng với các URL để upload tài liệu
   */
  @Post()
  @ApiOperation({summary: 'Tạo sản phẩm mới',})
  @ApiCreatedResponse({
    description: 'Sản phẩm đã được tạo thành công',
    schema: ApiResponseDto.getSchema(CreateProductResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_CREATION_FAILED
  )
  async createProduct(
    @CurrentUser('id') userId: number,
    @Body() createProductDto: CreateProductDto,
  ) {
    const result = await this.productUserService.createProduct(userId, createProductDto);
    return ApiResponseDto.created(result);
  }

  /**
   * Gửi duyệt sản phẩm (chuyển trạng thái từ DRAFT sang PENDING)
   * @param userId ID của người dùng hiện tại
   * @param productId ID của sản phẩm
   * @returns Sản phẩm đã được cập nhật
   */
  @Post(':id/pending')
  @ApiOperation({ summary: 'Gửi duyệt sản phẩm' })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm',
    type: Number,
    example: 1,
  })
  @ApiOkResponse({
    description: 'Sản phẩm đã được gửi duyệt thành công',
    schema: ApiResponseDto.getSchema(UserProductDetailResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_STATUS_CHANGE_FAILED,
    MARKETPLACE_ERROR_CODES.UNAUTHORIZED
  )
  async submitProductForApproval(
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) productId: number,
  ) {
    const result = await this.productUserService.submitProductForApproval(userId, productId);
    return ApiResponseDto.success(result, 'Sản phẩm đã được gửi duyệt thành công');
  }

  /**
   * Cập nhật thông tin sản phẩm với hai tùy chọn: lưu nháp hoặc gửi duyệt
   * @param userId ID của người dùng hiện tại
   * @param productId ID của sản phẩm
   * @param updateProductDto Dữ liệu cập nhật sản phẩm, bao gồm tùy chọn lưu nháp hoặc gửi duyệt
   * @returns Sản phẩm đã cập nhật và các URL để upload tài liệu mới
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin sản phẩm (lưu nháp hoặc gửi duyệt)' })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm',
    type: Number,
    example: 1,
  })
  @ApiBody({
    type: UpdateProductDto,
    description: 'Dữ liệu cập nhật sản phẩm',
    examples: {
      saveDraft: {
        summary: 'Lưu nháp',
        value: {
          productInfo: {
            name: "product abc",
            listedPrice: 123,
            discountedPrice: 122,
            description: "product description"
          },
          images: [
            {
              operation: "DELETE",
              key: "marketplace/KNOWLEDGEFILE/2025/4/1/12-wqe.png"
            },
            {
              index: 1,
              operation: "ADD",
              mimeType: "image/png"
            }
          ],
          detailEdited: true,
          userManual: true,
          updateOption: UpdateProductOption.SAVE_DRAFT
        }
      },
      submitForApproval: {
        summary: 'Gửi duyệt',
        value: {
          productInfo: {
            name: "product abc",
            listedPrice: 123,
            discountedPrice: 122,
            description: "product description"
          },
          images: [
            {
              operation: "DELETE",
              key: "marketplace/KNOWLEDGEFILE/2025/4/1/12-wqe.png"
            },
            {
              index: 1,
              operation: "ADD",
              mimeType: "image/png"
            }
          ],
          detailEdited: true,
          userManual: true,
          updateOption: UpdateProductOption.SUBMIT_FOR_APPROVAL
        }
      }
    }
  })
  @ApiOkResponse({
    description: 'Sản phẩm đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(UpdateProductResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_UPDATE_FAILED,
    MARKETPLACE_ERROR_CODES.UNAUTHORIZED
  )
  async updateProduct(
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) productId: number,
    @Body() updateProductDto: UpdateProductDto,
  ) {
    const result = await this.productUserService.updateProduct(userId, productId, updateProductDto);
    return ApiResponseDto.success(result, 'Sản phẩm đã được cập nhật thành công');
  }

  /**
   * Hủy gửi duyệt sản phẩm (chuyển trạng thái từ PENDING về DRAFT)
   * @param userId ID của người dùng hiện tại
   * @param productId ID của sản phẩm
   * @returns Sản phẩm đã được cập nhật
   */
  @Post(':id/cancel-submission')
  @ApiOperation({ summary: 'Hủy gửi duyệt sản phẩm' })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm',
    type: Number,
    example: 1,
  })
  @ApiOkResponse({
    description: 'Sản phẩm đã được hủy gửi duyệt thành công',
    schema: ApiResponseDto.getSchema(UserProductDetailResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_STATUS_CHANGE_FAILED,
    MARKETPLACE_ERROR_CODES.INVALID_STATUS,
    MARKETPLACE_ERROR_CODES.UNAUTHORIZED
  )
  async cancelSubmission(
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) productId: number,
  ) {
    const result = await this.productUserService.cancelSubmission(userId, productId);
    return ApiResponseDto.success(result, 'Sản phẩm đã được hủy gửi duyệt thành công');
  }

  /**
   * Gỡ sản phẩm (chuyển trạng thái sang DELETED)
   * @param userId ID của người dùng hiện tại
   * @param productId ID của sản phẩm
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Gỡ sản phẩm' })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm',
    type: Number,
    example: 1,
  })
  @ApiOkResponse({
    description: 'Sản phẩm đã được xóa thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_DELETE_FAILED,
    MARKETPLACE_ERROR_CODES.PRODUCT_DELETE_NOT_ALLOWED,
    MARKETPLACE_ERROR_CODES.UNAUTHORIZED
  )
  async deleteProduct(
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) productId: number,
  ) {
    await this.productUserService.deleteProduct(userId, productId);
    return ApiResponseDto.success(null, 'Sản phẩm đã được xóa thành công');
  }
}
