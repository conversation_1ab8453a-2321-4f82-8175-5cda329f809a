/**
 * <PERSON><PERSON>t để chạy các test cho module marketplace user
 * 
 * <PERSON><PERSON>ch sử dụng:
 * node run-tests.js                  - <PERSON><PERSON><PERSON> tất cả các test
 * node run-tests.js --coverage       - Ch<PERSON>y tất cả các test với coverage
 * node run-tests.js --dir controllers - <PERSON><PERSON><PERSON> tất cả các test trong thư mục controllers
 * node run-tests.js --file controllers/cart-user.controller.spec.ts - Chạy một file test cụ thể
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Màu sắc cho console
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  
  bgBlack: '\x1b[40m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m',
  bgWhite: '\x1b[47m'
};

// Đường dẫn đến thư mục test
const testDir = __dirname;
const configPath = path.join(testDir, 'jest.config.ts');

// Hàm log với màu sắc
function log(message) {
  console.log(message);
}

// Hàm chạy lệnh
function runCommand(command) {
  log(`${colors.dim}> ${command}${colors.reset}`);
  try {
    execSync(command, { stdio: 'inherit' });
    return true;
  } catch (error) {
    log(`${colors.red}Lỗi khi chạy lệnh: ${error.message}${colors.reset}`);
    return false;
  }
}

// Hàm chạy tất cả các test
function runAllTests() {
  log(`${colors.bright}${colors.green}Chạy tất cả các test cho module marketplace user${colors.reset}`);
  const command = `npx jest --config=${configPath} --maxWorkers=1 --detectOpenHandles --forceExit --runInBand --no-cache --node-options=--max-old-space-size=5120`;
  return runCommand(command);
}

// Hàm chạy tất cả các test với coverage
function runAllTestsWithCoverage() {
  log(`${colors.bright}${colors.green}Chạy tất cả các test với coverage cho module marketplace user${colors.reset}`);
  const command = `npx jest --config=${configPath} --coverage --maxWorkers=1 --detectOpenHandles --forceExit --runInBand --no-cache --node-options=--max-old-space-size=5120`;
  return runCommand(command);
}

// Hàm chạy tất cả các test trong một thư mục cụ thể
function runAllTestsInDirectory(directory) {
  if (!fs.existsSync(directory)) {
    log(`${colors.red}Thư mục không tồn tại: ${directory}${colors.reset}`);
    return false;
  }

  log(`${colors.bright}${colors.green}Chạy tất cả các test trong thư mục: ${directory}${colors.reset}`);
  const command = `npx jest --config=${configPath} ${directory} --maxWorkers=1 --detectOpenHandles --forceExit --runInBand --no-cache --node-options=--max-old-space-size=5120`;
  return runCommand(command);
}

// Hàm chạy một file test cụ thể
function runSingleTest(filePath) {
  const fullPath = path.resolve(testDir, filePath);
  if (!fs.existsSync(fullPath)) {
    log(`${colors.red}File không tồn tại: ${fullPath}${colors.reset}`);
    return false;
  }

  log(`${colors.bright}${colors.green}Chạy test cho file: ${fullPath}${colors.reset}`);
  const command = `npx jest --config=${configPath} ${fullPath} --maxWorkers=1 --detectOpenHandles --forceExit --runInBand --no-cache --node-options=--max-old-space-size=5120`;
  return runCommand(command);
}

// Xử lý tham số dòng lệnh
const args = process.argv.slice(2);
if (args.length === 0) {
  // Không có tham số, chạy tất cả các test
  runAllTests();
} else if (args[0] === '--coverage') {
  // Chạy với coverage
  runAllTestsWithCoverage();
} else if (args[0] === '--dir' && args[1]) {
  // Chạy test trong một thư mục cụ thể
  const directory = path.join(__dirname, args[1]);
  runAllTestsInDirectory(directory);
} else if (args[0] === '--file' && args[1]) {
  // Chạy một file test cụ thể
  const filePath = path.join(__dirname, args[1]);
  runSingleTest(filePath);
} else {
  // Hiển thị hướng dẫn sử dụng
  log(`${colors.bright}${colors.yellow}Cách sử dụng:${colors.reset}`);
  log(`  node run-tests.js                  - Chạy tất cả các test`);
  log(`  node run-tests.js --coverage       - Chạy tất cả các test với coverage`);
  log(`  node run-tests.js --dir controllers - Chạy tất cả các test trong thư mục controllers`);
  log(`  node run-tests.js --file controllers/cart-user.controller.spec.ts - Chạy một file test cụ thể`);
}
