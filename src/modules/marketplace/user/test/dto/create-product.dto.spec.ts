import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { CreateProductDto } from '@modules/marketplace/user/dto';
import { ProductCategory } from '@modules/marketplace/enums';

describe('CreateProductDto', () => {
  it('phải xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductDto, {
      name: 'AI Chatbot Template',
      description: 'Mẫu chatbot AI hỗ trợ khách hàng tự động',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg', 'image/png'],
      userManualMediaType: 'application/pdf',
      detailMediaType: 'application/pdf',
      sourceId: '34f5c7ef-649a-46e2-a399-34fc7c197032',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải báo lỗi khi thiếu tên sản phẩm', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductDto, {
      description: 'Mô tả sản phẩm',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg'],
      userManualMediaType: 'application/pdf',
      detailMediaType: 'application/pdf',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('name');
  });

  it('phải báo lỗi khi giá niêm yết nhỏ hơn giá sau giảm', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductDto, {
      name: 'AI Chatbot Template',
      description: 'Mô tả sản phẩm',
      listedPrice: 800, // Nhỏ hơn giá sau giảm
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg'],
      userManualMediaType: 'application/pdf',
      detailMediaType: 'application/pdf',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const priceError = errors.find(e => e.property === 'listedPrice' || e.property === 'discountedPrice');
    expect(priceError).toBeDefined();
  });

  it('phải báo lỗi khi loại sản phẩm không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductDto, {
      name: 'AI Chatbot Template',
      description: 'Mô tả sản phẩm',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: 'INVALID_CATEGORY', // Loại không hợp lệ
      imagesMediaTypes: ['image/jpeg'],
      userManualMediaType: 'application/pdf',
      detailMediaType: 'application/pdf',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('category');
  });

  it('phải báo lỗi khi định dạng hình ảnh không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductDto, {
      name: 'AI Chatbot Template',
      description: 'Mô tả sản phẩm',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['invalid/type'], // Định dạng không hợp lệ
      userManualMediaType: 'application/pdf',
      detailMediaType: 'application/pdf',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('imagesMediaTypes');
  });

  it('phải báo lỗi khi định dạng tài liệu không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductDto, {
      name: 'AI Chatbot Template',
      description: 'Mô tả sản phẩm',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg'],
      userManualMediaType: 'invalid/type', // Định dạng không hợp lệ
      detailMediaType: 'application/pdf',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('userManualMediaType');
  });

  it('phải chấp nhận DTO với các trường tùy chọn bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductDto, {
      name: 'AI Chatbot Template',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      // Thiếu các trường tùy chọn
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });
});
