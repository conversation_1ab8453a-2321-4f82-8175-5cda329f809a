import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { UpdateProductDto } from '@modules/marketplace/user/dto';
import { ProductCategory } from '@modules/marketplace/enums';

describe('UpdateProductDto', () => {
  it('phải xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      name: 'AI Chatbot Template Updated',
      description: '<PERSON><PERSON> tả cập nhật cho mẫu chatbot AI',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      option: 'UPDATE_BASIC_INFO',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải báo lỗi khi option không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      name: 'AI Chatbot Template Updated',
      description: '<PERSON><PERSON> tả cập nhật cho mẫu chatbot AI',
      option: 'INVALID_OPTION', // Option không hợp lệ
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('option');
  });

  it('phải báo lỗi khi giá niêm yết nhỏ hơn giá sau giảm', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      listedPrice: 800, // Nhỏ hơn giá sau giảm
      discountedPrice: 1000,
      option: 'UPDATE_BASIC_INFO',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const priceError = errors.find(e => e.property === 'listedPrice' || e.property === 'discountedPrice');
    expect(priceError).toBeDefined();
  });

  it('phải báo lỗi khi loại sản phẩm không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      category: 'INVALID_CATEGORY', // Loại không hợp lệ
      option: 'UPDATE_BASIC_INFO',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('category');
  });

  it('phải chấp nhận DTO với các trường tùy chọn bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      name: 'AI Chatbot Template Updated',
      option: 'UPDATE_BASIC_INFO',
      // Thiếu các trường tùy chọn
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải chấp nhận DTO với option UPDATE_IMAGES', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      option: 'UPDATE_IMAGES',
      imageKeys: ['image1.jpg', 'image2.jpg'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải báo lỗi khi option là UPDATE_IMAGES nhưng không có imageKeys', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      option: 'UPDATE_IMAGES',
      // Thiếu imageKeys
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
  });

  it('phải chấp nhận DTO với option UPDATE_USER_MANUAL', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      option: 'UPDATE_USER_MANUAL',
      userManual: 'manual.pdf',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải báo lỗi khi option là UPDATE_USER_MANUAL nhưng không có userManual', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      option: 'UPDATE_USER_MANUAL',
      // Thiếu userManual
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
  });

  it('phải chấp nhận DTO với option UPDATE_DETAIL', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      option: 'UPDATE_DETAIL',
      detail: 'detail.pdf',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải báo lỗi khi option là UPDATE_DETAIL nhưng không có detail', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductDto, {
      option: 'UPDATE_DETAIL',
      // Thiếu detail
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
  });
});
