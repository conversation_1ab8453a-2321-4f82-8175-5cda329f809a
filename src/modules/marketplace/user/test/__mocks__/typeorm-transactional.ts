export function Transactional() {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;
    descriptor.value = function (...args: any[]) {
      return originalMethod.apply(this, args);
    };
    return descriptor;
  };
}

export function runOnTransactionCommit(cb: () => void) {
  cb();
}

export function runOnTransactionRollback(cb: () => void) {
  // Do nothing
}

export function runOnTransactionComplete(cb: () => void) {
  cb();
}
