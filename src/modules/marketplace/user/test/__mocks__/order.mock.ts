import { PaginatedResult } from '@common/response/api-response-dto';
import { CombinedPurchaseHistoryResponseDto, PurchaseHistoryItemDto } from '@modules/marketplace/user/dto';
import { ProductCategory } from '@modules/marketplace/enums';
import { User } from '@modules/user/entities';
import { MarketOrderExtended } from '@modules/marketplace/interfaces';
import { MarketOrderLine } from '@modules/marketplace/entities';

/**
 * Mock data cho đơn hàng
 */
export const mockOrder: MarketOrderExtended = {
  id: 1,
  userId: 1,
  totalPoint: 1600,
  user: {
    id: 1,
    fullName: 'Nguyễn Văn A',
    email: '<EMAIL>',
  } as User,
  orderLines: [
    {
      id: 1,
      productId: 1,
      point: 800,
      productName: 'Sản phẩm 1',
      platformFeePercent: 5.0,
      sellerReceivePrice: 760,
      quantity: 2,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      product: {
        id: 1,
        name: '<PERSON><PERSON><PERSON> phẩm 1',
        description: '<PERSON><PERSON> tả sản phẩm 1',
        listedPrice: 1000,
        discountedPrice: 800,
        category: 'AGENT',
        status: 'APPROVED',
        userId: 2,
        user: {
          id: 2,
          fullName: 'Ngư<PERSON>i bán',
          email: '<EMAIL>',
        } as User,
        employee: {} as any,
        images: [{ key: 'image1.jpg', position: 0 }],
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
      },
      order: {} as any,
    } as unknown as MarketOrderLine,
  ],
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho danh sách đơn hàng
 */
export const mockOrders: MarketOrderExtended[] = [
  mockOrder,
  {
    id: 2,
    userId: 1,
    totalPoint: 2000,
    user: {
      id: 1,
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
    } as User,
    orderLines: [],
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
  } as MarketOrderExtended,
];

/**
 * Mock data cho PurchaseHistoryItemDto
 */
export const mockPurchaseHistoryItem: PurchaseHistoryItemDto = {
  productId: 1,
  productName: 'Sản phẩm 1',
  discountedPrice: 800,
  seller: {
    id: 2,
    name: 'Người bán',
    email: '<EMAIL>',
    avatar: null,
    phoneNumber: null,
  },
  createdAt: 1625097600000,
  orderId: 1,
  quantity: 2,
  platformFeePercent: 5.0,
  sellerReceivePrice: 760,
};

/**
 * Mock data cho CombinedPurchaseHistoryResponseDto
 */
export const mockCombinedPurchaseHistoryResponseDto: CombinedPurchaseHistoryResponseDto = {
  items: [mockPurchaseHistoryItem],
  meta: {
    totalItems: 1,
    itemCount: 1,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1,
  },
};
