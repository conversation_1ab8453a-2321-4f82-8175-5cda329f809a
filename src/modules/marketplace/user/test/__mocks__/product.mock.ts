import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';
import { User } from '@modules/user/entities';
import { Employee } from '@modules/employee/entities';
import { PaginatedResult } from '@common/response/api-response-dto';
import { ProductDetailResponseDto, UserProductDetailResponseDto, UserProductResponseDto } from '@modules/marketplace/user/dto';

/**
 * Mock data cho sản phẩm
 */
export const mockProduct: any = {
  id: 1,
  name: 'Sản phẩm test',
  description: 'Mô tả sản phẩm test',
  listedPrice: 1000,
  discountedPrice: 800,
  category: ProductCategory.AGENT,
  status: ProductStatus.APPROVED,
  userId: 1,
  employeeId: null,
  'user.id': 1,
  'user.full_name': 'Người dùng',
  'user.email': '<EMAIL>',
  employee: {} as any,
  images: [
    { key: 'image1.jpg', position: 0 },
    { key: 'image2.jpg', position: 1 },
  ],
  userManual: 'manual.pdf',
  detail: 'detail.pdf',
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  sourceId: 'source-123',
};

/**
 * Mock data cho sản phẩm của admin
 */
export const mockAdminProduct: any = {
  id: 2,
  name: 'Sản phẩm admin',
  description: 'Mô tả sản phẩm admin',
  listedPrice: 2000,
  discountedPrice: 1800,
  category: ProductCategory.FUNCTION,
  status: ProductStatus.APPROVED,
  userId: null,
  employeeId: 1,
  'user.id': null,
  'employee.id': 1,
  'employee.full_name': 'Admin',
  'employee.email': '<EMAIL>',
  images: [
    { key: 'admin-image1.jpg', position: 0 },
  ],
  userManual: 'admin-manual.pdf',
  detail: 'admin-detail.pdf',
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  sourceId: 'admin-source-123',
};

/**
 * Mock data cho danh sách sản phẩm
 */
export const mockProducts: any[] = [
  mockProduct,
  mockAdminProduct,
];

/**
 * Mock data cho UserProductResponseDto
 */
export const mockUserProductResponseDto: UserProductResponseDto = {
  id: 1,
  name: 'Sản phẩm test',
  description: 'Mô tả sản phẩm test',
  listedPrice: 1000,
  discountedPrice: 800,
  category: ProductCategory.AGENT,
  status: ProductStatus.APPROVED,
  images: [
    { key: 'image1.jpg', position: 0, url: 'https://cdn.redai.vn/image1.jpg?t=123456789' },
    { key: 'image2.jpg', position: 1, url: 'https://cdn.redai.vn/image2.jpg?t=123456789' },
  ],
  seller: {
    id: 1,
    name: 'Người dùng',
    email: '<EMAIL>',
    avatar: null,
    type: 'user',
  },
  createdAt: 1625097600000,
  userManual: 'https://cdn.redai.vn/manual.pdf?t=123456789',
  detail: 'https://cdn.redai.vn/detail.pdf?t=123456789',
};

/**
 * Mock data cho ProductDetailResponseDto
 */
export const mockProductDetailResponseDto: ProductDetailResponseDto = {
  id: 1,
  name: 'Sản phẩm test',
  description: 'Mô tả sản phẩm test',
  listedPrice: 1000,
  discountedPrice: 800,
  category: ProductCategory.AGENT,
  images: [
    { key: 'image1.jpg', position: 0, url: 'https://cdn.redai.vn/image1.jpg?t=123456789' },
    { key: 'image2.jpg', position: 1, url: 'https://cdn.redai.vn/image2.jpg?t=123456789' },
  ],
  seller: {
    id: 1,
    name: 'Người dùng',
    email: '<EMAIL>',
    avatar: null,
    type: 'user',
  },
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  sourceId: 'source-123',
  userManual: 'https://cdn.redai.vn/manual.pdf?t=123456789',
  detail: 'https://cdn.redai.vn/detail.pdf?t=123456789',
};

/**
 * Mock data cho UserProductDetailResponseDto
 */
export const mockUserProductDetailResponseDto: UserProductDetailResponseDto = {
  id: 1,
  name: 'Sản phẩm test',
  description: 'Mô tả sản phẩm test',
  listedPrice: 1000,
  discountedPrice: 800,
  category: ProductCategory.AGENT,
  status: ProductStatus.APPROVED,
  images: [
    { key: 'image1.jpg', position: 0, url: 'https://cdn.redai.vn/image1.jpg?t=123456789' },
    { key: 'image2.jpg', position: 1, url: 'https://cdn.redai.vn/image2.jpg?t=123456789' },
  ],
  seller: {
    id: 1,
    name: 'Người dùng',
    email: '<EMAIL>',
    avatar: null,
    type: 'user',
  },
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  userManual: 'https://cdn.redai.vn/manual.pdf?t=123456789',
  detail: 'https://cdn.redai.vn/detail.pdf?t=123456789',
  sourceId: 'source-123',
};

/**
 * Mock data cho PaginatedResult<UserProductResponseDto>
 */
export const mockPaginatedUserProductResponseDto: PaginatedResult<UserProductResponseDto> = {
  items: [mockUserProductResponseDto],
  meta: {
    totalItems: 1,
    itemCount: 1,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1,
  },
};

/**
 * Mock data cho kết quả tạo sản phẩm
 */
export const mockCreateProductResult = {
  id: 1,
  name: 'Sản phẩm mới',
  status: ProductStatus.DRAFT,
};

/**
 * Mock data cho kết quả cập nhật sản phẩm
 */
export const mockUpdateProductResult = {
  id: 1,
  name: 'Sản phẩm đã cập nhật',
  status: ProductStatus.DRAFT,
};
