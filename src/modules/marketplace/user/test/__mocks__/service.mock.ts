import { CartUserService, ProductUserService, OrderUserService, PaymentService } from '@modules/marketplace/user/services';
import { mockCartResponseDto, mockAddToCartResult, mockUpdateCartResult, mockRemoveFromCartResult } from './cart.mock';
import { mockUserProductResponseDto, mockProductDetailResponseDto, mockUserProductDetailResponseDto, mockPaginatedUserProductResponseDto, mockCreateProductResult, mockUpdateProductResult } from './product.mock';
import { mockCombinedPurchaseHistoryResponseDto } from './order.mock';
import {
  QueryCartDto,
  QueryUserProductDto,
  QueryPurchaseHistoryDto,
  AddToCartDto,
  UpdateCartItemDto,
  CreateProductDto,
  UpdateProductDto,
  PaymentDto,
} from '@modules/marketplace/user/dto';
import { PresignedUrlDto } from '@modules/marketplace/user/dto';

/**
 * Mock cho CartUserService
 */
export const mockCartUserService = {
  getCart: jest.fn().mockImplementation(() => Promise.resolve(mockCartResponseDto)),
  addToCart: jest.fn().mockImplementation(() => Promise.resolve(mockAddToCartResult)),
  updateCartItem: jest.fn().mockImplementation(() => Promise.resolve(mockUpdateCartResult)),
  removeFromCart: jest.fn().mockImplementation(() => Promise.resolve(mockRemoveFromCartResult)),
};

/**
 * Mock cho ProductUserService
 */
export const mockProductUserService = {
  getApprovedProducts: jest.fn().mockImplementation(() => Promise.resolve(mockPaginatedUserProductResponseDto)),
  getProductDetail: jest.fn().mockImplementation(() => Promise.resolve(mockProductDetailResponseDto)),
  getUserProducts: jest.fn().mockImplementation(() => Promise.resolve(mockPaginatedUserProductResponseDto)),
  getUserProductDetail: jest.fn().mockImplementation(() => Promise.resolve(mockUserProductDetailResponseDto)),
  createProduct: jest.fn().mockImplementation(() => Promise.resolve(mockCreateProductResult)),
  updateProduct: jest.fn().mockImplementation(() => Promise.resolve(mockUpdateProductResult)),
  submitProduct: jest.fn().mockImplementation(() => Promise.resolve({ id: 1, status: 'PENDING_APPROVAL' })),
  deleteProduct: jest.fn().mockImplementation(() => Promise.resolve({ id: 1, status: 'DELETED' })),
  createImageUploadUrl: jest.fn().mockImplementation(() => Promise.resolve({
    url: 'https://presigned-url.com/image',
    key: 'marketplace/image/product-image-123.jpg',
  } as PresignedUrlDto)),
  createDocumentUploadUrl: jest.fn().mockImplementation(() => Promise.resolve({
    url: 'https://presigned-url.com/document',
    key: 'marketplace/document/product-document-123.pdf',
  } as PresignedUrlDto)),
};

/**
 * Mock cho OrderUserService
 */
export const mockOrderUserService = {
  getPurchaseHistory: jest.fn().mockImplementation(() => Promise.resolve(mockCombinedPurchaseHistoryResponseDto)),
};

/**
 * Mock cho PaymentService
 */
export const mockPaymentService = {
  processPayment: jest.fn().mockImplementation(() => Promise.resolve({
    orderId: 1,
    totalPoint: 1600,
    products: [
      {
        id: 1,
        name: 'Sản phẩm 1',
        price: 800,
        quantity: 2,
      },
    ],
  })),
};
