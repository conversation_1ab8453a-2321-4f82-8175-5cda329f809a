import { ProductRepository, CartRepository, CartItemRepository, MarketOrderRepository, MarketOrderLineRepository } from '@modules/marketplace/repositories';
import { mockProduct, mockProducts } from './product.mock';
import { mockCart } from './cart.mock';
import { mockOrder, mockOrders } from './order.mock';
import { ProductStatus } from '@modules/marketplace/enums';

/**
 * Mock cho ProductRepository
 */
export const mockProductRepository = {
  findById: jest.fn().mockImplementation(() => Promise.resolve(mockProduct)),
  findApprovedProducts: jest.fn().mockImplementation(() => Promise.resolve({
    items: mockProducts.filter(p => p.status === ProductStatus.APPROVED),
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  })),
  findUserProducts: jest.fn().mockImplementation(() => Promise.resolve({
    items: [mockProduct],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  })),
  save: jest.fn().mockImplementation((product) => Promise.resolve({ ...product, id: 1 })),
  create: jest.fn().mockImplementation((data) => ({ ...data, id: 1 })),
  checkProductsPurchased: jest.fn().mockImplementation(() => Promise.resolve([])),
  findByIdWithUser: jest.fn().mockImplementation(() => Promise.resolve(mockProduct)),
};

/**
 * Mock cho CartRepository
 */
export const mockCartRepository = {
  findOrCreateCart: jest.fn().mockImplementation(() => Promise.resolve(mockCart)),
  findCartWithFilters: jest.fn().mockImplementation(() => Promise.resolve(mockCart)),
  save: jest.fn().mockImplementation((cart) => Promise.resolve({ ...cart, id: 1 })),
  create: jest.fn().mockImplementation((data) => ({ ...data, id: 1 })),
};

/**
 * Mock cho CartItemRepository
 */
export const mockCartItemRepository = {
  findById: jest.fn().mockImplementation(() => Promise.resolve(mockCart.cartItems[0])),
  findByCartIdAndProductId: jest.fn().mockImplementation(() => Promise.resolve(mockCart.cartItems[0])),
  addToCart: jest.fn().mockImplementation(() => Promise.resolve({ id: 1, cartId: 1, productId: 1, quantity: 2 })),
  updateQuantity: jest.fn().mockImplementation(() => Promise.resolve({ id: 1, cartId: 1, productId: 1, quantity: 3 })),
  remove: jest.fn().mockImplementation(() => Promise.resolve(true)),
  isProductValidForCart: jest.fn().mockImplementation(() => Promise.resolve(true)),
  save: jest.fn().mockImplementation((item) => Promise.resolve({ ...item, id: 1 })),
  create: jest.fn().mockImplementation((data) => ({ ...data, id: 1 })),
};

/**
 * Mock cho MarketOrderRepository
 */
export const mockMarketOrderRepository = {
  findById: jest.fn().mockImplementation(() => Promise.resolve(mockOrder)),
  findUserOrders: jest.fn().mockImplementation(() => Promise.resolve({
    items: mockOrders,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  })),
  createOrderWithLines: jest.fn().mockImplementation(() => Promise.resolve(mockOrder)),
  save: jest.fn().mockImplementation((order) => Promise.resolve({ ...order, id: 1 })),
  create: jest.fn().mockImplementation((data) => ({ ...data, id: 1 })),
};

/**
 * Mock cho MarketOrderLineRepository
 */
export const mockMarketOrderLineRepository = {
  findByOrderId: jest.fn().mockImplementation(() => Promise.resolve(mockOrder.orderLines)),
  save: jest.fn().mockImplementation((line) => Promise.resolve({ ...line, id: 1 })),
  create: jest.fn().mockImplementation((data) => ({ ...data, id: 1 })),
};
