export class AppException extends Error {
  constructor(public readonly errorCode: string, message: string) {
    super(message);
    this.name = 'AppException';
  }
}

export class ErrorCode {
  static readonly code: string;
  static readonly message: string;
}

export class ErrorCodes {
  static readonly INTERNAL_SERVER_ERROR = {
    code: 'INTERNAL_SERVER_ERROR',
    message: 'Lỗi máy chủ nội bộ',
  };

  static readonly BAD_REQUEST = {
    code: 'BAD_REQUEST',
    message: '<PERSON><PERSON>u cầu không hợp lệ',
  };

  static readonly UNAUTHORIZED = {
    code: 'UNAUTHORIZED',
    message: 'Không được phép truy cập',
  };

  static readonly NOT_FOUND = {
    code: 'NOT_FOUND',
    message: 'Không tìm thấy tài nguyên',
  };
}
