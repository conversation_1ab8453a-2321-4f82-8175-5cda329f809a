import { PaginatedResult } from '@common/response/api-response-dto';
import { CartResponseDto } from '@modules/marketplace/user/dto';
import { ProductCategory } from '@modules/marketplace/enums';

/**
 * Mock data cho giỏ hàng
 */
export const mockCart: any = {
  id: 1,
  user_id: 1,
  cartItems: [
    {
      id: 1,
      cart_id: 1,
      product_id: 1,
      quantity: 2,
      product_name: 'Sản phẩm 1',
      product_discounted_price: 800,
      user_id: 2,
      user_full_name: '<PERSON>ư<PERSON><PERSON> bán',
      user_email: '<EMAIL>',
      user_avatar: 'avatar.jpg',
      employee_id: null,
      employee_full_name: null,
      employee_email: null,
      product_category: ProductCategory.AGENT,
      product_image_key: 'image1.jpg',
    },
  ],
  created_at: 1625097600000,
  updated_at: 1625097600000,
};

/**
 * Mock data cho CartResponseDto
 */
export const mockCartResponseDto: CartResponseDto = {
  items: [
    {
      cartItemId: 1,
      productId: 1,
      productName: 'Sản phẩm 1',
      discountedPrice: 800,
      quantity: 2,
      sellerName: 'Người bán',
      createdAt: 1625097600000,
    },
  ],
  totalValue: 1600,
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho kết quả thêm vào giỏ hàng
 */
export const mockAddToCartResult = {
  cartItemId: 1,
  message: 'Đã thêm sản phẩm vào giỏ hàng',
};

/**
 * Mock data cho kết quả cập nhật giỏ hàng
 */
export const mockUpdateCartResult = {
  cartItemId: 1,
  message: 'Đã cập nhật số lượng sản phẩm',
};

/**
 * Mock data cho kết quả xóa khỏi giỏ hàng
 */
export const mockRemoveFromCartResult = {
  message: 'Đã xóa sản phẩm khỏi giỏ hàng',
};
