# Tests cho Module Marketplace User

## Cấu trúc thư mục

```
test/
├── __mocks__/                # Mock data và services
│   ├── cart.mock.ts          # Mock data cho giỏ hàng
│   ├── order.mock.ts         # Mock data cho đơn hàng
│   ├── product.mock.ts       # Mock data cho sản phẩm
│   ├── repository.mock.ts    # Mock cho Repository
│   └── service.mock.ts       # Mock cho Service
├── controllers/              # Tests cho controllers
│   ├── cart-user.controller.spec.ts    # Test cho CartUserController
│   ├── order-user.controller.spec.ts   # Test cho OrderUserController
│   ├── product-user.controller.spec.ts # Test cho ProductUserController
│   └── payment.controller.spec.ts      # Test cho PaymentController
├── services/                 # Tests cho services
│   ├── cart-user.service.spec.ts       # Test cho CartUserService
│   ├── order-user.service.spec.ts      # Test cho OrderUserService
│   ├── product-user.service.spec.ts    # Test cho ProductUserService
│   └── payment.service.spec.ts         # Test cho PaymentService
├── dto/                      # Tests cho DTOs
│   ├── create-product.dto.spec.ts      # Test cho CreateProductDto
│   └── update-product.dto.spec.ts      # Test cho UpdateProductDto
├── jest.config.ts            # Cấu hình Jest cho module marketplace user
├── run-tests.js              # Script chạy test
└── README.md                 # Tài liệu hướng dẫn
```

## Cách chạy tests

```bash
# Chạy tất cả tests
npm run test:marketplace-user

# Chạy test cho một file cụ thể
npx jest src/modules/marketplace/user/test/controllers/cart-user.controller.spec.ts --config=src/modules/marketplace/user/test/jest.config.ts

# Chạy test với coverage
npx jest --config=src/modules/marketplace/user/test/jest.config.ts --coverage

# Sử dụng script run-tests.js
node src/modules/marketplace/user/test/run-tests.js                  # Chạy tất cả các test
node src/modules/marketplace/user/test/run-tests.js --coverage       # Chạy tất cả các test với coverage
node src/modules/marketplace/user/test/run-tests.js --dir controllers # Chạy tất cả các test trong thư mục controllers
node src/modules/marketplace/user/test/run-tests.js --file controllers/cart-user.controller.spec.ts # Chạy một file test cụ thể
```

## Quy ước viết test

1. Mỗi controller và service cần có ít nhất một file test tương ứng
2. Sử dụng mock cho các dependencies để đảm bảo unit test độc lập
3. Đảm bảo test coverage cho các trường hợp thành công và thất bại
4. Sử dụng các mocks trong thư mục `__mocks__` để đảm bảo tính nhất quán

## Danh sách các test

### Controllers
- **CartUserController**: Test các API liên quan đến giỏ hàng
  - `getCart`: Lấy thông tin giỏ hàng
  - `addToCart`: Thêm sản phẩm vào giỏ hàng
  - `updateCartItem`: Cập nhật số lượng sản phẩm trong giỏ hàng
  - `removeFromCart`: Xóa sản phẩm khỏi giỏ hàng

- **ProductUserController**: Test các API liên quan đến sản phẩm
  - `getApprovedProducts`: Lấy danh sách sản phẩm được phê duyệt
  - `getProductDetail`: Lấy thông tin chi tiết sản phẩm
  - `getUserProducts`: Lấy danh sách sản phẩm của người dùng
  - `createProduct`: Tạo sản phẩm mới
  - `updateProduct`: Cập nhật sản phẩm
  - `submitProduct`: Gửi sản phẩm để duyệt
  - `deleteProduct`: Xóa sản phẩm

- **OrderUserController**: Test các API liên quan đến đơn hàng
  - `getPurchaseHistory`: Lấy lịch sử mua hàng

- **PaymentController**: Test các API liên quan đến thanh toán
  - `processPayment`: Xử lý thanh toán

### Services
- **CartUserService**: Test các phương thức xử lý logic giỏ hàng
  - `getCart`: Lấy thông tin giỏ hàng
  - `addToCart`: Thêm sản phẩm vào giỏ hàng
  - `updateCartItem`: Cập nhật số lượng sản phẩm trong giỏ hàng
  - `removeFromCart`: Xóa sản phẩm khỏi giỏ hàng

- **ProductUserService**: Test các phương thức xử lý logic sản phẩm
  - `getApprovedProducts`: Lấy danh sách sản phẩm được phê duyệt
  - `getProductDetail`: Lấy thông tin chi tiết sản phẩm
  - `getUserProducts`: Lấy danh sách sản phẩm của người dùng
  - `createProduct`: Tạo sản phẩm mới
  - `updateProduct`: Cập nhật sản phẩm
  - `submitProduct`: Gửi sản phẩm để duyệt
  - `deleteProduct`: Xóa sản phẩm

- **OrderUserService**: Test các phương thức xử lý logic đơn hàng
  - `getPurchaseHistory`: Lấy lịch sử mua hàng

- **PaymentService**: Test các phương thức xử lý logic thanh toán
  - `processPayment`: Xử lý thanh toán
