import { Test, TestingModule } from '@nestjs/testing';
import { ProductUserService } from '@modules/marketplace/user/services/product-user.service';
import { ProductRepository } from '@modules/marketplace/repositories';
import { ProductHelper, ValidationHelper, MediaHelper } from '@modules/marketplace/helpers';
import { S3Service, CdnService } from '@shared/services';
import { mockProductRepository } from '../__mocks__/repository.mock';
import { mockProduct, mockProducts } from '../__mocks__/product.mock';
import { QueryUserProductDto, CreateProductDto, UpdateProductDto } from '@modules/marketplace/user/dto';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';
import { AppException } from '../__mocks__/@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '../__mocks__/@modules/marketplace/exceptions';
import { FileTypeEnum, ImageTypeEnum, TimeIntervalEnum } from '@shared/utils';

describe('Dịch vụ quản lý sản phẩm (User)', () => {
  let service: ProductUserService;
  let productRepository: ProductRepository;
  let productHelper: ProductHelper;
  let validationHelper: ValidationHelper;
  let mediaHelper: MediaHelper;
  let s3Service: S3Service;
  let cdnService: CdnService;

  const mockProductHelper = {
    mapToUserProductResponseDto: jest.fn(),
    mapToProductDetailResponseDto: jest.fn(),
    mapToUserProductDetailResponseDto: jest.fn(),
    extractSellerInfo: jest.fn(),
  };

  const mockValidationHelper = {
    validateProductExists: jest.fn(),
    validateProductNotDeleted: jest.fn(),
    validateProductOwnership: jest.fn(),
    validateProductForUpdate: jest.fn(),
    validateProductForSubmission: jest.fn(),
    validateProductForCart: jest.fn(),
  };

  const mockMediaHelper = {
    getImageTypeFromMimeString: jest.fn(),
  };

  const mockS3Service = {
    createPresignedWithID: jest.fn(),
    getDownloadUrl: jest.fn(),
  };

  const mockCdnService = {
    generateUrlView: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductUserService,
        {
          provide: ProductRepository,
          useValue: mockProductRepository,
        },
        {
          provide: ProductHelper,
          useValue: mockProductHelper,
        },
        {
          provide: ValidationHelper,
          useValue: mockValidationHelper,
        },
        {
          provide: MediaHelper,
          useValue: mockMediaHelper,
        },
        {
          provide: S3Service,
          useValue: mockS3Service,
        },
        {
          provide: CdnService,
          useValue: mockCdnService,
        },
      ],
    }).compile();

    service = module.get<ProductUserService>(ProductUserService);
    productRepository = module.get<ProductRepository>(ProductRepository);
    productHelper = module.get<ProductHelper>(ProductHelper);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
    mediaHelper = module.get<MediaHelper>(MediaHelper);
    s3Service = module.get<S3Service>(S3Service);
    cdnService = module.get<CdnService>(CdnService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getApprovedProducts', () => {
    it('phải trả về danh sách sản phẩm được phê duyệt', async () => {
      // Arrange
      const queryDto: QueryUserProductDto = {
        page: 1,
        limit: 10,
      };
      const userId = 1;
      const mockPaginatedResult = {
        items: mockProducts,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };
      const mockMappedItems = mockProducts.map((p) => ({ ...p, id: p.id }));
      
      jest.spyOn(productRepository, 'findApprovedProducts').mockResolvedValue(mockPaginatedResult);
      jest.spyOn(productHelper, 'mapToUserProductResponseDto').mockImplementation((product) => ({
        ...product,
        id: product.id,
      }));

      // Act
      const result = await service.getApprovedProducts(queryDto, userId);

      // Assert
      expect(result).toBeDefined();
      expect(result.items).toHaveLength(mockProducts.length);
      expect(productRepository.findApprovedProducts).toHaveBeenCalledWith(queryDto, userId);
      expect(productHelper.mapToUserProductResponseDto).toHaveBeenCalledTimes(mockProducts.length);
    });

    it('phải xử lý lỗi khi repository ném ra ngoại lệ', async () => {
      // Arrange
      const queryDto: QueryUserProductDto = {
        page: 1,
        limit: 10,
      };
      const userId = 1;
      const error = new Error('Database error');
      jest.spyOn(productRepository, 'findApprovedProducts').mockRejectedValue(error);

      // Act & Assert
      await expect(service.getApprovedProducts(queryDto, userId)).rejects.toThrow();
    });
  });

  describe('getProductDetail', () => {
    it('phải trả về chi tiết sản phẩm', async () => {
      // Arrange
      const productId = 1;
      const userId = 1;
      const mockDetailDto = { id: productId, name: 'Sản phẩm test' };
      
      jest.spyOn(productRepository, 'findById').mockResolvedValue(mockProduct);
      jest.spyOn(productHelper, 'mapToProductDetailResponseDto').mockReturnValue(mockDetailDto);

      // Act
      const result = await service.getProductDetail(productId, userId);

      // Assert
      expect(result).toBeDefined();
      expect(result).toEqual(mockDetailDto);
      expect(productRepository.findById).toHaveBeenCalledWith(productId);
      expect(productHelper.mapToProductDetailResponseDto).toHaveBeenCalledWith(mockProduct);
    });

    it('phải ném ra ngoại lệ khi sản phẩm không tồn tại', async () => {
      // Arrange
      const productId = 999;
      const userId = 1;
      
      jest.spyOn(productRepository, 'findById').mockResolvedValue(null);
      jest.spyOn(validationHelper, 'validateProductExists').mockImplementation(() => {
        throw new AppException(MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND, 'Không tìm thấy sản phẩm');
      });

      // Act & Assert
      await expect(service.getProductDetail(productId, userId)).rejects.toThrow(AppException);
      expect(productRepository.findById).toHaveBeenCalledWith(productId);
    });
  });

  describe('createImageUploadUrl', () => {
    it('phải tạo URL tải lên hình ảnh', async () => {
      // Arrange
      const fileName = 'test-image.jpg';
      const mediaType = ImageTypeEnum.JPEG;
      const mockKey = 'marketplace/image/test-image.jpg';
      const mockUrl = 'https://presigned-url.com/image';
      
      jest.spyOn(s3Service, 'createPresignedWithID').mockResolvedValue(mockUrl);

      // Act
      const result = await service.createImageUploadUrl(fileName, mediaType);

      // Assert
      expect(result).toBeDefined();
      expect(result.url).toEqual(mockUrl);
      expect(s3Service.createPresignedWithID).toHaveBeenCalled();
    });
  });

  describe('createDocumentUploadUrl', () => {
    it('phải tạo URL tải lên tài liệu', async () => {
      // Arrange
      const fileName = 'test-document.pdf';
      const fileType = FileTypeEnum.PDF;
      const mockKey = 'marketplace/document/test-document.pdf';
      const mockUrl = 'https://presigned-url.com/document';
      
      jest.spyOn(s3Service, 'createPresignedWithID').mockResolvedValue(mockUrl);

      // Act
      const result = await service.createDocumentUploadUrl(fileName, fileType);

      // Assert
      expect(result).toBeDefined();
      expect(result.url).toEqual(mockUrl);
      expect(s3Service.createPresignedWithID).toHaveBeenCalled();
    });
  });

  describe('createProduct', () => {
    it('phải tạo sản phẩm mới', async () => {
      // Arrange
      const userId = 1;
      const createDto: CreateProductDto = {
        name: 'Sản phẩm mới',
        description: 'Mô tả sản phẩm mới',
        listedPrice: 1000,
        discountedPrice: 800,
        category: ProductCategory.AGENT,
        imagesMediaTypes: ['image/jpeg'],
        userManualMediaType: 'application/pdf',
        detailMediaType: 'application/pdf',
        sourceId: 'source-123',
      };
      const mockCreatedProduct = {
        id: 1,
        name: createDto.name,
        status: ProductStatus.DRAFT,
      };
      
      jest.spyOn(productRepository, 'create').mockReturnValue(mockCreatedProduct as any);
      jest.spyOn(productRepository, 'save').mockResolvedValue(mockCreatedProduct as any);

      // Act
      const result = await service.createProduct(userId, createDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.id).toEqual(mockCreatedProduct.id);
      expect(result.status).toEqual(ProductStatus.DRAFT);
      expect(productRepository.create).toHaveBeenCalled();
      expect(productRepository.save).toHaveBeenCalled();
    });
  });

  describe('updateProduct', () => {
    it('phải cập nhật sản phẩm', async () => {
      // Arrange
      const productId = 1;
      const userId = 1;
      const updateDto: UpdateProductDto = {
        name: 'Sản phẩm đã cập nhật',
        description: 'Mô tả đã cập nhật',
        listedPrice: 1200,
        discountedPrice: 1000,
        option: 'UPDATE_BASIC_INFO',
      };
      const mockUpdatedProduct = {
        id: productId,
        name: updateDto.name,
        status: ProductStatus.DRAFT,
      };
      
      jest.spyOn(productRepository, 'findById').mockResolvedValue(mockProduct);
      jest.spyOn(productRepository, 'save').mockResolvedValue(mockUpdatedProduct as any);

      // Act
      const result = await service.updateProduct(productId, userId, updateDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.id).toEqual(mockUpdatedProduct.id);
      expect(productRepository.findById).toHaveBeenCalledWith(productId);
      expect(productRepository.save).toHaveBeenCalled();
    });

    it('phải ném ra ngoại lệ khi sản phẩm không thuộc về người dùng', async () => {
      // Arrange
      const productId = 1;
      const userId = 2; // Khác với userId của sản phẩm
      const updateDto: UpdateProductDto = {
        name: 'Sản phẩm đã cập nhật',
        option: 'UPDATE_BASIC_INFO',
      };
      
      jest.spyOn(productRepository, 'findById').mockResolvedValue(mockProduct);
      jest.spyOn(validationHelper, 'validateProductOwnership').mockImplementation(() => {
        throw new AppException(MARKETPLACE_ERROR_CODES.UNAUTHORIZED, 'Bạn không có quyền truy cập sản phẩm này');
      });

      // Act & Assert
      await expect(service.updateProduct(productId, userId, updateDto)).rejects.toThrow(AppException);
      expect(productRepository.findById).toHaveBeenCalledWith(productId);
      expect(validationHelper.validateProductOwnership).toHaveBeenCalled();
    });
  });
});
