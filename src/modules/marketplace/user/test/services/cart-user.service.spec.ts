import { Test, TestingModule } from '@nestjs/testing';
import { CartUserService } from '@modules/marketplace/user/services/cart-user.service';
import { CartRepository, CartItemRepository, ProductRepository } from '@modules/marketplace/repositories';
import { Cart<PERSON>elper, ValidationHelper } from '@modules/marketplace/helpers';
import { mockCartRepository, mockCartItemRepository, mockProductRepository } from '../__mocks__/repository.mock';
import { mockCart } from '../__mocks__/cart.mock';
import { mockProduct } from '../__mocks__/product.mock';
import { QueryCartDto, AddToCartDto, UpdateCartItemDto } from '@modules/marketplace/user/dto';
import { AppException } from '../__mocks__/@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '../__mocks__/@modules/marketplace/exceptions';

describe('Dịch vụ quản lý giỏ hàng (User)', () => {
  let service: CartUserService;
  let cartRepository: CartRepository;
  let cartItemRepository: CartItemRepository;
  let productRepository: ProductRepository;
  let cartHelper: CartHelper;
  let validationHelper: ValidationHelper;

  const mockCartHelper = {
    mapToCartResponseDto: jest.fn(),
  };

  const mockValidationHelper = {
    validateProductExists: jest.fn(),
    validateProductNotDeleted: jest.fn(),
    validateProductForCart: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CartUserService,
        {
          provide: CartRepository,
          useValue: mockCartRepository,
        },
        {
          provide: CartItemRepository,
          useValue: mockCartItemRepository,
        },
        {
          provide: ProductRepository,
          useValue: mockProductRepository,
        },
        {
          provide: CartHelper,
          useValue: mockCartHelper,
        },
        {
          provide: ValidationHelper,
          useValue: mockValidationHelper,
        },
      ],
    }).compile();

    service = module.get<CartUserService>(CartUserService);
    cartRepository = module.get<CartRepository>(CartRepository);
    cartItemRepository = module.get<CartItemRepository>(CartItemRepository);
    productRepository = module.get<ProductRepository>(ProductRepository);
    cartHelper = module.get<CartHelper>(CartHelper);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getCart', () => {
    it('phải trả về thông tin giỏ hàng', async () => {
      // Arrange
      const userId = 1;
      const queryDto: QueryCartDto = {
        search: '',
        sortBy: 'createdAt',
        sortDirection: 'DESC',
      };
      const mockCartResponse = {
        id: 1,
        items: [
          {
            id: 1,
            productId: 1,
            productName: 'Sản phẩm 1',
            discountedPrice: 800,
            quantity: 2,
            seller: {
              id: 2,
              name: 'Người bán',
              email: '<EMAIL>',
              avatar: 'https://cdn.redai.vn/avatar.jpg?t=123456789',
            },
            category: 'AGENT',
            imageUrl: 'https://cdn.redai.vn/image1.jpg?t=123456789',
            createdAt: 1625097600000,
          },
        ],
        totalValue: 1600,
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
      };
      
      jest.spyOn(cartRepository, 'findOrCreateCart').mockResolvedValue(mockCart);
      jest.spyOn(cartRepository, 'findCartWithFilters').mockResolvedValue(mockCart);
      jest.spyOn(cartHelper, 'mapToCartResponseDto').mockReturnValue(mockCartResponse);

      // Act
      const result = await service.getCart(userId, queryDto);

      // Assert
      expect(result).toBeDefined();
      expect(result).toEqual(mockCartResponse);
      expect(cartRepository.findCartWithFilters).toHaveBeenCalledWith(userId, queryDto);
      expect(cartHelper.mapToCartResponseDto).toHaveBeenCalledWith(mockCart);
    });

    it('phải trả về giỏ hàng trống khi không tìm thấy giỏ hàng', async () => {
      // Arrange
      const userId = 1;
      const queryDto: QueryCartDto = {
        search: '',
        sortBy: 'createdAt',
        sortDirection: 'DESC',
      };
      const emptyCartResponse = {
        id: 1,
        items: [],
        totalValue: 0,
        createdAt: expect.any(Number),
        updatedAt: expect.any(Number),
      };
      
      jest.spyOn(cartRepository, 'findCartWithFilters').mockResolvedValue(null);
      jest.spyOn(cartRepository, 'findOrCreateCart').mockResolvedValue({ ...mockCart, cartItems: [] });
      jest.spyOn(cartHelper, 'mapToCartResponseDto').mockReturnValue(emptyCartResponse);

      // Act
      const result = await service.getCart(userId, queryDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.items).toHaveLength(0);
      expect(result.totalValue).toBe(0);
      expect(cartRepository.findCartWithFilters).toHaveBeenCalledWith(userId, queryDto);
      expect(cartRepository.findOrCreateCart).toHaveBeenCalledWith(userId);
    });
  });

  describe('addToCart', () => {
    it('phải thêm sản phẩm vào giỏ hàng', async () => {
      // Arrange
      const userId = 1;
      const addToCartDto: AddToCartDto = {
        productId: 1,
        quantity: 2,
      };
      const mockCartItem = {
        id: 1,
        cartId: 1,
        productId: 1,
        quantity: 2,
      };
      
      jest.spyOn(productRepository, 'findById').mockResolvedValue(mockProduct);
      jest.spyOn(cartItemRepository, 'isProductValidForCart').mockResolvedValue(true);
      jest.spyOn(cartRepository, 'findOrCreateCart').mockResolvedValue(mockCart);
      jest.spyOn(cartItemRepository, 'addToCart').mockResolvedValue(mockCartItem);

      // Act
      const result = await service.addToCart(userId, addToCartDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.cartItemId).toBe(mockCartItem.id);
      expect(productRepository.findById).toHaveBeenCalledWith(addToCartDto.productId);
      expect(validationHelper.validateProductForCart).toHaveBeenCalledWith(mockProduct, userId);
      expect(cartRepository.findOrCreateCart).toHaveBeenCalledWith(userId);
      expect(cartItemRepository.addToCart).toHaveBeenCalledWith(mockCart.id, addToCartDto.productId, addToCartDto.quantity);
    });

    it('phải ném ra ngoại lệ khi sản phẩm không hợp lệ', async () => {
      // Arrange
      const userId = 1;
      const addToCartDto: AddToCartDto = {
        productId: 1,
        quantity: 2,
      };
      
      jest.spyOn(productRepository, 'findById').mockResolvedValue(mockProduct);
      jest.spyOn(validationHelper, 'validateProductForCart').mockImplementation(() => {
        throw new AppException(MARKETPLACE_ERROR_CODES.PRODUCT_NOT_APPROVED, 'Chỉ có thể thêm sản phẩm đã được phê duyệt vào giỏ hàng');
      });

      // Act & Assert
      await expect(service.addToCart(userId, addToCartDto)).rejects.toThrow(AppException);
      expect(productRepository.findById).toHaveBeenCalledWith(addToCartDto.productId);
      expect(validationHelper.validateProductForCart).toHaveBeenCalledWith(mockProduct, userId);
    });
  });

  describe('updateCartItem', () => {
    it('phải cập nhật số lượng sản phẩm trong giỏ hàng', async () => {
      // Arrange
      const cartItemId = 1;
      const userId = 1;
      const updateCartItemDto: UpdateCartItemDto = {
        quantity: 3,
      };
      const mockCartItem = {
        id: 1,
        cartId: 1,
        productId: 1,
        quantity: 2,
        cart_id: 1,
      };
      const mockUpdatedCartItem = {
        id: 1,
        cartId: 1,
        productId: 1,
        quantity: 3,
      };
      
      jest.spyOn(cartItemRepository, 'findById').mockResolvedValue(mockCartItem);
      jest.spyOn(cartRepository, 'findOrCreateCart').mockResolvedValue({ ...mockCart, id: 1 });
      jest.spyOn(cartItemRepository, 'updateQuantity').mockResolvedValue(mockUpdatedCartItem);

      // Act
      const result = await service.updateCartItem(cartItemId, userId, updateCartItemDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.cartItemId).toBe(mockUpdatedCartItem.id);
      expect(cartItemRepository.findById).toHaveBeenCalledWith(cartItemId);
      expect(cartRepository.findOrCreateCart).toHaveBeenCalledWith(userId);
      expect(cartItemRepository.updateQuantity).toHaveBeenCalledWith(cartItemId, updateCartItemDto.quantity);
    });

    it('phải ném ra ngoại lệ khi không tìm thấy sản phẩm trong giỏ hàng', async () => {
      // Arrange
      const cartItemId = 999;
      const userId = 1;
      const updateCartItemDto: UpdateCartItemDto = {
        quantity: 3,
      };
      
      jest.spyOn(cartItemRepository, 'findById').mockResolvedValue(null);

      // Act & Assert
      await expect(service.updateCartItem(cartItemId, userId, updateCartItemDto)).rejects.toThrow(AppException);
      expect(cartItemRepository.findById).toHaveBeenCalledWith(cartItemId);
    });

    it('phải ném ra ngoại lệ khi sản phẩm không thuộc giỏ hàng của người dùng', async () => {
      // Arrange
      const cartItemId = 1;
      const userId = 1;
      const updateCartItemDto: UpdateCartItemDto = {
        quantity: 3,
      };
      const mockCartItem = {
        id: 1,
        cartId: 1,
        productId: 1,
        quantity: 2,
        cart_id: 1,
      };
      
      jest.spyOn(cartItemRepository, 'findById').mockResolvedValue(mockCartItem);
      jest.spyOn(cartRepository, 'findOrCreateCart').mockResolvedValue({ ...mockCart, id: 2 }); // Khác cart_id

      // Act & Assert
      await expect(service.updateCartItem(cartItemId, userId, updateCartItemDto)).rejects.toThrow(AppException);
      expect(cartItemRepository.findById).toHaveBeenCalledWith(cartItemId);
      expect(cartRepository.findOrCreateCart).toHaveBeenCalledWith(userId);
    });
  });

  describe('removeFromCart', () => {
    it('phải xóa sản phẩm khỏi giỏ hàng', async () => {
      // Arrange
      const cartItemId = 1;
      const userId = 1;
      const mockCartItem = {
        id: 1,
        cartId: 1,
        productId: 1,
        quantity: 2,
        cart_id: 1,
      };
      
      jest.spyOn(cartItemRepository, 'findById').mockResolvedValue(mockCartItem);
      jest.spyOn(cartRepository, 'findOrCreateCart').mockResolvedValue({ ...mockCart, id: 1 });
      jest.spyOn(cartItemRepository, 'remove').mockResolvedValue(true);

      // Act
      const result = await service.removeFromCart(cartItemId, userId);

      // Assert
      expect(result).toBeDefined();
      expect(result.message).toBe('Đã xóa sản phẩm khỏi giỏ hàng');
      expect(cartItemRepository.findById).toHaveBeenCalledWith(cartItemId);
      expect(cartRepository.findOrCreateCart).toHaveBeenCalledWith(userId);
      expect(cartItemRepository.remove).toHaveBeenCalledWith(mockCartItem);
    });

    it('phải ném ra ngoại lệ khi không tìm thấy sản phẩm trong giỏ hàng', async () => {
      // Arrange
      const cartItemId = 999;
      const userId = 1;
      
      jest.spyOn(cartItemRepository, 'findById').mockResolvedValue(null);

      // Act & Assert
      await expect(service.removeFromCart(cartItemId, userId)).rejects.toThrow(AppException);
      expect(cartItemRepository.findById).toHaveBeenCalledWith(cartItemId);
    });
  });
});
