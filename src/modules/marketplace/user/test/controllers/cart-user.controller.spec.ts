import { Test, TestingModule } from '@nestjs/testing';
import { CartUserController } from '@modules/marketplace/user/controllers/cart-user.controller';
import { CartUserService } from '@modules/marketplace/user/services/cart-user.service';
import { mockCartUserService } from '../__mocks__/service.mock';
import { mockCartResponseDto, mockAddToCartResult, mockUpdateCartResult, mockRemoveFromCartResult } from '../__mocks__/cart.mock';
import { QueryCartDto, AddToCartDto, UpdateCartItemDto } from '@modules/marketplace/user/dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { AppException } from '../__mocks__/@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '../__mocks__/@modules/marketplace/exceptions';

describe('Controller quản lý giỏ hàng (User)', () => {
  let controller: CartUserController;
  let service: CartUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CartUserController],
      providers: [
        {
          provide: CartUserService,
          useValue: mockCartUserService,
        },
      ],
    })
      .overrideGuard(JwtUserGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<CartUserController>(CartUserController);
    service = module.get<CartUserService>(CartUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getCart', () => {
    it('phải trả về thông tin giỏ hàng', async () => {
      // Arrange
      const queryDto: QueryCartDto = {
        search: '',
        sortBy: 'createdAt',
        sortDirection: 'DESC',
      };
      const userId = 1;
      jest.spyOn(service, 'getCart').mockResolvedValue(mockCartResponseDto);

      // Act
      const result = await controller.getCart(userId, queryDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockCartResponseDto);
      expect(service.getCart).toHaveBeenCalledWith(userId, queryDto);
    });

    it('phải xử lý lỗi khi service ném ra ngoại lệ', async () => {
      // Arrange
      const queryDto: QueryCartDto = {
        search: '',
        sortBy: 'createdAt',
        sortDirection: 'DESC',
      };
      const userId = 1;
      const error = new AppException(MARKETPLACE_ERROR_CODES.CART_NOT_FOUND, 'Không tìm thấy giỏ hàng');
      jest.spyOn(service, 'getCart').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getCart(userId, queryDto)).rejects.toThrow(AppException);
    });
  });

  describe('addToCart', () => {
    it('phải thêm sản phẩm vào giỏ hàng', async () => {
      // Arrange
      const addToCartDto: AddToCartDto = {
        productId: 1,
        quantity: 2,
      };
      const userId = 1;
      jest.spyOn(service, 'addToCart').mockResolvedValue(mockAddToCartResult);

      // Act
      const result = await controller.addToCart(userId, addToCartDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockAddToCartResult);
      expect(service.addToCart).toHaveBeenCalledWith(userId, addToCartDto);
    });

    it('phải xử lý lỗi khi service ném ra ngoại lệ', async () => {
      // Arrange
      const addToCartDto: AddToCartDto = {
        productId: 999,
        quantity: 2,
      };
      const userId = 1;
      const error = new AppException(MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND, 'Không tìm thấy sản phẩm');
      jest.spyOn(service, 'addToCart').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.addToCart(userId, addToCartDto)).rejects.toThrow(AppException);
    });
  });

  describe('updateCartItem', () => {
    it('phải cập nhật số lượng sản phẩm trong giỏ hàng', async () => {
      // Arrange
      const cartItemId = 1;
      const updateCartItemDto: UpdateCartItemDto = {
        quantity: 3,
      };
      const userId = 1;
      jest.spyOn(service, 'updateCartItem').mockResolvedValue(mockUpdateCartResult);

      // Act
      const result = await controller.updateCartItem(cartItemId, userId, updateCartItemDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockUpdateCartResult);
      expect(service.updateCartItem).toHaveBeenCalledWith(cartItemId, userId, updateCartItemDto);
    });

    it('phải xử lý lỗi khi service ném ra ngoại lệ', async () => {
      // Arrange
      const cartItemId = 999;
      const updateCartItemDto: UpdateCartItemDto = {
        quantity: 3,
      };
      const userId = 1;
      const error = new AppException(MARKETPLACE_ERROR_CODES.CART_ITEM_NOT_FOUND, 'Không tìm thấy sản phẩm trong giỏ hàng');
      jest.spyOn(service, 'updateCartItem').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.updateCartItem(cartItemId, userId, updateCartItemDto)).rejects.toThrow(AppException);
    });
  });

  describe('removeFromCart', () => {
    it('phải xóa sản phẩm khỏi giỏ hàng', async () => {
      // Arrange
      const cartItemId = 1;
      const userId = 1;
      jest.spyOn(service, 'removeFromCart').mockResolvedValue(mockRemoveFromCartResult);

      // Act
      const result = await controller.removeFromCart(cartItemId, userId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockRemoveFromCartResult);
      expect(service.removeFromCart).toHaveBeenCalledWith(cartItemId, userId);
    });

    it('phải xử lý lỗi khi service ném ra ngoại lệ', async () => {
      // Arrange
      const cartItemId = 999;
      const userId = 1;
      const error = new AppException(MARKETPLACE_ERROR_CODES.CART_ITEM_NOT_FOUND, 'Không tìm thấy sản phẩm trong giỏ hàng');
      jest.spyOn(service, 'removeFromCart').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.removeFromCart(cartItemId, userId)).rejects.toThrow(AppException);
    });
  });
});
