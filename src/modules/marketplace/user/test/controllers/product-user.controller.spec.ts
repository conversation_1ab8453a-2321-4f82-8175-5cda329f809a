import { Test, TestingModule } from '@nestjs/testing';
import { ProductUserController } from '@modules/marketplace/user/controllers/product-user.controller';
import { ProductUserService } from '@modules/marketplace/user/services/product-user.service';
import { mockProductUserService } from '../__mocks__/service.mock';
import { mockUserProductResponseDto, mockProductDetailResponseDto, mockUserProductDetailResponseDto, mockPaginatedUserProductResponseDto, mockCreateProductResult, mockUpdateProductResult } from '../__mocks__/product.mock';
import { QueryUserProductDto, CreateProductDto, UpdateProductDto } from '@modules/marketplace/user/dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';
import { AppException } from '../__mocks__/@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '../__mocks__/@modules/marketplace/exceptions';

describe('Controller quản lý sản phẩm (User)', () => {
  let controller: ProductUserController;
  let service: ProductUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProductUserController],
      providers: [
        {
          provide: ProductUserService,
          useValue: mockProductUserService,
        },
      ],
    })
      .overrideGuard(JwtUserGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<ProductUserController>(ProductUserController);
    service = module.get<ProductUserService>(ProductUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getApprovedProducts', () => {
    it('phải trả về danh sách sản phẩm được phê duyệt', async () => {
      // Arrange
      const queryDto: QueryUserProductDto = {
        page: 1,
        limit: 10,
      };
      const userId = 1;
      jest.spyOn(service, 'getApprovedProducts').mockResolvedValue(mockPaginatedUserProductResponseDto);

      // Act
      const result = await controller.getApprovedProducts(userId, queryDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockPaginatedUserProductResponseDto);
      expect(service.getApprovedProducts).toHaveBeenCalledWith(queryDto, userId);
    });

    it('phải xử lý lỗi khi service ném ra ngoại lệ', async () => {
      // Arrange
      const queryDto: QueryUserProductDto = {
        page: 1,
        limit: 10,
      };
      const userId = 1;
      const error = new AppException(MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND, 'Không tìm thấy sản phẩm');
      jest.spyOn(service, 'getApprovedProducts').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getApprovedProducts(userId, queryDto)).rejects.toThrow(AppException);
    });
  });

  describe('getProductDetail', () => {
    it('phải trả về chi tiết sản phẩm', async () => {
      // Arrange
      const productId = 1;
      const userId = 1;
      jest.spyOn(service, 'getProductDetail').mockResolvedValue(mockProductDetailResponseDto);

      // Act
      const result = await controller.getProductDetail(productId, userId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockProductDetailResponseDto);
      expect(service.getProductDetail).toHaveBeenCalledWith(productId, userId);
    });

    it('phải xử lý lỗi khi service ném ra ngoại lệ', async () => {
      // Arrange
      const productId = 999;
      const userId = 1;
      const error = new AppException(MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND, 'Không tìm thấy sản phẩm');
      jest.spyOn(service, 'getProductDetail').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getProductDetail(productId, userId)).rejects.toThrow(AppException);
    });
  });

  describe('getUserProducts', () => {
    it('phải trả về danh sách sản phẩm của người dùng', async () => {
      // Arrange
      const queryDto: QueryUserProductDto = {
        page: 1,
        limit: 10,
      };
      const userId = 1;
      jest.spyOn(service, 'getUserProducts').mockResolvedValue(mockPaginatedUserProductResponseDto);

      // Act
      const result = await controller.getUserProducts(userId, queryDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockPaginatedUserProductResponseDto);
      expect(service.getUserProducts).toHaveBeenCalledWith(userId, queryDto);
    });
  });

  describe('getUserProductDetail', () => {
    it('phải trả về chi tiết sản phẩm của người dùng', async () => {
      // Arrange
      const productId = 1;
      const userId = 1;
      jest.spyOn(service, 'getUserProductDetail').mockResolvedValue(mockUserProductDetailResponseDto);

      // Act
      const result = await controller.getUserProductDetail(productId, userId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockUserProductDetailResponseDto);
      expect(service.getUserProductDetail).toHaveBeenCalledWith(productId, userId);
    });
  });

  describe('createProduct', () => {
    it('phải tạo sản phẩm mới', async () => {
      // Arrange
      const createDto: CreateProductDto = {
        name: 'Sản phẩm mới',
        description: 'Mô tả sản phẩm mới',
        listedPrice: 1000,
        discountedPrice: 800,
        category: ProductCategory.AGENT,
        imagesMediaTypes: ['image/jpeg'],
        userManualMediaType: 'application/pdf',
        detailMediaType: 'application/pdf',
        sourceId: 'source-123',
      };
      const userId = 1;
      jest.spyOn(service, 'createProduct').mockResolvedValue(mockCreateProductResult);

      // Act
      const result = await controller.createProduct(userId, createDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockCreateProductResult);
      expect(service.createProduct).toHaveBeenCalledWith(userId, createDto);
    });
  });

  describe('updateProduct', () => {
    it('phải cập nhật sản phẩm', async () => {
      // Arrange
      const productId = 1;
      const updateDto: UpdateProductDto = {
        name: 'Sản phẩm đã cập nhật',
        description: 'Mô tả đã cập nhật',
        listedPrice: 1200,
        discountedPrice: 1000,
        option: 'UPDATE_BASIC_INFO',
      };
      const userId = 1;
      jest.spyOn(service, 'updateProduct').mockResolvedValue(mockUpdateProductResult);

      // Act
      const result = await controller.updateProduct(productId, userId, updateDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockUpdateProductResult);
      expect(service.updateProduct).toHaveBeenCalledWith(productId, userId, updateDto);
    });
  });

  describe('submitProduct', () => {
    it('phải gửi sản phẩm để duyệt', async () => {
      // Arrange
      const productId = 1;
      const userId = 1;
      jest.spyOn(service, 'submitProduct').mockResolvedValue({ id: 1, status: 'PENDING_APPROVAL' });

      // Act
      const result = await controller.submitProduct(productId, userId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual({ id: 1, status: 'PENDING_APPROVAL' });
      expect(service.submitProduct).toHaveBeenCalledWith(productId, userId);
    });
  });

  describe('deleteProduct', () => {
    it('phải xóa sản phẩm', async () => {
      // Arrange
      const productId = 1;
      const userId = 1;
      jest.spyOn(service, 'deleteProduct').mockResolvedValue({ id: 1, status: 'DELETED' });

      // Act
      const result = await controller.deleteProduct(productId, userId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual({ id: 1, status: 'DELETED' });
      expect(service.deleteProduct).toHaveBeenCalledWith(productId, userId);
    });
  });

  describe('createImageUploadUrl', () => {
    it('phải tạo URL tải lên hình ảnh', async () => {
      // Arrange
      const userId = 1;
      const mockResult = {
        url: 'https://presigned-url.com/image',
        key: 'marketplace/image/product-image-123.jpg',
      };
      jest.spyOn(service, 'createImageUploadUrl').mockResolvedValue(mockResult);

      // Act
      const result = await controller.createImageUploadUrl(userId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockResult);
      expect(service.createImageUploadUrl).toHaveBeenCalled();
    });
  });

  describe('createDocumentUploadUrl', () => {
    it('phải tạo URL tải lên tài liệu', async () => {
      // Arrange
      const userId = 1;
      const mockResult = {
        url: 'https://presigned-url.com/document',
        key: 'marketplace/document/product-document-123.pdf',
      };
      jest.spyOn(service, 'createDocumentUploadUrl').mockResolvedValue(mockResult);

      // Act
      const result = await controller.createDocumentUploadUrl(userId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockResult);
      expect(service.createDocumentUploadUrl).toHaveBeenCalled();
    });
  });
});
