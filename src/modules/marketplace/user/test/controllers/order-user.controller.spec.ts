import { Test, TestingModule } from '@nestjs/testing';
import { OrderUserController } from '@modules/marketplace/user/controllers/order-user.controller';
import { OrderUserService } from '@modules/marketplace/user/services/order-user.service';
import { mockOrderUserService } from '../__mocks__/service.mock';
import { mockCombinedPurchaseHistoryResponseDto } from '../__mocks__/order.mock';
import { QueryPurchaseHistoryDto } from '@modules/marketplace/user/dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { AppException } from '../__mocks__/@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '../__mocks__/@modules/marketplace/exceptions';

describe('Controller quản lý đơn hàng (User)', () => {
  let controller: OrderUserController;
  let service: OrderUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrderUserController],
      providers: [
        {
          provide: OrderUserService,
          useValue: mockOrderUserService,
        },
      ],
    })
      .overrideGuard(JwtUserGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<OrderUserController>(OrderUserController);
    service = module.get<OrderUserService>(OrderUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getPurchaseHistory', () => {
    it('phải trả về lịch sử mua hàng', async () => {
      // Arrange
      const queryDto: QueryPurchaseHistoryDto = {
        page: 1,
        limit: 10,
        search: '',
        sortBy: 'purchaseDate',
        sortDirection: 'DESC',
      };
      const userId = 1;
      jest.spyOn(service, 'getPurchaseHistory').mockResolvedValue(mockCombinedPurchaseHistoryResponseDto);

      // Act
      const result = await controller.getPurchaseHistory(userId, queryDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockCombinedPurchaseHistoryResponseDto);
      expect(service.getPurchaseHistory).toHaveBeenCalledWith(userId, queryDto);
    });

    it('phải xử lý lỗi khi service ném ra ngoại lệ', async () => {
      // Arrange
      const queryDto: QueryPurchaseHistoryDto = {
        page: 1,
        limit: 10,
        search: '',
        sortBy: 'purchaseDate',
        sortDirection: 'DESC',
      };
      const userId = 1;
      const error = new AppException(MARKETPLACE_ERROR_CODES.ORDER_NOT_FOUND, 'Không tìm thấy đơn hàng');
      jest.spyOn(service, 'getPurchaseHistory').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getPurchaseHistory(userId, queryDto)).rejects.toThrow(AppException);
    });
  });
});
