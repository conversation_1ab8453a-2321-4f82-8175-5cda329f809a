import { Test, TestingModule } from '@nestjs/testing';
import { PaymentController } from '@modules/marketplace/user/controllers/payment.controller';
import { PaymentService } from '@modules/marketplace/user/services/payment.service';
import { mockPaymentService } from '../__mocks__/service.mock';
import { PaymentDto } from '@modules/marketplace/user/dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { AppException } from '../__mocks__/@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '../__mocks__/@modules/marketplace/exceptions';

describe('Controller thanh toán (User)', () => {
  let controller: PaymentController;
  let service: PaymentService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PaymentController],
      providers: [
        {
          provide: PaymentService,
          useValue: mockPaymentService,
        },
      ],
    })
      .overrideGuard(JwtUserGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<PaymentController>(PaymentController);
    service = module.get<PaymentService>(PaymentService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('processPayment', () => {
    it('phải xử lý thanh toán thành công', async () => {
      // Arrange
      const paymentDto: PaymentDto = {
        productIds: [1, 2],
      };
      const userId = 1;
      const mockPaymentResult = {
        orderId: 1,
        totalPoint: 1600,
        products: [
          {
            id: 1,
            name: 'Sản phẩm 1',
            price: 800,
            quantity: 2,
          },
        ],
      };
      jest.spyOn(service, 'processPayment').mockResolvedValue(mockPaymentResult);

      // Act
      const result = await controller.processPayment(userId, paymentDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(mockPaymentResult);
      expect(service.processPayment).toHaveBeenCalledWith(userId, paymentDto);
    });

    it('phải xử lý lỗi khi service ném ra ngoại lệ', async () => {
      // Arrange
      const paymentDto: PaymentDto = {
        productIds: [999],
      };
      const userId = 1;
      const error = new AppException(MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND, 'Không tìm thấy sản phẩm');
      jest.spyOn(service, 'processPayment').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.processPayment(userId, paymentDto)).rejects.toThrow(AppException);
    });

    it('phải xử lý lỗi khi không đủ điểm', async () => {
      // Arrange
      const paymentDto: PaymentDto = {
        productIds: [1, 2],
      };
      const userId = 1;
      const error = new AppException(MARKETPLACE_ERROR_CODES.INSUFFICIENT_POINTS, 'Không đủ R-Point để thanh toán');
      jest.spyOn(service, 'processPayment').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.processPayment(userId, paymentDto)).rejects.toThrow(AppException);
      expect(service.processPayment).toHaveBeenCalledWith(userId, paymentDto);
    });

    it('phải xử lý lỗi khi sản phẩm đã được mua', async () => {
      // Arrange
      const paymentDto: PaymentDto = {
        productIds: [1],
      };
      const userId = 1;
      const error = new AppException(MARKETPLACE_ERROR_CODES.PRODUCT_ALREADY_PURCHASED, 'Sản phẩm đã được mua');
      jest.spyOn(service, 'processPayment').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.processPayment(userId, paymentDto)).rejects.toThrow(AppException);
      expect(service.processPayment).toHaveBeenCalledWith(userId, paymentDto);
    });
  });
});
