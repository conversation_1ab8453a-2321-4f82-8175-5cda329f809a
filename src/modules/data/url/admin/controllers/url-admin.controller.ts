import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery, ApiResponse as SwaggerApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { ApiResponseDto as ApiResponse } from '@common/response/api-response-dto';
import { UrlSchema, UrlListResponseSchema } from '../../schemas/url.schema';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { UrlAdminService } from '../services/url-admin.service';
import { FindAllUrlAdminDto } from '../dto/find-all-url-admin.dto';
import { CreateUrlAdminDto } from '../dto/create-url-admin.dto';
import { UpdateUrlAdminDto } from '../dto/update-url-admin.dto';
import { CrawlAdminDto } from '../dto/crawl-admin.dto';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

/**
 * Controller xử lý các endpoint liên quan đến URL cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_URL)
@ApiSecurity('JWT-auth')
@Controller('data/admin/url')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
export class UrlAdminController {
  private readonly logger = new Logger(UrlAdminController.name);

  constructor(private readonly urlAdminService: UrlAdminService) {}

  /**
   * Lấy danh sách URL với phân trang và tìm kiếm
   */
  @ApiOperation({ summary: 'Lấy danh sách URL với phân trang và tìm kiếm' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng bản ghi trên mỗi trang', type: Number })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Trường cần sắp xếp', type: String })
  @ApiQuery({ name: 'sortDirection', required: false, description: 'Hướng sắp xếp (ASC hoặc DESC)', type: String })
  @ApiQuery({ name: 'keyword', required: false, description: 'Từ khóa tìm kiếm', type: String })
  @ApiQuery({ name: 'type', required: false, description: 'Loại URL cần lọc', type: String })
  @ApiQuery({ name: 'tags', required: false, description: 'Các thẻ cần lọc (có thể dùng dấu phẩy để phân tách nhiều thẻ, ví dụ: tags=nestjs,tutorial)', type: String })
  @ApiQuery({ name: 'userId', required: false, description: 'ID người dùng sở hữu URL', type: Number })
  @ApiQuery({ name: 'isActive', required: false, description: 'Trạng thái kích hoạt', type: Boolean })
  @SwaggerApiResponse({ status: 200, description: 'Danh sách URL', type: UrlListResponseSchema })
  @Get()
  async findAll(@Query() queryParams: FindAllUrlAdminDto) {
    this.logger.log(`Finding all URLs with params: ${JSON.stringify(queryParams)}`);

    const result = await this.urlAdminService.findAllUrls(queryParams);

    this.logger.log(`Found ${result.items.length} URLs`);

    return ApiResponse.success(result);
  }

  /**
   * Lấy thông tin chi tiết URL theo ID
   */
  @ApiOperation({ summary: 'Lấy thông tin chi tiết URL theo ID' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'Thông tin chi tiết URL', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    this.logger.log(`Finding URL with ID: ${id}`);

    const result = await this.urlAdminService.findUrlById(id);

    this.logger.log(`URL with ID: ${id} found`);

    return ApiResponse.success(result);
  }

  /**
   * Tạo URL mới
   */
  @ApiOperation({ summary: 'Tạo URL mới' })
  @SwaggerApiResponse({ status: 201, description: 'URL đã được tạo thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @Post()
  async create(@Body() createUrlDto: CreateUrlAdminDto) {
    this.logger.log(`Creating new URL`);
    this.logger.debug(`URL data: ${JSON.stringify(createUrlDto)}`);

    const result = await this.urlAdminService.createUrl(createUrlDto);

    this.logger.log(`URL created successfully with ID: ${result.id}`);

    return ApiResponse.created(result, 'URL đã được tạo thành công');
  }

  /**
   * Cập nhật thông tin URL
   */
  @ApiOperation({ summary: 'Cập nhật thông tin URL' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'URL đã được cập nhật thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateUrlDto: UpdateUrlAdminDto
  ) {
    this.logger.log(`Updating URL with ID: ${id}`);
    this.logger.debug(`Update data: ${JSON.stringify(updateUrlDto)}`);

    const result = await this.urlAdminService.updateUrl(id, updateUrlDto);

    this.logger.log(`URL with ID: ${id} updated successfully`);

    return ApiResponse.success(result, 'URL đã được cập nhật thành công');
  }

  /**
   * Xóa URL
   */
  @ApiOperation({ summary: 'Xóa URL' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'URL đã được xóa thành công' })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Delete(':id')
  async remove(@Param('id') id: string) {
    this.logger.log(`Deleting URL with ID: ${id}`);

    await this.urlAdminService.deleteUrl(id);

    this.logger.log(`URL with ID: ${id} deleted successfully`);

    return ApiResponse.success(null, 'URL đã được xóa thành công');
  }

  /**
   * Cập nhật trạng thái kích hoạt của URL
   */
  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt của URL' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @ApiParam({ name: 'isActive', description: 'Trạng thái kích hoạt mới', type: Boolean })
  @SwaggerApiResponse({ status: 200, description: 'Trạng thái URL đã được cập nhật thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Patch(':id/status/:isActive')
  async updateStatus(
    @Param('id') id: string,
    @Param('isActive') isActive: string
  ) {
    // Xử lý nhiều giá trị có thể có để chuyển đổi sang boolean
    const isActiveBoolean = ['true', '1', 'yes', 'on'].includes(isActive.toLowerCase());
    this.logger.log(`Updating URL status with ID: ${id}, isActive: ${isActiveBoolean}`);

    const result = await this.urlAdminService.updateUrlStatus(id, isActiveBoolean);

    this.logger.log(`URL status with ID: ${id} updated successfully`);

    return ApiResponse.success(result, 'Trạng thái URL đã được cập nhật thành công');
  }

  /**
   * Đảo ngược trạng thái kích hoạt của URL (toggle) - Endpoint 1
   */
  @ApiOperation({ summary: 'Đảo ngược trạng thái kích hoạt của URL (toggle)' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'Trạng thái URL đã được đảo ngược thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Patch(':id/status/toggle')
  async toggleStatus(
    @Param('id') id: string
  ) {
    this.logger.log(`Toggling URL status with ID: ${id}`);

    const result = await this.urlAdminService.toggleUrlStatus(id);

    this.logger.log(`URL status with ID: ${id} toggled successfully to: ${result.isActive}`);

    return ApiResponse.success(result, `Trạng thái URL đã được đảo ngược thành công. Trạng thái mới: ${result.isActive ? 'Kích hoạt' : 'Vô hiệu hóa'}`);
  }

  /**
   * Đảo ngược trạng thái kích hoạt của URL (toggle) - Endpoint 2
   */
  @ApiOperation({ summary: 'Đảo ngược trạng thái kích hoạt của URL (toggle)' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'Trạng thái URL đã được đảo ngược thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Patch(':id/toggle-status')
  async toggleStatusAlt(
    @Param('id') id: string
  ) {
    this.logger.log(`Toggling URL status with ID: ${id} (alternative endpoint)`);

    const result = await this.urlAdminService.toggleUrlStatus(id);

    this.logger.log(`URL status with ID: ${id} toggled successfully to: ${result.isActive}`);

    return ApiResponse.success(result, `Trạng thái URL đã được đảo ngược thành công. Trạng thái mới: ${result.isActive ? 'Kích hoạt' : 'Vô hiệu hóa'}`);
  }

  /**
   * Đảo ngược trạng thái kích hoạt của URL (toggle) - Endpoint 3
   */
  @ApiOperation({ summary: 'Đảo ngược trạng thái kích hoạt của URL (toggle)' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'Trạng thái URL đã được đảo ngược thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Post(':id/toggle')
  async toggleStatusPost(
    @Param('id') id: string
  ) {
    this.logger.log(`Toggling URL status with ID: ${id} (POST method)`);

    const result = await this.urlAdminService.toggleUrlStatus(id);

    this.logger.log(`URL status with ID: ${id} toggled successfully to: ${result.isActive}`);

    return ApiResponse.success(result, `Trạng thái URL đã được đảo ngược thành công. Trạng thái mới: ${result.isActive ? 'Kích hoạt' : 'Vô hiệu hóa'}`);
  }

  /**
   * Crawl URL và các URL con để lấy metadata
   */
  @ApiOperation({ summary: 'Crawl URL và các URL con để lấy metadata' })
  @SwaggerApiResponse({ status: 200, description: 'Crawl URL thành công' })
  @SwaggerApiResponse({ status: 400, description: 'Lỗi khi crawl URL' })
  @Post('crawl')
  async crawlUrl(
    @CurrentEmployee('id') employeeId: number,
    @Body() crawlDto: CrawlAdminDto
  ) {
    this.logger.log(`Crawling URL: ${crawlDto.url} with depth: ${crawlDto.depth}`);

    const result = await this.urlAdminService.crawlUrl(employeeId, crawlDto);

    this.logger.log(`Crawl URL completed: ${result.urlsProcessed} URLs processed, status: ${result.status}`);
    return ApiResponse.success(result, result.message);
  }
}
