import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { UrlUserController } from './controllers/url-user.controller';
import { Url } from '../entities/url.entity';
import { UrlMetadata } from './entities/url-metadata.entity';
import { UrlRepository } from '../repositories';
import { UrlMetadataRepository } from './repositories/url-metadata.repository';
import { AuthModule } from '../../../../modules/auth/auth.module';
import { RedisService } from '../../../../shared/services/redis.service';
import { UrlUserService } from './services/url-user.service';
import { ServicesModule } from '../../../../shared/services/services.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Url, UrlMetadata]),
    AuthModule,
    HttpModule,
    ServicesModule
  ],
  controllers: [UrlUserController],
  providers: [UrlUserService, UrlRepository, UrlMetadataRepository, RedisService],
  exports: [UrlUserService],
})
export class UrlUserModule {}
