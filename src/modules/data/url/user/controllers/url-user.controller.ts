import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  UseGuards
} from '@nestjs/common';
import { SortDirection } from '@common/dto/query.dto';
import { ApiOperation, ApiParam, ApiQuery, ApiResponse as SwaggerApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { ApiResponseDto as ApiResponse } from '@common/response/api-response-dto';
import { UrlSchema, UrlListResponseSchema } from '../../schemas/url.schema';
import { CreateUrlDto } from '../../schemas/create-url.dto';
import { UpdateUrlDto } from '../../schemas/update-url.dto';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { UrlUserService } from '../services/url-user.service';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { FindAllUrlDto } from '../dto/find-all-url.dto';
import { CrawlDto } from '../dto/crawl.dto';

/**
 * Controller xử lý các endpoint liên quan đến URL cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_URL)
@ApiSecurity('JWT-auth')
@Controller('data/url')
@UseGuards(JwtUserGuard)
export class UrlUserController {
  private readonly logger = new Logger(UrlUserController.name);

  constructor(private readonly urlUserService: UrlUserService) {}

  /**
   * Lấy danh sách URL của người dùng với phân trang và tìm kiếm
   */
  @ApiOperation({ summary: 'Lấy danh sách URL của người dùng' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng kết quả mỗi trang', type: Number })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Trường sắp xếp', type: String })
  @ApiQuery({ name: 'sortDirection', required: false, description: 'Hướng sắp xếp', enum: ['ASC', 'DESC'] })
  @ApiQuery({ name: 'keyword', required: false, description: 'Từ khóa tìm kiếm', type: String })
  @ApiQuery({ name: 'type', required: false, description: 'Loại URL cần lọc', type: String })
  @ApiQuery({ name: 'tags', required: false, description: 'Các thẻ cần lọc (có thể dùng dấu phẩy để phân tách nhiều thẻ, ví dụ: tags=nestjs,tutorial)', type: String })
  @SwaggerApiResponse({ status: 200, description: 'Danh sách URL', type: UrlListResponseSchema })
  @Get()
  async findAll(
    @CurrentUser() user: any,
    @Query() queryParams: FindAllUrlDto
  ) {
    const userId = user.id;
    this.logger.log(`Finding all URLs for user: ${userId}, page: ${queryParams.page}, limit: ${queryParams.limit}`);

    // Convert string parameters to appropriate types if needed
    const page = queryParams.page || 1;
    const limit = queryParams.limit || 10;
    const sortBy = queryParams.sortBy || 'createdAt';
    const sortDirection = queryParams.sortDirection || SortDirection.DESC;

    // Đảm bảo tags luôn là mảng
    const tags = queryParams.tags || [];

    const result = await this.urlUserService.findUrlsByOwner(
      userId,
      page,
      limit,
      sortBy,
      sortDirection,
      queryParams.keyword,
      queryParams.type,
      tags
    );

    this.logger.log(`Found ${result.items.length} URLs for user: ${userId}`);

    return ApiResponse.success(result);
  }

  /**
   * Lấy thông tin chi tiết URL theo ID
   */
  @ApiOperation({ summary: 'Lấy thông tin chi tiết URL theo ID' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'Thông tin chi tiết URL', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Get(':id')
  async findOne(
    @CurrentUser() user: any,
    @Param('id') id: string
  ) {
    const userId = user.id;
    this.logger.log(`Finding URL with ID: ${id} for user: ${userId}`);

    const result = await this.urlUserService.findUrlById(userId, id);

    this.logger.log(`URL with ID: ${id} found for user: ${userId}`);

    return ApiResponse.success(result);
  }

  /**
   * Tạo URL mới
   */
  @ApiOperation({ summary: 'Tạo URL mới' })
  @SwaggerApiResponse({ status: 201, description: 'URL đã được tạo thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @Post()
  async create(
    @CurrentUser() user: any,
    @Body() createUrlDto: CreateUrlDto
  ) {
    const userId = user.id;
    this.logger.log(`Creating new URL for user: ${userId}`);
    this.logger.debug(`URL data: ${JSON.stringify(createUrlDto)}`);

    const result = await this.urlUserService.createUrl(userId, createUrlDto);

    this.logger.log(`URL created successfully with ID: ${result.id}`);

    return ApiResponse.created(result, 'URL đã được tạo thành công');
  }

  /**
   * Cập nhật thông tin URL
   */
  @ApiOperation({ summary: 'Cập nhật thông tin URL' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'URL đã được cập nhật thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Put(':id')
  async update(
    @CurrentUser() user: any,
    @Param('id') id: string,
    @Body() updateUrlDto: UpdateUrlDto
  ) {
    const userId = user.id;
    this.logger.log(`Updating URL with ID: ${id} for user: ${userId}`);
    this.logger.debug(`Update data: ${JSON.stringify(updateUrlDto)}`);

    const result = await this.urlUserService.updateUrl(id, userId, updateUrlDto);

    this.logger.log(`URL with ID: ${id} updated successfully`);

    return ApiResponse.success(result, 'URL đã được cập nhật thành công');
  }

  /**
   * Xóa URL
   */
  @ApiOperation({ summary: 'Xóa URL' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'URL đã được xóa thành công' })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Delete(':id')
  async remove(
    @CurrentUser() user: any,
    @Param('id') id: string
  ) {
    const userId = user.id;
    this.logger.log(`Deleting URL with ID: ${id} for user: ${userId}`);

    await this.urlUserService.deleteUrl(id, userId);

    this.logger.log(`URL with ID: ${id} deleted successfully`);

    return ApiResponse.success(null, 'URL đã được xóa thành công');
  }


  /**
   * Tự động crawl URL theo độ sâu và lấy metadata từ thẻ head
   * @param user Thông tin người dùng hiện tại
   * @param crawlDto Thông tin URL cần crawl
   * @returns Kết quả crawl URL với metadata
   */
  @ApiOperation({
    summary: 'Tự động crawl URL theo độ sâu và lấy metadata',
    description: 'Crawl URL và lấy metadata (title, description, keywords) từ thẻ head'
  })
  @SwaggerApiResponse({ status: 200, description: 'Đã crawl URL thành công' })
  @SwaggerApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @Post('crawl')
  async crawlUrl(
    @CurrentUser() user: any,
    @Body() crawlDto: CrawlDto
  ) {
    const userId = user.id;
    this.logger.log(`Bắt đầu crawl URL cho user: ${userId}, URL: ${crawlDto.url}, độ sâu: ${crawlDto.depth}`);

    // Log các tùy chọn
    if (crawlDto.ignoreRobotsTxt) {
      this.logger.log(`Tùy chọn: Bỏ qua kiểm tra robots.txt`);
    }

    if (crawlDto.maxUrls) {
      this.logger.log(`Tùy chọn: Giới hạn số lượng URL tối đa: ${crawlDto.maxUrls}`);
    }

    // Gọi service để crawl URL
    const result = await this.urlUserService.crawlUrl(userId, crawlDto);

    // Tạo thông báo chi tiết hơn
    let message = '';
    if (result.status === 'error') {
      // Nếu có lỗi, hiển thị thông báo lỗi
      message = result.message;
      this.logger.error(`Crawl URL thất bại: ${message}`);

      // Nếu có danh sách lỗi chi tiết, hiển thị chúng
      if (result.errors && result.errors.length > 0) {
        this.logger.error(`Chi tiết lỗi:`);
        result.errors.forEach((error, index) => {
          this.logger.error(`${index + 1}. ${error}`);
        });
      }

      // Sử dụng success nhưng với status là error trong result
      return ApiResponse.success(result, message);
    } else if (result.urlsProcessed && result.urlsProcessed > 0) {
      // Nếu crawl thành công
      message = `Đã crawl thành công ${result.urlsProcessed} URL`;
      return ApiResponse.success(result, message);
    } else {
      // Nếu không crawl được URL nào
      message = 'Không crawl được URL nào.';

      // Nếu có danh sách lỗi chi tiết, thêm vào thông báo
      if (result.errors && result.errors.length > 0) {
        message += ' Lỗi: ' + result.errors[0];
        if (result.errors.length > 1) {
          message += ` và ${result.errors.length - 1} lỗi khác`;
        }
      }

      return ApiResponse.success(result, message);
    }
  }


}