import { QueryDto, SortDirection } from "@common/dto";
import { MediaStatusEnum } from '@modules/data/media/enums/media-status.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { Type } from "class-transformer";

/**
 * Lớp mở rộng QueryDto để bao gồm trường `status` cho việc lọc media theo trạng thái
 */
export class MediaQueryDto extends QueryDto {
    @ApiProperty({
        description: 'Trường cần sắp xếp',
        example: 'createdAt',
        default: 'createdAt',
        required: false,
    })
    @IsOptional()
    @IsString()
    sortBy: string = 'createdAt';

    @ApiProperty({
        description: 'Hướng sắp xếp',
        enum: SortDirection,
        example: SortDirection.DESC,
        default: SortDirection.DESC,
        required: false,
    })
    @IsOptional()
    @IsEnum(SortDirection)
    sortDirection: SortDirection = SortDirection.DESC;
    @ApiProperty({
        description: 'Trạng thái của media',
        enum: MediaStatusEnum,
        required: false,
    })
    @IsOptional()
    @IsEnum([MediaStatusEnum.DRAFT, MediaStatusEnum.APPROVED], {
        message: 'status chỉ có thể là DRAFT hoặc APPROVED'
    })
    @Type(() => String)
    status?: MediaStatusEnum;
}
