import { ApiProperty } from "@nestjs/swagger";
import { Media } from "../entities";
import { IsNotEmpty, IsString } from "class-validator";

export class AdminMediaResponseDto extends Media {
    @ApiProperty({
        description: 'Tên người đăng tải',
        example: '<PERSON>uyễn <PERSON>ăn <PERSON>'
    })
    @IsString()
    @IsNotEmpty()
    author?: string;

    @ApiProperty({
        description: 'Avatar của người đăng tải',
        example: 'https://example.com/avatar/123e4567-e89b-12d3-a456-426614174000.jpg'
    })
    @IsString()
    @IsNotEmpty()
    avatar?: string;
}