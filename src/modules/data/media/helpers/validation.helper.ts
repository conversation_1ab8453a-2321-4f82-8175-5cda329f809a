import { Injectable } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions';
import { Media } from '../entities/media.entity';
import { MEDIA_ERROR_CODES } from '../exception';
import { FileSizeEnum } from '@/shared/utils';

@Injectable()
export class MediaValidationHelper {
  // Các hàm validate ở đây
 /**
 * Validates that the media exists (is not null).
 * Acts as a type guard to narrow type from Media | null to Media.
 *
 * @param media The media object to validate
 * @param id The ID used to look up the media
 * @throws AppException if media is null
 */
validateMediaExists(media: Media, id: string): boolean {
    // Check if media is null
    if (!media) {
      throw new AppException(
        MEDIA_ERROR_CODES.NOT_FOUND,
        `Media with id ${id} does not exist`,
      );
    }
    return true;
  }
   /**
   * Validates that the request is performed by an admin.
   * 
   * @param isAdmin Whether the current user is an admin.
   * @throws AppException if the user is not an admin.
   */
   isAdmin(isAdmin: boolean | undefined): void {
    if (!isAdmin) {
      throw new AppException(
        MEDIA_ERROR_CODES.FORBIDDEN,
      );
    }
  }

  validateMediaExceptions(appException: AppException): void {
    if (appException instanceof AppException) {
        throw appException;
    }
    throw new AppException(
      ErrorCode.INTERNAL_SERVER_ERROR,
        'An unexpected error occurred',
    );
  }

  validateMediaArray(mediaArray: string[], errMsg: string): void {
    if (!Array.isArray(mediaArray) || mediaArray.length === 0) {
        throw new AppException(
            MEDIA_ERROR_CODES.BAD_REQUEST,
            errMsg,
        );
      }
  }

  validateMediaLength(length:number, errMsg: string): void {
    if (!length||length==0) { // 10MB
      throw new AppException(
        MEDIA_ERROR_CODES.BAD_REQUEST,
        errMsg,
    );
      }
  }

  validateMediaSize(
    size: number,
    maxSize: FileSizeEnum
  ): void {
    if (size > maxSize) { // 10MB
      throw new AppException(
        MEDIA_ERROR_CODES.BAD_REQUEST,
        `File size exceeds the maximum limit of (${maxSize / (1024 * 1024)} MB)`,
    );
      }
  }

  validateUserPermission(
    userId: number,
    ownedBy: number,

  ):boolean{
    return userId === ownedBy;
  }
}
