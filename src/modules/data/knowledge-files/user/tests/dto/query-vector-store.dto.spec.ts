import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryVectorStoreDto } from '../../dto/query-vector-store.dto';
import { SortDirection } from '@dto/query.dto';

describe('QueryVectorStoreDto', () => {
  it('nên xác thực DTO hợp lệ với tất cả các tham số', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      page: 1,
      limit: 10,
      search: 'vector store',
      sortBy: 'name',
      sortDirection: SortDirection.ASC,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với các tham số mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ chỉ với search', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      search: 'vector store',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ chỉ với sortBy', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      sortBy: 'name',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ chỉ với sortDirection', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      sortDirection: SortDirection.ASC,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi xác thực với page không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      page: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi xác thực với page nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      page: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi xác thực với limit không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      limit: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi xác thực với limit nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      limit: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi xác thực với sortBy không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      sortBy: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi xác thực với sortDirection không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      sortDirection: 'INVALID_DIRECTION',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });
});
