import { KnowledgeFileStatus } from './__mocks__/@modules/data/knowledge-files/enums/knowledge-file-status.enum';
import { OwnerType } from './__mocks__/@modules/email/entitys/enums/owner-type.enum';
import { FileTypeEnum } from './__mocks__/@shared/utils/file/file-media-type.util';
import { CategoryFolderEnum } from './__mocks__/@shared/utils/file';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { AppException } from './__mocks__/@shared/exceptions/app.exception';
import { KNOWLEDGE_FILE_ERROR_CODES } from './__mocks__/@modules/data/knowledge-files/user/exceptions';

export class KnowledgeFileUserServiceMock {
  // Phương thức tạo batch files
  async batchCreateFiles(dto, userId) {
    const files = dto.files;
    if (files.some(file => file.mime === 'invalid/mime')) {
      throw new BadRequestException('Loại file không được hỗ trợ');
    }

    if (files.some(file => file.name === 'error.pdf')) {
      throw new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR);
    }

    return {
      success: true,
      data: files.map((file, index) => ({
        id: `file${index + 1}`,
        name: file.name,
        storage: file.storage,
        status: KnowledgeFileStatus.DRAFT,
        ownerType: OwnerType.USER,
        ownedBy: userId,
        createdAt: Date.now(),
      })),
    };
  }

  // Phương thức lấy danh sách files
  async getFiles(queryDto, userId) {
    const mockFiles = [
      {
        id: 'file1',
        name: 'test.pdf',
        storageKey: 'knowledge/test.pdf',
        ownerType: OwnerType.USER,
        ownedBy: userId,
        storage: 1024,
        createdAt: Date.now(),
        status: KnowledgeFileStatus.DRAFT,
        extension: 'pdf',
        viewUrl: 'https://cdn.example.com/knowledge/test.pdf',
      },
      {
        id: 'file2',
        name: 'test.docx',
        storageKey: 'knowledge/test.docx',
        ownerType: OwnerType.USER,
        ownedBy: userId,
        storage: 2048,
        createdAt: Date.now(),
        status: KnowledgeFileStatus.DRAFT,
        extension: 'docx',
        viewUrl: 'https://cdn.example.com/knowledge/test.docx',
      },
    ];

    if (queryDto.search) {
      return {
        items: mockFiles.filter(file => file.name.includes(queryDto.search)),
        meta: {
          totalItems: mockFiles.filter(file => file.name.includes(queryDto.search)).length,
          itemsPerPage: queryDto.limit,
          currentPage: queryDto.page,
        },
      };
    }

    if (queryDto.extension) {
      return {
        items: mockFiles.filter(file => file.name.endsWith(queryDto.extension)),
        meta: {
          totalItems: mockFiles.filter(file => file.name.endsWith(queryDto.extension)).length,
          itemsPerPage: queryDto.limit,
          currentPage: queryDto.page,
        },
      };
    }

    if (queryDto.vectorStoreId === 'error') {
      throw new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR);
    }

    return {
      items: mockFiles,
      meta: {
        totalItems: mockFiles.length,
        itemsPerPage: queryDto.limit,
        currentPage: queryDto.page,
      },
    };
  }

  // Phương thức xóa file
  async deleteFile(fileId, userId) {
    if (fileId === 'non_existent') {
      throw new NotFoundException(`Không tìm thấy file với id ${fileId}`);
    }

    if (fileId === 'vector_store_error') {
      throw new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR, 'Lỗi khi xóa liên kết vector store');
    }

    if (fileId === 'openai_error') {
      throw new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR, 'Lỗi khi xóa file từ OpenAI');
    }

    if (fileId === 's3_error') {
      throw new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR, 'Lỗi khi xóa file từ S3');
    }

    if (fileId === 'repository_error') {
      throw new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR, 'Lỗi khi xóa file từ repository');
    }

    return { success: true };
  }

  // Các phương thức utility
  getFileExtensionFromMime(mimeType) {
    switch (mimeType) {
      case FileTypeEnum.PDF:
        return '.pdf';
      case FileTypeEnum.DOCX:
        return '.docx';
      case FileTypeEnum.TXT:
        return '.txt';
      default:
        throw new BadRequestException('Loại file không được hỗ trợ');
    }
  }

  getCategoryFromMimeType(mimeType) {
    switch (mimeType) {
      case FileTypeEnum.PDF:
      case FileTypeEnum.DOCX:
      case FileTypeEnum.TXT:
        return CategoryFolderEnum.DOCUMENT;
      default:
        throw new BadRequestException('Loại file không được hỗ trợ');
    }
  }

  async checkVectorStoreExists(vectorStoreId) {
    if (vectorStoreId === 'non_existent_vs') {
      throw new NotFoundException(`Không tìm thấy vector store với id ${vectorStoreId}`);
    }

    if (vectorStoreId === 'db_error') {
      throw new Error('Database connection error');
    }

    return { id: vectorStoreId, name: 'Test Vector Store' };
  }

  getSortColumn(sortField) {
    if (!sortField) {
      return 'createdAt';
    }

    const lowerCaseField = sortField.toLowerCase();
    switch (lowerCaseField) {
      case 'name':
        return 'name';
      case 'createdat':
        return 'createdAt';
      case 'storage':
        return 'storage';
      default:
        return 'createdAt';
    }
  }
}
