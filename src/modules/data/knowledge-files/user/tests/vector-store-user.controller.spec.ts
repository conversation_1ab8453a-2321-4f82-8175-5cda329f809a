import { Test, TestingModule } from '@nestjs/testing';
import { VectorStoreUserController } from '../controllers';
import { VectorStoreUserService } from '../services';
import { AssignFilesDto, AssignFilesResponseDto, CreateVectorStoreDto, QueryVectorStoreDto, VectorStoreResponseDto } from '../dto';
import { HttpStatus, NotFoundException } from '@nestjs/common';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions/app.exception';
// Import mock error codes instead of actual ones
import { KNOWLEDGE_FILE_ERROR_CODES } from './__mocks__/@modules/data/knowledge-files/exceptions';
import { JwtAuthGuard } from './__mocks__/@modules/auth/guards/jwt-auth.guard';
import { JwtUtilService } from './__mocks__/@modules/auth/services/jwt.util';
import { RedisService } from './__mocks__/@shared/services/redis.service';

describe('VectorStoreUserController', () => {
  let controller: VectorStoreUserController;
  let service: VectorStoreUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [VectorStoreUserController],
      providers: [
        {
          provide: VectorStoreUserService,
          useValue: {
            createVectorStore: jest.fn(),
            getVectorStores: jest.fn(),
            getVectorStoreDetail: jest.fn(),
            assignFilesToVectorStore: jest.fn(),
            removeFilesFromVectorStore: jest.fn(),
            deleteVectorStore: jest.fn(),
          },
        },
        {
          provide: JwtAuthGuard,
          useValue: {
            canActivate: jest.fn().mockReturnValue(true),
          },
        },
        {
          provide: JwtUtilService,
          useValue: {
            verifyToken: jest.fn().mockResolvedValue({ id: 1, email: '<EMAIL>' }),
            generateToken: jest.fn().mockResolvedValue('mock-token'),
          },
        },
        {
          provide: RedisService,
          useValue: {
            get: jest.fn().mockResolvedValue(null),
            set: jest.fn().mockResolvedValue(undefined),
            del: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    })
    .overrideGuard(JwtAuthGuard)
    .useValue({ canActivate: () => true })
    .compile();

    controller = module.get<VectorStoreUserController>(VectorStoreUserController);
    service = module.get<VectorStoreUserService>(VectorStoreUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createVectorStore', () => {
    it('should create a vector store successfully', async () => {
      // Arrange
      const dto: CreateVectorStoreDto = {
        name: 'Test Vector Store',
      };
      const userId = 1;
      const mockResponse: VectorStoreResponseDto = {
        storeId: 'vs_123',
        storeName: 'Test Vector Store',
        size: 0,
        agents: 0,
        files: 0,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      jest.spyOn(service, 'createVectorStore').mockResolvedValue(mockResponse);

      // Act
      const result = await controller.createVectorStore(dto, userId);

      // Assert
      expect(service.createVectorStore).toHaveBeenCalledWith(dto, userId);
      expect(result.code).toBe(HttpStatus.CREATED);
      expect(result.message).toBe('Tạo vector store thành công.');
      expect(result.result).toEqual(mockResponse);
    });

    it('should handle service exceptions and rethrow them', async () => {
      // Arrange
      const dto: CreateVectorStoreDto = {
        name: 'Test Vector Store',
      };
      const userId = 1;
      const errorMessage = 'Lỗi khi tạo vector store';

      jest.spyOn(service, 'createVectorStore').mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(controller.createVectorStore(dto, userId)).rejects.toThrow(Error);
      expect(service.createVectorStore).toHaveBeenCalledWith(dto, userId);
    });

    it('should handle AppException from service', async () => {
      // Arrange
      const dto: CreateVectorStoreDto = {
        name: 'Test Vector Store',
      };
      const userId = 1;

      // Create a proper mock error code
      const mockErrorCode = { code: 20201, message: 'Lỗi khi tạo vector store', status: HttpStatus.INTERNAL_SERVER_ERROR };
      jest.spyOn(service, 'createVectorStore').mockRejectedValue(new AppException(mockErrorCode, 'Lỗi ứng dụng'));

      // Act & Assert
      await expect(controller.createVectorStore(dto, userId)).rejects.toThrow(AppException);
      expect(service.createVectorStore).toHaveBeenCalledWith(dto, userId);
    });
  });

  describe('getVectorStores', () => {
    it('should return paginated vector stores', async () => {
      // Arrange
      const queryDto: QueryVectorStoreDto = {
        page: 1,
        limit: 10,
      };
      const userId = 1;

      const mockPaginatedResult: PaginatedResult<VectorStoreResponseDto> = {
        items: [
          {
            storeId: 'vs_123',
            storeName: 'Test Vector Store 1',
            size: 1024,
            agents: 2,
            files: 3,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
          {
            storeId: 'vs_456',
            storeName: 'Test Vector Store 2',
            size: 2048,
            agents: 1,
            files: 5,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(service, 'getVectorStores').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getVectorStores(queryDto, userId);

      // Assert
      expect(service.getVectorStores).toHaveBeenCalledWith(queryDto, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách vector store thành công.');
      expect(result.result).toEqual(mockPaginatedResult);
    });

    it('should handle empty result', async () => {
      // Arrange
      const queryDto: QueryVectorStoreDto = {
        page: 1,
        limit: 10,
      };
      const userId = 1;

      const mockEmptyResult: PaginatedResult<VectorStoreResponseDto> = {
        items: [],
        meta: {
          totalItems: 0,
          itemCount: 0,
          itemsPerPage: 10,
          totalPages: 0,
          currentPage: 1,
        },
      };

      jest.spyOn(service, 'getVectorStores').mockResolvedValue(mockEmptyResult);

      // Act
      const result = await controller.getVectorStores(queryDto, userId);

      // Assert
      expect(service.getVectorStores).toHaveBeenCalledWith(queryDto, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách vector store thành công.');
      expect(result.result).toEqual(mockEmptyResult);
    });

    it('should handle search parameter', async () => {
      // Arrange
      const queryDto: QueryVectorStoreDto = {
        page: 1,
        limit: 10,
        search: 'test',
      };
      const userId = 1;

      const mockPaginatedResult: PaginatedResult<VectorStoreResponseDto> = {
        items: [
          {
            storeId: 'vs_123',
            storeName: 'Test Vector Store',
            size: 1024,
            agents: 2,
            files: 3,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(service, 'getVectorStores').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getVectorStores(queryDto, userId);

      // Assert
      expect(service.getVectorStores).toHaveBeenCalledWith(queryDto, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách vector store thành công.');
      expect(result.result).toEqual(mockPaginatedResult);
    });

    it('should handle service exceptions and rethrow them', async () => {
      // Arrange
      const queryDto: QueryVectorStoreDto = {
        page: 1,
        limit: 10,
      };
      const userId = 1;
      const errorMessage = 'Lỗi khi lấy danh sách vector store';

      jest.spyOn(service, 'getVectorStores').mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(controller.getVectorStores(queryDto, userId)).rejects.toThrow(Error);
      expect(service.getVectorStores).toHaveBeenCalledWith(queryDto, userId);
    });

    it('should handle AppException from service', async () => {
      // Arrange
      const queryDto: QueryVectorStoreDto = {
        page: 1,
        limit: 10,
      };
      const userId = 1;

      // Create a proper mock error code
      const mockErrorCode = { code: 20202, message: 'Lỗi khi lấy danh sách vector store', status: HttpStatus.INTERNAL_SERVER_ERROR };
      jest.spyOn(service, 'getVectorStores').mockRejectedValue(new AppException(mockErrorCode, 'Lỗi khi lấy danh sách vector store'));

      // Act & Assert
      await expect(controller.getVectorStores(queryDto, userId)).rejects.toThrow(AppException);
      expect(service.getVectorStores).toHaveBeenCalledWith(queryDto, userId);
    });
  });

  describe('getVectorStoreDetail', () => {
    it('should return vector store detail', async () => {
      // Arrange
      const storeId = 'vs_123';
      const userId = 1;

      const mockVectorStore: VectorStoreResponseDto = {
        storeId: 'vs_123',
        storeName: 'Test Vector Store',
        size: 1024,
        agents: 2,
        files: 3,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      jest.spyOn(service, 'getVectorStoreDetail').mockResolvedValue(mockVectorStore);

      // Act
      const result = await controller.getVectorStoreDetail(storeId, userId);

      // Assert
      expect(service.getVectorStoreDetail).toHaveBeenCalledWith(storeId, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy thông tin chi tiết vector store thành công.');
      expect(result.result).toEqual(mockVectorStore);
    });

    it('should handle NotFoundException from service', async () => {
      // Arrange
      const storeId = 'non_existent_vs';
      const userId = 1;
      const errorMessage = 'Vector store không tồn tại';

      jest.spyOn(service, 'getVectorStoreDetail').mockRejectedValue(new NotFoundException(errorMessage));

      // Act & Assert
      await expect(controller.getVectorStoreDetail(storeId, userId)).rejects.toThrow(NotFoundException);
      expect(service.getVectorStoreDetail).toHaveBeenCalledWith(storeId, userId);
    });

    it('should handle service exceptions and rethrow them', async () => {
      // Arrange
      const storeId = 'vs_123';
      const userId = 1;
      const errorMessage = 'Lỗi khi lấy thông tin chi tiết vector store';

      jest.spyOn(service, 'getVectorStoreDetail').mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(controller.getVectorStoreDetail(storeId, userId)).rejects.toThrow(Error);
      expect(service.getVectorStoreDetail).toHaveBeenCalledWith(storeId, userId);
    });

    it('should handle AppException from service', async () => {
      // Arrange
      const storeId = 'vs_123';
      const userId = 1;

      // Create a proper mock error code
      const mockErrorCode = { code: 20203, message: 'Lỗi khi lấy chi tiết vector store', status: HttpStatus.INTERNAL_SERVER_ERROR };
      jest.spyOn(service, 'getVectorStoreDetail').mockRejectedValue(new AppException(mockErrorCode, 'Lỗi khi lấy thông tin chi tiết vector store'));

      // Act & Assert
      await expect(controller.getVectorStoreDetail(storeId, userId)).rejects.toThrow(AppException);
      expect(service.getVectorStoreDetail).toHaveBeenCalledWith(storeId, userId);
    });
  });

  describe('assignFilesToVectorStore', () => {
    it('should assign files to vector store successfully', async () => {
      // Arrange
      const storeId = 'vs_123';
      const dto: AssignFilesDto = {
        fileIds: ['file1', 'file2', 'file3'],
      };
      const userId = 1;

      const mockResult: AssignFilesResponseDto = {
        success: true,
        message: 'Đã xử lý 3 file với OpenAI thành công. Đã gán 3 file vào vector store.',
        processedFiles: 3,
        processedFileDetails: [
          { id: 'file1', openAiFileId: 'file-abc123' },
          { id: 'file2', openAiFileId: 'file-def456' },
          { id: 'file3', openAiFileId: 'file-ghi789' },
        ],
        skippedFiles: [],
      };

      jest.spyOn(service, 'assignFilesToVectorStore').mockResolvedValue(mockResult);

      // Act
      const result = await controller.assignFilesToVectorStore(storeId, dto, userId);

      // Assert
      expect(service.assignFilesToVectorStore).toHaveBeenCalledWith(storeId, dto, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Gán file vào vector store thành công.');
      expect(result.result).toEqual(mockResult);
    });

    it('should handle NotFoundException from service', async () => {
      // Arrange
      const storeId = 'non_existent_vs';
      const dto: AssignFilesDto = {
        fileIds: ['file1', 'file2', 'file3'],
      };
      const userId = 1;
      const errorMessage = 'Vector store không tồn tại';

      jest.spyOn(service, 'assignFilesToVectorStore').mockRejectedValue(new NotFoundException(errorMessage));

      // Act & Assert
      await expect(controller.assignFilesToVectorStore(storeId, dto, userId)).rejects.toThrow(NotFoundException);
      expect(service.assignFilesToVectorStore).toHaveBeenCalledWith(storeId, dto, userId);
    });

    it('should handle service exceptions and rethrow them', async () => {
      // Arrange
      const storeId = 'vs_123';
      const dto: AssignFilesDto = {
        fileIds: ['file1', 'file2', 'file3'],
      };
      const userId = 1;
      const errorMessage = 'Lỗi khi gán file vào vector store';

      jest.spyOn(service, 'assignFilesToVectorStore').mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(controller.assignFilesToVectorStore(storeId, dto, userId)).rejects.toThrow(Error);
      expect(service.assignFilesToVectorStore).toHaveBeenCalledWith(storeId, dto, userId);
    });

    it('should handle AppException from service', async () => {
      // Arrange
      const storeId = 'vs_123';
      const dto: AssignFilesDto = {
        fileIds: ['file1', 'file2', 'file3'],
      };
      const userId = 1;

      // Create a proper mock error code
      const mockErrorCode = { code: 20206, message: 'Lỗi khi gán file vào vector store', status: HttpStatus.INTERNAL_SERVER_ERROR };
      jest.spyOn(service, 'assignFilesToVectorStore').mockRejectedValue(new AppException(mockErrorCode, 'Lỗi khi gán file vào vector store'));

      // Act & Assert
      await expect(controller.assignFilesToVectorStore(storeId, dto, userId)).rejects.toThrow(AppException);
      expect(service.assignFilesToVectorStore).toHaveBeenCalledWith(storeId, dto, userId);
    });
  });

  describe('removeFileFromVectorStore', () => {
    it('should remove a file from vector store successfully', async () => {
      // Arrange
      const storeId = 'vs_123';
      const fileId = 'file1';
      const userId = 1;

      const mockResult = {
        success: true,
        message: 'Đã xóa 1 file khỏi vector store thành công.',
      };

      jest.spyOn(service, 'removeFilesFromVectorStore').mockResolvedValue(mockResult);

      // Act
      const result = await controller.removeFileFromVectorStore(storeId, fileId, userId);

      // Assert
      expect(service.removeFilesFromVectorStore).toHaveBeenCalledWith(storeId, [fileId], userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Xóa file khỏi vector store thành công.');
      expect(result.result).toEqual(mockResult);
    });

    it('should handle NotFoundException from service', async () => {
      // Arrange
      const storeId = 'non_existent_vs';
      const fileId = 'file1';
      const userId = 1;
      const errorMessage = 'Vector store không tồn tại';

      jest.spyOn(service, 'removeFilesFromVectorStore').mockRejectedValue(new NotFoundException(errorMessage));

      // Act & Assert
      await expect(controller.removeFileFromVectorStore(storeId, fileId, userId)).rejects.toThrow(NotFoundException);
      expect(service.removeFilesFromVectorStore).toHaveBeenCalledWith(storeId, [fileId], userId);
    });

    it('should handle service exceptions and rethrow them', async () => {
      // Arrange
      const storeId = 'vs_123';
      const fileId = 'file1';
      const userId = 1;
      const errorMessage = 'Lỗi khi xóa file khỏi vector store';

      jest.spyOn(service, 'removeFilesFromVectorStore').mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(controller.removeFileFromVectorStore(storeId, fileId, userId)).rejects.toThrow(Error);
      expect(service.removeFilesFromVectorStore).toHaveBeenCalledWith(storeId, [fileId], userId);
    });

    it('should handle AppException from service', async () => {
      // Arrange
      const storeId = 'vs_123';
      const fileId = 'file1';
      const userId = 1;

      // Create a proper mock error code
      const mockErrorCode = { code: 20207, message: 'Lỗi khi xóa file khỏi vector store', status: HttpStatus.INTERNAL_SERVER_ERROR };
      jest.spyOn(service, 'removeFilesFromVectorStore').mockRejectedValue(new AppException(mockErrorCode, 'Lỗi khi xóa file khỏi vector store'));

      // Act & Assert
      await expect(controller.removeFileFromVectorStore(storeId, fileId, userId)).rejects.toThrow(AppException);
      expect(service.removeFilesFromVectorStore).toHaveBeenCalledWith(storeId, [fileId], userId);
    });
  });

  describe('removeFilesFromVectorStore', () => {
    it('should remove multiple files from vector store successfully', async () => {
      // Arrange
      const storeId = 'vs_123';
      const dto: AssignFilesDto = {
        fileIds: ['file1', 'file2', 'file3'],
      };
      const userId = 1;

      const mockResult = {
        success: true,
        message: 'Đã xóa 3 file khỏi vector store thành công.',
      };

      jest.spyOn(service, 'removeFilesFromVectorStore').mockResolvedValue(mockResult);

      // Act
      const result = await controller.removeFilesFromVectorStore(storeId, dto, userId);

      // Assert
      expect(service.removeFilesFromVectorStore).toHaveBeenCalledWith(storeId, dto.fileIds, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Xóa file khỏi vector store thành công.');
      expect(result.result).toEqual(mockResult);
    });

    it('should handle NotFoundException from service', async () => {
      // Arrange
      const storeId = 'non_existent_vs';
      const dto: AssignFilesDto = {
        fileIds: ['file1', 'file2', 'file3'],
      };
      const userId = 1;
      const errorMessage = 'Vector store không tồn tại';

      jest.spyOn(service, 'removeFilesFromVectorStore').mockRejectedValue(new NotFoundException(errorMessage));

      // Act & Assert
      await expect(controller.removeFilesFromVectorStore(storeId, dto, userId)).rejects.toThrow(NotFoundException);
      expect(service.removeFilesFromVectorStore).toHaveBeenCalledWith(storeId, dto.fileIds, userId);
    });

    it('should handle service exceptions and rethrow them', async () => {
      // Arrange
      const storeId = 'vs_123';
      const dto: AssignFilesDto = {
        fileIds: ['file1', 'file2', 'file3'],
      };
      const userId = 1;
      const errorMessage = 'Lỗi khi xóa file khỏi vector store';

      jest.spyOn(service, 'removeFilesFromVectorStore').mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(controller.removeFilesFromVectorStore(storeId, dto, userId)).rejects.toThrow(Error);
      expect(service.removeFilesFromVectorStore).toHaveBeenCalledWith(storeId, dto.fileIds, userId);
    });

    it('should handle AppException from service', async () => {
      // Arrange
      const storeId = 'vs_123';
      const dto: AssignFilesDto = {
        fileIds: ['file1', 'file2', 'file3'],
      };
      const userId = 1;

      // Create a proper mock error code
      const mockErrorCode = { code: 20207, message: 'Lỗi khi xóa file khỏi vector store', status: HttpStatus.INTERNAL_SERVER_ERROR };
      jest.spyOn(service, 'removeFilesFromVectorStore').mockRejectedValue(new AppException(mockErrorCode, 'Lỗi khi xóa file khỏi vector store'));

      // Act & Assert
      await expect(controller.removeFilesFromVectorStore(storeId, dto, userId)).rejects.toThrow(AppException);
      expect(service.removeFilesFromVectorStore).toHaveBeenCalledWith(storeId, dto.fileIds, userId);
    });
  });


});
