import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { KnowledgeFileUserService } from '../services';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import {
  BatchCreateFilesDto,
  BatchCreateFilesResponseDto,
  DeleteFilesDto,
  FileResponseDto,
  QueryFileDto
} from '../dto';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { ApiExtraModels } from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators';

@ApiTags(SWAGGER_API_TAGS.USER_KNOWLEDGE_FILES)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@ApiExtraModels(FileResponseDto, BatchCreateFilesResponseDto, DeleteFilesDto, PaginatedResult)
@Controller('user/knowledge-files')
export class KnowledgeFileUserController {
  constructor(
    private readonly knowledgeFileUserService: KnowledgeFileUserService,
  ) {}

  /**
   * Thêm nhiều file tri thức
   */
  @ApiOperation({ summary: 'Thêm nhiều file tri thức' })
  @ApiBody({ type: BatchCreateFilesDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Đã tạo files tri thức thành công.',
    schema: ApiResponseDto.getSchema(BatchCreateFilesResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    schema: {
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Dữ liệu đầu vào không hợp lệ.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Bạn không có quyền thực hiện thao tác này.',
    schema: {
      properties: {
        code: { type: 'number', example: 403 },
        message: {
          type: 'string',
          example: 'Bạn không có quyền thực hiện thao tác này.',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Post('batch')
  @HttpCode(HttpStatus.CREATED)
  async batchCreateFiles(
    @Body() batchCreateFilesDto: BatchCreateFilesDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.knowledgeFileUserService.batchCreateFiles(
      batchCreateFilesDto,
      userId,
    );

    return ApiResponseDto.created<BatchCreateFilesResponseDto>(result, 'Đã tạo files tri thức thành công.');
  }

  /**
   * Lấy danh sách files tri thức
   */
  @ApiOperation({ summary: 'Lấy danh sách files tri thức' })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Tên file cần tìm kiếm',
  })
  @ApiQuery({
    name: 'extensions',
    required: false,
    type: String,
    description: 'Lọc theo định dạng file (ví dụ: "pdf,docx,txt")',
  })
  @ApiQuery({
    name: 'vectorStoreId',
    required: false,
    type: String,
    description: 'ID của vector store để lọc file',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Số trang, mặc định là 1',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số lượng kết quả trên một trang, mặc định là 10',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Trường sắp xếp (createdAt, updatedAt)',
    enum: ['name', 'createdAt', 'storage'],
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    description: 'Hướng sắp xếp (asc hoặc desc)',
    enum: ['asc', 'desc'],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách file thành công.',
    schema: ApiResponseDto.getPaginatedSchema(FileResponseDto)
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Tham số không hợp lệ.',
    schema: {
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Tham số không hợp lệ.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Bạn không có quyền truy cập danh sách file này.',
    schema: {
      properties: {
        code: { type: 'number', example: 403 },
        message: {
          type: 'string',
          example: 'Bạn không có quyền truy cập danh sách file này.',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Get()
  @HttpCode(HttpStatus.OK)
  async getFiles(@Query() queryDto: QueryFileDto, @CurrentUser('id') userId: number) {
    const result = await this.knowledgeFileUserService.getFiles(
      queryDto,
      userId,
    );
    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách file thành công.',
    );
  }


  /**
   * Xóa nhiều file tri thức
   */
  @ApiOperation({ summary: 'Xóa nhiều file tri thức' })
  @ApiBody({ type: DeleteFilesDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa file thành công.',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Đã xóa 2 file thành công.' },
        result: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            deletedCount: { type: 'number', example: 2 },
            failedItems: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890' },
                  reason: { type: 'string', example: 'File đã bị xóa trước đó' },
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    schema: {
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Danh sách file không được để trống.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy file.',
    schema: {
      properties: {
        code: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Không có file nào tồn tại hoặc bạn không có quyền truy cập.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Bạn không có quyền xóa file này.',
    schema: {
      properties: {
        code: { type: 'number', example: 403 },
        message: {
          type: 'string',
          example: 'Bạn không có quyền xóa file này.',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Delete('batch')
  @HttpCode(HttpStatus.OK)
  async batchDeleteFiles(@Body() deleteFilesDto: DeleteFilesDto, @CurrentUser('id') userId: number) {
    const result = await this.knowledgeFileUserService.deleteFiles(deleteFilesDto.fileIds, userId);
    return ApiResponseDto.deleted(result, `Đã xóa ${result.deletedCount} file thành công.`);
  }

  /**
   * Gửi file tri thức để duyệt
   */
  @ApiOperation({ summary: 'Gửi file tri thức để duyệt' })
  @ApiParam({ name: 'id', description: 'ID của file cần gửi duyệt' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'File đã được gửi duyệt thành công.',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'File đã được gửi duyệt thành công.' },
        result: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            message: { type: 'string', example: 'File đã được gửi duyệt thành công' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy file.',
    schema: {
      properties: {
        code: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Không tìm thấy file.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Trạng thái file không hợp lệ.',
    schema: {
      properties: {
        code: { type: 'number', example: 20108 },
        message: { type: 'string', example: 'File không ở trạng thái nháp và không thể gửi duyệt' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Bạn không có quyền thực hiện thao tác này.',
    schema: {
      properties: {
        code: { type: 'number', example: 403 },
        message: {
          type: 'string',
          example: 'Bạn không có quyền thực hiện thao tác này.',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Put(':id/submit')
  @HttpCode(HttpStatus.OK)
  async submitForApproval(@Param('id') id: string, @CurrentUser('id') userId: number) {
    const result = await this.knowledgeFileUserService.submitForApproval(id, userId);
    return ApiResponseDto.success(result, 'File đã được gửi duyệt thành công.');
  }
}
