import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { RuleContractRepository } from '../../repositories';
import {
  RuleContractQueryDto,
  RuleContractResponseDto,
  RegisterRuleContractDto,
  RuleContractStatusResponseDto,
  CreateIndividualRuleContractDto,
  RuleContractExtendedResponseDto
} from '../dto';
import { RULE_CONTRACT_ERROR_CODES } from '../../errors';
import { Transactional } from 'typeorm-transactional';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { ContractStatusEnum, ContractTypeEnum } from '../../entities/rule-contract.entity';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { UserTypeEnum } from '@modules/user/enums';
import { PdfPosition } from '@/shared/interface/pdf-edit.interface';
import { ContractHelperService } from './contract-helper.service';

/**
 * Service xử lý logic nghiệp vụ cho hợp đồng nguyên tắc (user)
 */
@Injectable()
export class RuleContractUserService {
  private readonly logger = new Logger(RuleContractUserService.name);

  constructor(
    private readonly ruleContractRepository: RuleContractRepository,
    private readonly cdnService: CdnService,
    private readonly userRepository: UserRepository,
    private readonly contractHelperService: ContractHelperService,
  ) {}

  /**
   * Đăng ký loại hợp đồng nguyên tắc
   * @param userId ID của người dùng
   * @param dto Thông tin đăng ký
   * @returns Trạng thái hợp đồng
   */
  @Transactional()
  async registerTypeRuleContract(
    userId: number,
    dto: RegisterRuleContractDto,
  ): Promise<RuleContractStatusResponseDto> {
    try {
      // Nếu là hợp đồng doanh nghiệp, cập nhật loại tài khoản người dùng
      if (dto.type === ContractTypeEnum.BUSINESS) {
        // Tìm người dùng theo ID
        const user = await this.userRepository.findById(userId);
        if (user) {
          // Cập nhật loại tài khoản người dùng thành BUSINESS
          user.type = UserTypeEnum.BUSINESS;
          user.updatedAt = Date.now();
          // Sử dụng repository.save từ TypeORM
          await this.userRepository['repository'].save(user);
        }
      }

      // Tạo hợp đồng mới
      const newContract = this.ruleContractRepository.create({
        userId,
        type: dto.type,
        status: ContractStatusEnum.DRAFT, // Trạng thái CHUA_KY trong Java tương ứng với DRAFT
        createdAt: Date.now(),
      });

      // Lưu hợp đồng vào database
      await this.ruleContractRepository.save(newContract);

      // Trả về trạng thái hợp đồng
      return {
        status: ContractStatusEnum.DRAFT,
        type: dto.type,
      };
    } catch (error) {
      this.logger.error(
        `Error registering rule contract for user ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi đăng ký hợp đồng nguyên tắc',
      );
    }
  }

  /**
   * Lấy danh sách hợp đồng nguyên tắc của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  @Transactional()
  async getContracts(
    userId: number,
    queryDto: RuleContractQueryDto,
  ): Promise<PaginatedResult<RuleContractResponseDto>> {
    try {
      // Lấy danh sách hợp đồng với phân trang
      const { items, meta } = await this.ruleContractRepository.findWithPaginationForUser(
        userId,
        queryDto,
      );

      // Chuyển đổi dữ liệu sang DTO
      const contractDtos = await Promise.all(items.map(async (contract) => {
        // Tạo URL có chữ ký cho file hợp đồng
        let contractUrl = '';
        if (contract.contractUrlPdf) {
          const generatedUrl = this.cdnService.generateUrlView(
            contract.contractUrlPdf as string,
            TimeIntervalEnum.ONE_HOUR,
          );
          if (generatedUrl) {
            contractUrl = generatedUrl;
          }
        }

        return {
          id: contract.id,
          contractCode: `HD-${contract.id}`,
          status: contract.status,
          type: contract.type,
          contractUrl,
          createdAt: contract.createdAt,
          userSignatureAt: contract.userSignatureAt,
          adminSignatureAt: contract.adminSignatureAt,
        };
      }));

      return {
        items: contractDtos,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting contracts for user ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách hợp đồng nguyên tắc',
      );
    }
  }

  /**
   * Lấy chi tiết hợp đồng nguyên tắc của người dùng
   * @param userId ID của người dùng
   * @param id ID của hợp đồng
   * @returns Thông tin chi tiết hợp đồng
   */
  @Transactional()
  async getContractById(userId: number, id: number): Promise<RuleContractResponseDto> {
    try {
      // Lấy thông tin chi tiết hợp đồng
      const contract = await this.ruleContractRepository.findById(id);

      if (!contract) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          `Không tìm thấy hợp đồng nguyên tắc với ID ${id}`,
        );
      }

      // Kiểm tra quyền truy cập
      if (contract.userId !== userId) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.UNAUTHORIZED_ACCESS,
          'Bạn không có quyền truy cập hợp đồng này',
        );
      }

      // Tạo URL có chữ ký cho file hợp đồng
      let contractUrl = '';
      if (contract.contractUrlPdf) {
        const generatedUrl = this.cdnService.generateUrlView(
          contract.contractUrlPdf as string,
          TimeIntervalEnum.ONE_HOUR,
        );
        if (generatedUrl) {
          contractUrl = generatedUrl;
        }
      }

      // Xử lý dữ liệu trả về
      return {
        id: contract.id,
        contractCode: `HD-${contract.id}`,
        status: contract.status,
        type: contract.type,
        contractUrl,
        createdAt: contract.createdAt,
        userSignatureAt: contract.userSignatureAt,
        adminSignatureAt: contract.adminSignatureAt,
      };
    } catch (error) {
      this.logger.error(
        `Error getting contract for user ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin chi tiết hợp đồng nguyên tắc',
      );
    }
  }
  /**
   * Tạo hợp đồng nguyên tắc cho cá nhân
   * @param userId ID của người dùng
   * @param dto Thông tin hợp đồng
   * @returns Thông tin hợp đồng đã tạo
   */
  @Transactional()
  async createIndividualRuleContract(
    userId: number,
    dto: CreateIndividualRuleContractDto,
  ): Promise<RuleContractExtendedResponseDto> {
    try {
      this.logger.log(`Tạo hợp đồng nguyên tắc cho cá nhân, userId: ${userId}`);

      // Tạo danh sách vị trí cần chỉnh sửa trên PDF
      const positions = this.createIndividualContractPositions(dto);

      // Sử dụng ContractHelperService để tạo hợp đồng
      const { contractKey, contractBase64 } = await this.contractHelperService.createIndividualRuleContract(
        userId,
        positions,
      );

      // Tạo hợp đồng mới trong database
      const newContract = this.ruleContractRepository.create({
        userId,
        type: ContractTypeEnum.INDIVIDUAL,
        status: ContractStatusEnum.DRAFT,
        contractUrlPdf: contractKey,
        createdAt: Date.now(),
      });

      // Lưu hợp đồng vào database
      await this.ruleContractRepository.save(newContract);

      // Tạo URL có chữ ký cho file hợp đồng
      const contractUrl = this.cdnService.generateUrlView(
        contractKey,
        TimeIntervalEnum.ONE_HOUR,
      );

      // Trả về thông tin hợp đồng
      return {
        status: ContractStatusEnum.DRAFT,
        type: ContractTypeEnum.INDIVIDUAL,
        contractBase64: contractBase64,
        contractUrl: contractUrl || '',
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo hợp đồng nguyên tắc cho cá nhân, userId: ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi tạo hợp đồng nguyên tắc cho cá nhân',
      );
    }
  }

  /**
   * Tạo danh sách vị trí cần chỉnh sửa trên PDF hợp đồng cá nhân
   * @param dto Thông tin hợp đồng
   * @returns Danh sách vị trí cần chỉnh sửa
   */
  private createIndividualContractPositions(
    dto: CreateIndividualRuleContractDto,
  ): PdfPosition[] {
    // Chuyển đổi định dạng ngày tháng
    const formatDate = (dateStr: string) => {
      const date = new Date(dateStr);
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
    };

    const formattedDateOfBirth = formatDate(dto.dateOfBirth);
    const formattedIssueDate = formatDate(dto.issueDate);

    // Tạo danh sách vị trí
    // Lưu ý: Các vị trí này cần được điều chỉnh theo mẫu hợp đồng thực tế
    const positions: PdfPosition[] = [
      // Thông tin cá nhân
      { pageIndex: 0, text: dto.name, xMm: 50, yMm: 50, size: 12 },
      { pageIndex: 0, text: formattedDateOfBirth, xMm: 50, yMm: 60, size: 12 },
      { pageIndex: 0, text: dto.cccd, xMm: 50, yMm: 70, size: 12 },
      { pageIndex: 0, text: formattedIssueDate, xMm: 50, yMm: 80, size: 12 },
      { pageIndex: 0, text: dto.issuePlace, xMm: 50, yMm: 90, size: 12 },
      { pageIndex: 0, text: dto.phone, xMm: 50, yMm: 100, size: 12 },
      { pageIndex: 0, text: dto.address, xMm: 50, yMm: 110, size: 12 },
    ];

    // Thêm mã số thuế nếu có
    if (dto.taxCode) {
      positions.push({ pageIndex: 0, text: dto.taxCode, xMm: 50, yMm: 120, size: 12 });
    }

    // Thêm tên người ký ở cuối hợp đồng
    positions.push({
      pageIndex: 2,
      text: dto.name.toUpperCase(),
      xMm: 150,
      yMm: 200,
      size: 14,
    });

    return positions;
  }
}