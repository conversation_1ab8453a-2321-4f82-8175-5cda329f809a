import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { RuleContractUserService } from '../services';
import {
  RuleContractQueryDto,
  RuleContractResponseDto,
  RegisterRuleContractDto,
  RuleContractStatusResponseDto,
  CreateIndividualRuleContractDto,
  RuleContractExtendedResponseDto,
  SignContractDto,
} from '../dto';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { RuleContractStateService } from '../../state-machine/rule-contract-state.service';
import {
  RuleContractState,
  RuleContractEvent,
  CreateEventData,
  mapStateToStatus
} from '../../state-machine/rule-contract.types';
import { ContractTypeEnum } from '../../entities/rule-contract.entity';
import { SortDirection } from '@common/dto/query.dto';

/**
 * Controller xử lý API liên quan đến hợp đồng nguyên tắc cho user
 */
@Controller('user/rule-contracts')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.USER_RULE_CONTRACT)
@ApiExtraModels(
  ApiResponseDto,
  RuleContractResponseDto,
  RuleContractStatusResponseDto,
  RuleContractExtendedResponseDto,
  PaginatedResult
)
export class RuleContractUserController {
  constructor(
    private readonly ruleContractUserService: RuleContractUserService,
    private readonly ruleContractStateService: RuleContractStateService,
  ) {}

  /**
   * Đăng ký loại hợp đồng nguyên tắc
   * @param user Thông tin người dùng hiện tại
   * @param dto Thông tin đăng ký
   * @returns Trạng thái hợp đồng
   */
  @Post('register')
  @ApiOperation({ summary: 'Đăng ký loại hợp đồng nguyên tắc' })
  @ApiResponse({
    status: 200,
    description: 'Đăng ký loại hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(RuleContractStatusResponseDto) },
          },
        },
      ],
    },
  })
  async registerTypeRuleContract(
    @CurrentUser() user: JwtPayload,
    @Body() dto: RegisterRuleContractDto,
  ): Promise<ApiResponseDto<RuleContractStatusResponseDto>> {
    // Sử dụng state machine để tạo hợp đồng
    const createData: CreateEventData = {
      userId: user.id,
      contractType: dto.type,
    };

    // Tạo hợp đồng mới thông qua state machine
    await this.ruleContractStateService.createContract(user.id, createData);

    // Trả về trạng thái hợp đồng
    return ApiResponseDto.success(
      {
        status: mapStateToStatus(RuleContractState.DRAFT),
        type: dto.type,
      },
      'Đăng ký loại hợp đồng nguyên tắc thành công',
    );
  }

  /**
   * Lấy danh sách hợp đồng nguyên tắc của người dùng
   * @param user Thông tin người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách hợp đồng nguyên tắc của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(RuleContractResponseDto) },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  })
  async getContracts(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: RuleContractQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<RuleContractResponseDto>>> {
    const contracts = await this.ruleContractUserService.getContracts(user.id, queryDto);
    return ApiResponseDto.success(
      contracts,
      'Lấy danh sách hợp đồng nguyên tắc thành công',
    );
  }

  /**
   * Lấy chi tiết hợp đồng nguyên tắc của người dùng
   * @param user Thông tin người dùng hiện tại
   * @param id ID của hợp đồng
   * @returns Chi tiết hợp đồng
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết hợp đồng nguyên tắc' })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(RuleContractResponseDto) },
          },
        },
      ],
    },
  })
  async getContractById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<RuleContractResponseDto>> {
    const contract = await this.ruleContractUserService.getContractById(user.id, id);
    return ApiResponseDto.success(
      contract,
      'Lấy chi tiết hợp đồng nguyên tắc thành công',
    );
  }

  /**
   * Tạo hợp đồng nguyên tắc cho cá nhân
   * @param user Thông tin người dùng hiện tại
   * @param dto Thông tin hợp đồng
   * @returns Thông tin hợp đồng đã tạo
   */
  @Post('individual')
  @ApiOperation({ summary: 'Tạo hợp đồng nguyên tắc cho cá nhân' })
  @ApiResponse({
    status: 200,
    description: 'Tạo hợp đồng nguyên tắc cho cá nhân thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(RuleContractExtendedResponseDto) },
          },
        },
      ],
    },
  })
  async createIndividualRuleContract(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CreateIndividualRuleContractDto,
  ): Promise<ApiResponseDto<RuleContractExtendedResponseDto>> {
    // Sử dụng service để tạo hợp đồng và lấy thông tin
    const result = await this.ruleContractUserService.createIndividualRuleContract(user.id, dto);

    // Sử dụng state machine để cập nhật trạng thái hợp đồng
    if (result.contractUrl) {
      // Lấy contractId từ database dựa trên contractKey
      const contracts = await this.ruleContractUserService.getContracts(user.id, {
        limit: 1,
        page: 1,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
      });

      if (contracts.items.length > 0) {
        const contractId = contracts.items[0].id;

        // Khởi tạo state machine cho hợp đồng này
        await this.ruleContractStateService.initializeStateMachine(user.id, contractId);
      }
    }

    // Trả về thông tin hợp đồng
    return ApiResponseDto.success(
      result,
      'Tạo hợp đồng nguyên tắc cho cá nhân thành công',
    );
  }

  /**
   * Ký hợp đồng nguyên tắc
   * @param user Thông tin người dùng hiện tại
   * @param id ID của hợp đồng
   * @param dto Thông tin chữ ký
   * @returns Trạng thái hợp đồng sau khi ký
   */
  @Post(':id/sign')
  @ApiOperation({ summary: 'Ký hợp đồng nguyên tắc' })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Ký hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                state: {
                  type: 'string',
                  enum: Object.values(RuleContractState),
                  example: RuleContractState.PENDING_APPROVAL,
                },
              },
            },
          },
        },
      ],
    },
  })
  async signContract(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: SignContractDto,
  ): Promise<ApiResponseDto<{ state: RuleContractState }>> {
    // Kiểm tra quyền truy cập hợp đồng
    await this.ruleContractUserService.getContractById(user.id, id);

    // Sử dụng state machine để ký hợp đồng
    const state = await this.ruleContractStateService.signContract(id, {
      signatureData: dto.signatureData,
    });

    return ApiResponseDto.success(
      { state },
      'Ký hợp đồng nguyên tắc thành công',
    );
  }
}
