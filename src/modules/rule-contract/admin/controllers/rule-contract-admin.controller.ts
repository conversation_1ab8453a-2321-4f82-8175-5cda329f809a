import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { RuleContractAdminService } from '../services';
import {
  RuleContractQueryDto,
  RuleContractResponseDto,
  ApproveContractDto,
  RejectContractDto,
} from '../dto';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { RuleContractStateService } from '../../state-machine/rule-contract-state.service';
import {
  RuleContractState,
  mapStateToStatus
} from '../../state-machine/rule-contract.types';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';

/**
 * Controller xử lý API liên quan đến hợp đồng nguyên tắc cho admin
 */
@Controller('admin/rule-contracts')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.ADMIN_FILES)
@ApiExtraModels(ApiResponseDto, RuleContractResponseDto, PaginatedResult)
export class RuleContractAdminController {
  constructor(
    private readonly ruleContractAdminService: RuleContractAdminService,
    private readonly ruleContractStateService: RuleContractStateService,
  ) {}

  /**
   * Lấy danh sách hợp đồng nguyên tắc
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách hợp đồng nguyên tắc' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(RuleContractResponseDto) },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  })
  async getContracts(
    @Query() queryDto: RuleContractQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<RuleContractResponseDto>>> {
    const contracts = await this.ruleContractAdminService.getContracts(queryDto);
    return ApiResponseDto.success(
      contracts,
      'Lấy danh sách hợp đồng nguyên tắc thành công',
    );
  }

  /**
   * Lấy chi tiết hợp đồng nguyên tắc
   * @param id ID của hợp đồng
   * @returns Chi tiết hợp đồng
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết hợp đồng nguyên tắc' })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(RuleContractResponseDto) },
          },
        },
      ],
    },
  })
  async getContractById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<RuleContractResponseDto>> {
    const contract = await this.ruleContractAdminService.getContractById(id);
    return ApiResponseDto.success(
      contract,
      'Lấy chi tiết hợp đồng nguyên tắc thành công',
    );
  }

  /**
   * Phê duyệt hợp đồng nguyên tắc
   * @param employee Thông tin nhân viên hiện tại
   * @param id ID của hợp đồng
   * @param dto Thông tin phê duyệt
   * @returns Trạng thái hợp đồng sau khi phê duyệt
   */
  @Post(':id/approve')
  @ApiOperation({ summary: 'Phê duyệt hợp đồng nguyên tắc' })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Phê duyệt hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                state: {
                  type: 'string',
                  enum: Object.values(RuleContractState),
                  example: RuleContractState.APPROVED,
                },
              },
            },
          },
        },
      ],
    },
  })
  async approveContract(
    @CurrentEmployee() employee: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: ApproveContractDto,
  ): Promise<ApiResponseDto<{ state: RuleContractState }>> {
    // Kiểm tra hợp đồng tồn tại
    await this.ruleContractAdminService.getContractById(id);

    // Sử dụng state machine để phê duyệt hợp đồng
    const state = await this.ruleContractStateService.approveContract(id, {
      adminId: dto.adminId,
    });

    return ApiResponseDto.success(
      { state },
      'Phê duyệt hợp đồng nguyên tắc thành công',
    );
  }

  /**
   * Từ chối hợp đồng nguyên tắc
   * @param employee Thông tin nhân viên hiện tại
   * @param id ID của hợp đồng
   * @param dto Thông tin từ chối
   * @returns Trạng thái hợp đồng sau khi từ chối
   */
  @Post(':id/reject')
  @ApiOperation({ summary: 'Từ chối hợp đồng nguyên tắc' })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Từ chối hợp đồng nguyên tắc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                state: {
                  type: 'string',
                  enum: Object.values(RuleContractState),
                  example: RuleContractState.REJECTED,
                },
              },
            },
          },
        },
      ],
    },
  })
  async rejectContract(
    @CurrentEmployee() employee: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: RejectContractDto,
  ): Promise<ApiResponseDto<{ state: RuleContractState }>> {
    // Kiểm tra hợp đồng tồn tại
    await this.ruleContractAdminService.getContractById(id);

    // Sử dụng state machine để từ chối hợp đồng
    const state = await this.ruleContractStateService.rejectContract(id, {
      adminId: dto.adminId,
      rejectionReason: dto.rejectionReason,
    });

    return ApiResponseDto.success(
      { state },
      'Từ chối hợp đồng nguyên tắc thành công',
    );
  }
}
