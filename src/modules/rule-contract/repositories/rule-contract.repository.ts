import { Injectable, Logger } from '@nestjs/common';
import { DataSource, FindOptionsWhere, Like, Repository } from 'typeorm';
import { RuleContract, ContractStatusEnum, ContractTypeEnum } from '../entities/rule-contract.entity';
import { PaginatedResult } from '@/common/response';
import { RuleContractQueryDto as AdminRuleContractQueryDto } from '../admin/dto';
import { RuleContractQueryDto as UserRuleContractQueryDto } from '../user/dto';

/**
 * Repository cho RuleContract
 */
@Injectable()
export class RuleContractRepository extends Repository<RuleContract> {
  private readonly logger = new Logger(RuleContractRepository.name);

  constructor(private dataSource: DataSource) {
    super(RuleContract, dataSource.createEntityManager());
  }

  /**
   * Tìm hợp đồng theo ID
   * @param id ID của hợp đồng
   * @returns Thông tin hợp đồng hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<RuleContract | null> {
    return this.findOne({ where: { id } });
  }

  /**
   * Tìm hợp đồng theo ID người dùng
   * @param userId ID của người dùng
   * @returns Danh sách hợp đồng của người dùng
   */
  async findByUserId(userId: number): Promise<RuleContract[]> {
    return this.find({ where: { userId } });
  }

  /**
   * Lấy danh sách hợp đồng với phân trang (cho admin)
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  async findWithPaginationForAdmin(
    queryDto: AdminRuleContractQueryDto,
  ): Promise<PaginatedResult<RuleContract>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      status,
      type,
    } = queryDto;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Xây dựng điều kiện tìm kiếm
    const where: FindOptionsWhere<RuleContract> = {};

    // Thêm điều kiện lọc theo trạng thái
    if (status) {
      where.status = status;
    }

    // Thêm điều kiện lọc theo loại hợp đồng
    if (type) {
      where.type = type;
    }

    // Thực hiện truy vấn với phân trang
    const [items, totalItems] = await this.findAndCount({
      where,
      order: { [sortBy]: sortDirection },
      skip: offset,
      take: limit,
    });

    // Trả về kết quả phân trang
    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy danh sách hợp đồng với phân trang (cho user)
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  async findWithPaginationForUser(
    userId: number,
    queryDto: UserRuleContractQueryDto,
  ): Promise<PaginatedResult<RuleContract>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      status,
      type,
    } = queryDto;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Xây dựng điều kiện tìm kiếm
    const where: FindOptionsWhere<RuleContract> = { userId };

    // Thêm điều kiện lọc theo trạng thái
    if (status) {
      where.status = status;
    }

    // Thêm điều kiện lọc theo loại hợp đồng
    if (type) {
      where.type = type;
    }

    // Thực hiện truy vấn với phân trang
    const [items, totalItems] = await this.findAndCount({
      where,
      order: { [sortBy]: sortDirection },
      skip: offset,
      take: limit,
    });

    // Trả về kết quả phân trang
    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }
}
