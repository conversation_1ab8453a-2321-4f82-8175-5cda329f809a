import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { User } from '@modules/user/entities';
import { AppException, ErrorCode } from '@/common';

/**
 * Repository cho User trong module rule-contract
 */
@Injectable()
export class UserRepository extends Repository<User> {
  private readonly logger = new Logger(UserRepository.name);

  constructor(private dataSource: DataSource) {
    super(User, dataSource.createEntityManager());
  }

  /**
   * Tìm người dùng theo ID
   * @param id ID của người dùng
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<User | null> {
    return this.findOne({ where: { id } });
  }

  /**
   * Lưu thông tin người dùng
   * @param user Thông tin người dùng cần lưu
   * @returns Thông tin người dùng đã lưu
   */
  async saveUser(user: User): Promise<User> {
    try {
      return await this.save(user);
    } catch (error) {
      this.logger.error(`Lỗi khi lưu thông tin người dùng: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lưu thông tin người dùng');
    }
  }
}
