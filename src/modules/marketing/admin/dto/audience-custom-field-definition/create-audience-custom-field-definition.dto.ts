import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, Matches } from 'class-validator';

/**
 * Enum cho các kiểu dữ liệu của trường tùy chỉnh
 */
export enum CustomFieldDataType {
  STRING = 'string',
  INTEGER = 'integer',
  DATE = 'date',
  BOOLEAN = 'boolean',
  JSON = 'json',
}

/**
 * DTO cho việc tạo trường tùy chỉnh
 */
export class CreateAudienceCustomFieldDefinitionDto {
  /**
   * Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)
   * @example "customer_address"
   */
  @ApiProperty({
    description: 'Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)',
    example: 'customer_address',
  })
  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> danh không được để trống' })
  @IsString({ message: '<PERSON>ịnh danh phải là chuỗi' })
  @Matches(/^[a-z0-9_]+$/, { message: 'Định danh chỉ được chứa chữ thường, số và dấu gạch dưới' })
  fieldKey: string;

  /**
   * Tên hiển thị thân thiện với admin
   * @example "Địa chỉ khách hàng"
   */
  @ApiProperty({
    description: 'Tên hiển thị thân thiện với admin',
    example: 'Địa chỉ khách hàng',
  })
  @IsNotEmpty({ message: 'Tên hiển thị không được để trống' })
  @IsString({ message: 'Tên hiển thị phải là chuỗi' })
  displayName: string;

  /**
   * Kiểu dữ liệu: string, integer, date, boolean, json
   * @example "string"
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu',
    enum: CustomFieldDataType,
    example: CustomFieldDataType.STRING,
  })
  @IsNotEmpty({ message: 'Kiểu dữ liệu không được để trống' })
  @IsEnum(CustomFieldDataType, {
    message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldDataType).join(', ')}`,
  })
  dataType: CustomFieldDataType;

  /**
   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh
   * @example "Địa chỉ liên hệ của khách hàng"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',
    example: 'Địa chỉ liên hệ của khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;
}
