import { IsIn, IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CampaignServerDto } from './campaign-server.dto';

/**
 * Enum cho các nền tảng gửi
 */
export enum CampaignPlatform {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
}

/**
 * DTO cho việc tạo campaign mới
 */
export class CreateCampaignDto {
  /**
   * Tiêu đề chiến dịch
   * @example "Khuyến mãi tháng 5"
   */
  @ApiProperty({
    description: 'Tiêu đề chiến dịch',
    example: 'Khuyến mãi tháng 5',
  })
  @IsNotEmpty({ message: 'Tiêu đề không được để trống' })
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  title: string;

  /**
   * <PERSON>ô tả chiến dịch
   * @example "Chiến dịch khuyến mãi dành cho khách hàng VIP"
   */
  @ApiProperty({
    description: 'Mô tả chiến dịch',
    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Nền tảng gửi
   * @example "email"
   */
  @ApiProperty({
    description: 'Nền tảng gửi',
    enum: CampaignPlatform,
    example: CampaignPlatform.EMAIL,
  })
  @IsNotEmpty({ message: 'Nền tảng không được để trống' })
  @IsIn(Object.values(CampaignPlatform), {
    message: `Nền tảng phải là một trong các giá trị: ${Object.values(CampaignPlatform).join(', ')}`,
  })
  platform: CampaignPlatform;

  /**
   * Nội dung chiến dịch
   * @example "<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>"
   */
  @ApiProperty({
    description: 'Nội dung chiến dịch',
    example: '<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>',
  })
  @IsNotEmpty({ message: 'Nội dung không được để trống' })
  @IsString({ message: 'Nội dung phải là chuỗi' })
  content: string;

  /**
   * Thông tin máy chủ gửi
   */
  @ApiProperty({
    description: 'Thông tin máy chủ gửi',
    type: CampaignServerDto,
  })
  @IsNotEmpty({ message: 'Thông tin máy chủ không được để trống' })
  @ValidateNested()
  @Type(() => CampaignServerDto)
  server: CampaignServerDto;

  /**
   * Thời gian dự kiến gửi chiến dịch (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian dự kiến gửi chiến dịch (Unix timestamp)',
    example: 1619171200,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Thời gian dự kiến phải là số' })
  scheduledAt?: number;

  /**
   * Tiêu đề email (chỉ áp dụng cho chiến dịch email)
   * @example "Khuyến mãi đặc biệt dành cho bạn"
   */
  @ApiProperty({
    description: 'Tiêu đề email (chỉ áp dụng cho chiến dịch email)',
    example: 'Khuyến mãi đặc biệt dành cho bạn',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tiêu đề email phải là chuỗi' })
  subject?: string;

  /**
   * ID của segment hoặc danh sách ID của audience
   */
  @ApiProperty({
    description: 'ID của segment',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID segment phải là số' })
  segmentId?: number;

  /**
   * Danh sách ID của audience
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID của audience',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  audienceIds?: number[];
}
