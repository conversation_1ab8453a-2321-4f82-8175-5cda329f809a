import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserConvertCustomer } from '@modules/business/entities';
import { PaginatedResult } from '@common/response'; // Using user's simpler path
import { QueryUserConvertCustomerDto as QueryUserConvertCustomerDtoUser } from '@modules/business/user/dto'; // Aliased DTO
import { QueryUserConvertCustomerDto as QueryUserConvertCustomerDtoAdmin } from '@modules/business/admin/dto'; // Aliased DTO
import { Agent } from '@modules/agent/entities/agent.entity'; // From user file
import { User } from '@modules/user/entities/user.entity'; // From user file

/**
 * Repository xử lý các thao tác với bảng user_convert_customers,
 * kết hợp chức năng từ cả user và admin context.
 */
@Injectable()
export class UserConvertCustomerRepository extends Repository<UserConvertCustomer> {
  private readonly logger = new Logger(UserConvertCustomerRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserConvertCustomer, dataSource.createEntityManager());
  }

  // --- Base Query Builders ---

  /**
   * Tạo truy vấn cơ bản cho bảng user_convert_customers (User context version)
   * @returns QueryBuilder cho bảng user_convert_customers
   */
  private createBaseQuery_user(): SelectQueryBuilder<UserConvertCustomer> {
    return this.createQueryBuilder('customer');
  }

  /**
   * Tạo query builder cơ bản cho bảng user_convert_customers (Admin context version)
   * @returns SelectQueryBuilder<UserConvertCustomer>
   */
  private createBaseQuery_admin(): SelectQueryBuilder<UserConvertCustomer> {
    this.logger.log('(Admin) createBaseQuery: Tạo query builder cơ bản cho bảng user_convert_customers');
    return this.createQueryBuilder('customer');
  }

  /**
   * Tạo query builder cơ bản với join bảng user_orders (Admin context - unique method)
   * @returns SelectQueryBuilder<UserConvertCustomer>
   */
  private createBaseQueryWithOrders_admin(): SelectQueryBuilder<UserConvertCustomer> {
    this.logger.log('(Admin) createBaseQueryWithOrders: Tạo query builder với join bảng user_orders');
    return this.createQueryBuilder('customer')
      .leftJoinAndSelect('customer.userOrders', 'orders'); // Assuming 'userOrders' is a defined relation on UserConvertCustomer
  }


  // --- Methods from User File ---

  /**
   * Tìm khách hàng theo ID với thông tin Agent và User (User context method)
   * @param id ID khách hàng
   * @returns Khách hàng hoặc null nếu không tìm thấy, augmented with Agent and User
   */
  async findById(id: number): Promise<UserConvertCustomer & { agent?: Agent, user?: User } | null> {
    try {
      const query = this.createBaseQuery_user() // Use user base query
        .where('customer.id = :id', { id });

      const customer = await query.getOne();

      if (!customer) {
        return null;
      }

      const result: UserConvertCustomer & { agent?: Agent, user?: User } = { ...customer };

      if (customer.userId) {
        const user = await this.dataSource
          .getRepository(User)
          .createQueryBuilder('user')
          .select(['user.id', 'user.fullName', 'user.email']) // Example: select only necessary fields
          .where('user.id = :userId', { userId: customer.userId })
          .getOne();
        if (user) {
          result.user = user;
        }
      }

      if (customer.agentId) {
        // Ensure Agent entity is correctly imported and repository is accessible
        const agent = await this.dataSource
          .getRepository(Agent)
          .createQueryBuilder('agent')
          .select(['agent.id', 'agent.name']) // Example: select only necessary fields
          .where('agent.id = :agentId', { agentId: customer.agentId })
          .getOne();
        if (agent) {
          result.agent = agent;
        }
      }
      return result;
    } catch (error) {
      this.logger.error(`(User) Lỗi khi tìm khách hàng theo ID ${id}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm khách hàng theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm danh sách khách hàng với phân trang (User context method)
   * @param userId ID người dùng (specific to user context filtering)
   * @param queryDto Tham số truy vấn (User DTO)
   * @returns Danh sách khách hàng với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserConvertCustomerDtoUser): Promise<PaginatedResult<UserConvertCustomer>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        platform,
        agentId, // User DTO has agentId
        sortBy = 'createdAt',
        sortDirection = 'DESC',
      } = queryDto;

      const skip = (page - 1) * limit;

      const query = this.createBaseQuery_user() // Use user base query
        .where('customer.userId = :userId', { userId }); // User-specific mandatory filter

      if (search) {
        query.andWhere('(customer.name ILIKE :search OR customer.phone ILIKE :search)', { search: `%${search}%` });
      }
      if (platform) {
        query.andWhere('customer.platform ILIKE :platform', { platform });
      }
      if (agentId) {
        query.andWhere('customer.agentId = :agentId', { agentId });
      }

      query.orderBy(`customer.${sortBy}`, sortDirection);
      query.skip(skip).take(limit);

      const [items, total] = await query.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`(User) Lỗi khi lấy danh sách khách hàng: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi lấy danh sách khách hàng: ${error.message}`);
    }
  }

  // --- Methods from Admin File ---

  /**
   * Tìm khách hàng chuyển đổi theo ID (Admin context method)
   * @param id ID của khách hàng chuyển đổi
   * @returns Khách hàng chuyển đổi hoặc null nếu không tìm thấy
   */
  async findUserConvertCustomerById(id: number): Promise<UserConvertCustomer | null> {
    this.logger.log(`(Admin) findUserConvertCustomerById: Tìm khách hàng chuyển đổi với ID: ${id}`);
    try {
      const result = await this.createBaseQuery_admin() // Use admin base query
        .where('customer.id = :id', { id })
        .getOne();
      this.logger.log(result
        ? `(Admin) findUserConvertCustomerById: Đã tìm thấy khách hàng chuyển đổi với ID: ${id}`
        : `(Admin) findUserConvertCustomerById: Không tìm thấy khách hàng chuyển đổi với ID: ${id}`);
      return result;
    } catch (error) {
      this.logger.error(`(Admin) findUserConvertCustomerById: Lỗi khi tìm khách hàng chuyển đổi với ID ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tìm khách hàng chuyển đổi theo ID kèm theo đơn hàng (Admin context method)
   * @param id ID của khách hàng chuyển đổi
   * @returns Khách hàng chuyển đổi với đơn hàng hoặc null nếu không tìm thấy
   */
  async findUserConvertCustomerByIdWithOrders(id: number): Promise<UserConvertCustomer | null> {
    this.logger.log(`(Admin) findUserConvertCustomerByIdWithOrders: Tìm khách hàng chuyển đổi với ID: ${id} kèm đơn hàng`);
    try {
      const result = await this.createBaseQueryWithOrders_admin() // Use admin specific base query
        .where('customer.id = :id', { id })
        .getOne();
      this.logger.log(result
        ? `(Admin) findUserConvertCustomerByIdWithOrders: Đã tìm thấy khách hàng chuyển đổi với ID: ${id} kèm đơn hàng`
        : `(Admin) findUserConvertCustomerByIdWithOrders: Không tìm thấy khách hàng chuyển đổi với ID: ${id}`);
      return result;
    } catch (error) {
      this.logger.error(`(Admin) findUserConvertCustomerByIdWithOrders: Lỗi khi tìm khách hàng chuyển đổi với ID ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tìm danh sách khách hàng chuyển đổi với phân trang, tìm kiếm và lọc (Admin context method)
   * @param queryParams Tham số truy vấn (Admin DTO)
   * @returns Danh sách khách hàng chuyển đổi phân trang
   */
  async findUserConvertCustomers(queryParams: QueryUserConvertCustomerDtoAdmin): Promise<PaginatedResult<UserConvertCustomer>> {
    this.logger.log(`(Admin) findUserConvertCustomers: Tìm kiếm khách hàng chuyển đổi với các tham số: ${JSON.stringify(queryParams)}`);
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        userId, // Admin DTO can filter by userId
        agentId, // Admin DTO can filter by agentId
        platform, // Admin DTO can filter by platform
        createdAtFrom,
        createdAtTo
      } = queryParams;

      const skip = (page - 1) * limit;
      this.logger.log(`(Admin) findUserConvertCustomers: Phân trang: skip ${skip}, limit ${limit}`);

      const queryBuilder = this.createBaseQuery_admin(); // Use admin base query
      this.logger.log(`(Admin) findUserConvertCustomers: Đã tạo query builder cơ bản`);

      if (search) {
        queryBuilder.andWhere('(customer.name ILIKE :search OR customer.phone ILIKE :search)', { search: `%${search}%` });
      }
      if (userId) {
        queryBuilder.andWhere('customer.userId = :userId', { userId });
      }
      if (agentId) {
        queryBuilder.andWhere('customer.agentId = :agentId', { agentId });
      }
      if (platform) {
        queryBuilder.andWhere('customer.platform ILIKE :platform', { platform });
      }
      if (createdAtFrom) {
        queryBuilder.andWhere('customer.createdAt >= :createdAtFrom', { createdAtFrom: Number(createdAtFrom) }); // Sử dụng số thay vì Date object
      }
      if (createdAtTo) {
        queryBuilder.andWhere('customer.createdAt <= :createdAtTo', { createdAtTo: Number(createdAtTo) }); // Sử dụng số thay vì Date object
      }

      this.logger.log(`(Admin) findUserConvertCustomers: Đang đếm tổng số bản ghi`);
      const totalItems = await queryBuilder.getCount();
      this.logger.log(`(Admin) findUserConvertCustomers: Tổng số bản ghi: ${totalItems}`);

      queryBuilder.orderBy(`customer.${sortBy}`, sortDirection).skip(skip).take(limit);

      this.logger.log(`(Admin) findUserConvertCustomers: Đang thực hiện truy vấn`);
      const items = await queryBuilder.getMany();
      this.logger.log(`(Admin) findUserConvertCustomers: Đã tìm thấy ${items.length}/${totalItems} khách hàng chuyển đổi`);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`(Admin) findUserConvertCustomers: Lỗi khi tìm kiếm khách hàng chuyển đổi: ${error.message}`);
      throw error;
    }
  }
}