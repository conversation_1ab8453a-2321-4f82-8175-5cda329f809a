import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response khi tạo nhóm trường tùy chỉnh thành công
 */
export class CustomGroupFormCreatedResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của nhóm trường tùy chỉnh',
    example: 2,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Thông tin bổ sung',
  })
  label: string;



  @Expose()
  @ApiProperty({
    description: 'ID sản phẩm liên kết',
    example: 101,
    nullable: true,
  })
  productId: number | null;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createAt: number;
}
