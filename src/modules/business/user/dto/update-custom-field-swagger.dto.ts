import { ApiProperty } from '@nestjs/swagger';
import {
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { FieldConfigDto } from './create-custom-field-swagger.dto';

/**
 * DTO cho cập nhật trường tùy chỉnh (chỉ dùng cho Swagger)
 */
export class UpdateCustomFieldSwaggerDto {
  /**
   * Thành phần UI
   * @example "Text Input"
   */
  @ApiProperty({
    description: 'Thành phần UI',
    example: 'Text Input',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Thành phần UI phải là chuỗi' })
  component?: string;

  /**
   * Cấu hình trường
   */
  @ApiProperty({
    description: 'Cấu hình trường',
    type: FieldConfigDto,
    example: {
      id: 'text-input',
      label: 'Họ và tên',
      type: 'text',
      required: true,
      validation: { minLength: 3, maxLength: 50, pattern: '^[a-zA-Z\\s]*$' },
      defaultValue: '',
      placeholder: 'Nhập họ và tên',
      variant: 'outlined',
      size: 'small'
    },
    required: false
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình trường phải là đối tượng' })
  config?: FieldConfigDto;

  /**
   * Cấu hình grid
   * @example { "i": "field1", "x": 0, "y": 0, "w": 6, "h": 2 }
   */
  @ApiProperty({
    description: 'Cấu hình grid',
    example: { i: 'field1', x: 0, y: 0, w: 6, h: 2 },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình grid phải là đối tượng' })
  grid?: any;
}
