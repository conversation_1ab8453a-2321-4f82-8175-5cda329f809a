import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

/**
 * DTO cho cập nhật trường tùy chỉnh
 */
export class UpdateCustomFieldDto {
  /**
   * Thành phần UI
   * @example "Text Input"
   */
  @ApiProperty({
    description: 'Thành phần UI',
    example: 'Text Input',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Thành phần UI phải là chuỗi' })
  component?: string;

  /**
   * Nhãn hiển thị
   * @example "Họ và tên đầy đủ"
   */
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Họ và tên đầy đủ',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nhãn hiển thị phải là chuỗi' })
  @MaxLength(255, { message: 'Nhãn hiển thị không được vượt quá 255 ký tự' })
  label?: string;

  /**
   * Loại trường
   * @example "text"
   */
  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Loại trường phải là chuỗi' })
  type?: string;

  /**
   * Trường bắt buộc hay không
   * @example true
   */
  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường bắt buộc phải là boolean' })
  required?: boolean;

  /**
   * Cấu hình JSON
   * @example { "validation": { "minLength": 5, "maxLength": 60, "pattern": "^[a-zA-Z0-9 ]*$" }, "placeholder": "Nhập họ và tên đầy đủ", "variant": "outlined", "size": "small" }
   */
  @ApiProperty({
    description: 'Cấu hình JSON',
    example: {
      validation: { minLength: 5, maxLength: 60, pattern: '^[a-zA-Z0-9 ]*$' },
      placeholder: 'Nhập họ và tên đầy đủ',
      variant: 'outlined',
      size: 'small',
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình JSON phải là đối tượng' })
  configJson?: any;
}
