import { Injectable, Logger } from '@nestjs/common';
import { UserConvertCustomerRepository } from '@modules/business/repositories';
import { PaginatedResult } from '@common/response';
import { AgentInfoDto, QueryUserConvertCustomerDto, UserConvertCustomerListItemDto, UserConvertCustomerResponseDto, UserInfoDto } from '../dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';


/**
 * Service xử lý logic nghiệp vụ cho khách hàng chuyển đổi
 */
@Injectable()
export class UserConvertCustomerService {
  private readonly logger = new Logger(UserConvertCustomerService.name);

  constructor(
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
  ) {}

  /**
   * <PERSON><PERSON>y danh sách khách hàng chuyển đổi của người dùng với phân trang
   * @param userId ID người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách khách hàng chuyển đổi với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserConvertCustomerDto): Promise<PaginatedResult<UserConvertCustomerListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách khách hàng chuyển đổi cho userId=${userId}`);

      // Lấy danh sách khách hàng chuyển đổi từ repository
      const result = await this.userConvertCustomerRepository.findAll(userId, queryDto);

      // Chuyển đổi sang DTO response
      const items = result.items.map(item => {
        return plainToInstance(UserConvertCustomerListItemDto, item, { excludeExtraneousValues: true });
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách khách hàng chuyển đổi: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_FIND_FAILED,
        `Lỗi khi lấy danh sách khách hàng chuyển đổi: ${error.message}`
      );
    }
  }

  /**
   * Lấy chi tiết khách hàng chuyển đổi theo ID
   * @param id ID khách hàng chuyển đổi
   * @param userId ID người dùng
   * @returns Chi tiết khách hàng chuyển đổi
   */
  async findById(id: number, userId: number): Promise<UserConvertCustomerResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết khách hàng chuyển đổi id=${id} cho userId=${userId}`);

      // Lấy khách hàng chuyển đổi từ repository
      const customer = await this.userConvertCustomerRepository.findById(id);

      // Kiểm tra khách hàng chuyển đổi tồn tại
      if (!customer) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
          `Không tìm thấy khách hàng chuyển đổi với ID ${id}`
        );
      }

      // Kiểm tra khách hàng chuyển đổi thuộc về người dùng
      if (customer.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_ACCESS_DENIED,
          `Bạn không có quyền truy cập khách hàng chuyển đổi này`
        );
      }

      // Xử lý thông tin agent nếu có
      let agentInfo: AgentInfoDto | undefined;
      if (customer.agent) {
        agentInfo = {
          id: customer.agent.id,
          name: customer.agent.name || '',
          avatar: customer.agent.avatar,
        };
      }

      // Xử lý thông tin user nếu có
      let userInfo: UserInfoDto | undefined;
      if (customer.user) {
        userInfo = {
          id: customer.user.id,
          fullName: customer.user.fullName || '',
          email: customer.user.email || '',
          phoneNumber: customer.user.phoneNumber || '',
          avatar: customer.user.avatar,
        };
      }

      // Chuyển đổi sang DTO response
      const responseDto = plainToInstance(UserConvertCustomerResponseDto, customer, { excludeExtraneousValues: true });

      // Gán thông tin agent nếu có
      if (agentInfo) {
        responseDto.agent = agentInfo;
      }

      // Gán thông tin user nếu có
      if (userInfo) {
        responseDto.user = userInfo;
      }

      return responseDto;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết khách hàng chuyển đổi: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_FIND_FAILED,
        `Lỗi khi lấy chi tiết khách hàng chuyển đổi: ${error.message}`
      );
    }
  }
}
