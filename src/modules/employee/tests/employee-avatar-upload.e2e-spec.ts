import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '@/app.module';
import { JwtService } from '@nestjs/jwt';
import { EmployeeRepository } from '@modules/employee/repositories';
import { S3Service } from '@/shared/services/s3.service';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { TimeIntervalEnum } from '@/shared/utils';

describe('Employee Avatar Upload API (e2e)', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let employeeRepository: EmployeeRepository;
  let s3Service: S3Service;
  let accessToken: string;
  let testEmployeeId: number;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    jwtService = app.get<JwtService>(JwtService);
    employeeRepository = app.get<EmployeeRepository>(EmployeeRepository);
    s3Service = app.get<S3Service>(S3Service);

    // Tạo một nhân viên test nếu chưa có
    const testEmployee = await createTestEmployee();
    testEmployeeId = testEmployee.id;

    // Tạo token JWT cho nhân viên test
    accessToken = createTestToken(testEmployee);
  });

  afterAll(async () => {
    await app.close();
  });

  /**
   * Hàm tạo nhân viên test
   */
  async function createTestEmployee() {
    // Kiểm tra xem đã có nhân viên test chưa
    let employee = await employeeRepository.findByEmail('<EMAIL>');
    
    if (!employee) {
      // Tạo nhân viên test mới
      employee = await employeeRepository.createEmployee(
        {
          fullName: 'Test Employee',
          email: '<EMAIL>',
          phoneNumber: '0987654321',
          address: 'Test Address',
          enable: true,
        },
        // Mật khẩu đã hash (ví dụ)
        '$2b$10$abcdefghijklmnopqrstuvwxyz123456789'
      );
    }
    
    return employee;
  }

  /**
   * Hàm tạo token JWT cho nhân viên test
   */
  function createTestToken(employee: any) {
    const payload = {
      sub: employee.id,
      email: employee.email,
      type: 'employee',
    };
    
    return jwtService.sign(payload, {
      secret: process.env.JWT_SECRET || 'test-secret',
      expiresIn: '1h',
    });
  }

  describe('POST /employees/:id/avatar/upload-url', () => {
    it('should return a temporary URL for avatar upload', async () => {
      // Mock S3Service.createPresignedWithID để trả về URL giả
      jest.spyOn(s3Service, 'createPresignedWithID').mockResolvedValue('https://mock-presigned-url.com');
      
      const response = await request(app.getHttpServer())
        .post(`/employees/${testEmployeeId}/avatar/upload-url`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          imageType: ImageTypeEnum.JPEG,
          maxSize: 2097152, // 2MB
        })
        .expect(200);

      // Kiểm tra cấu trúc phản hồi
      expect(response.body).toHaveProperty('code', 200);
      expect(response.body).toHaveProperty('message', 'Tạo URL tải lên avatar thành công');
      expect(response.body).toHaveProperty('result');
      expect(response.body.result).toHaveProperty('uploadUrl');
      expect(response.body.result).toHaveProperty('avatarKey');
      expect(response.body.result).toHaveProperty('expiresIn');
      expect(response.body.result).toHaveProperty('expiresAt');
      
      // Kiểm tra URL tạm thời
      expect(response.body.result.uploadUrl).toBe('https://mock-presigned-url.com');
      
      // Kiểm tra thời gian hết hạn
      expect(response.body.result.expiresIn).toBe(TimeIntervalEnum.FIVE_MINUTES);
      expect(typeof response.body.result.expiresAt).toBe('number');
    });

    it('should return 400 when imageType is invalid', async () => {
      const response = await request(app.getHttpServer())
        .post(`/employees/${testEmployeeId}/avatar/upload-url`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          imageType: 'invalid-type',
          maxSize: 2097152,
        })
        .expect(400);

      expect(response.body).toHaveProperty('code');
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Loại hình ảnh không hợp lệ');
    });

    it('should return 404 when employee does not exist', async () => {
      const nonExistentEmployeeId = 99999;
      
      // Mock findById để trả về lỗi
      jest.spyOn(employeeRepository, 'findById').mockRejectedValue(new Error('Employee not found'));
      
      const response = await request(app.getHttpServer())
        .post(`/employees/${nonExistentEmployeeId}/avatar/upload-url`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          imageType: ImageTypeEnum.JPEG,
          maxSize: 2097152,
        })
        .expect(404);

      expect(response.body).toHaveProperty('code');
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Không tìm thấy nhân viên');
    });

    it('should return 401 when not authenticated', async () => {
      await request(app.getHttpServer())
        .post(`/employees/${testEmployeeId}/avatar/upload-url`)
        .send({
          imageType: ImageTypeEnum.JPEG,
          maxSize: 2097152,
        })
        .expect(401);
    });
  });

  describe('POST /employees', () => {
    it('should create employee with avatar upload URL', async () => {
      // Mock S3Service.createPresignedWithID để trả về URL giả
      jest.spyOn(s3Service, 'createPresignedWithID').mockResolvedValue('https://mock-presigned-url.com');
      
      // Mock employeeRepository để tránh xung đột email
      jest.spyOn(employeeRepository, 'findByEmail').mockResolvedValue(null);
      
      const uniqueEmail = `test.employee.${Date.now()}@example.com`;
      
      const response = await request(app.getHttpServer())
        .post('/employees')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          fullName: 'New Test Employee',
          email: uniqueEmail,
          phoneNumber: '0987654321',
          password: 'Password123!',
          address: 'Test Address',
          avatarImageType: ImageTypeEnum.JPEG,
          avatarMaxSize: 2097152,
        })
        .expect(200);

      // Kiểm tra cấu trúc phản hồi
      expect(response.body).toHaveProperty('code', 200);
      expect(response.body).toHaveProperty('message', 'Tạo nhân viên thành công');
      expect(response.body).toHaveProperty('result');
      expect(response.body.result).toHaveProperty('id');
      expect(response.body.result).toHaveProperty('fullName', 'New Test Employee');
      expect(response.body.result).toHaveProperty('email', uniqueEmail);
      
      // Kiểm tra thông tin URL tạm thời
      expect(response.body.result).toHaveProperty('avatarUploadUrl');
      expect(response.body.result).toHaveProperty('avatarKey');
      expect(response.body.result).toHaveProperty('avatarUrlExpiresAt');
      
      // Kiểm tra URL tạm thời
      expect(response.body.result.avatarUploadUrl).toBe('https://mock-presigned-url.com');
    });
  });
});
