import { Column, Entity, JoinTable, ManyToMany, PrimaryColumn } from 'typeorm';
import { Permission } from './permission.entity';

/**
 * Entity đại diện cho bảng employee_roles trong cơ sở dữ liệu
 * <PERSON>h sách role của nhân viên
 */
@Entity('employee_roles')
export class EmployeeRole {
  /**
   * ID của role
   */
  @PrimaryColumn({ name: 'id' })
  id: number;

  /**
   * Tên role
   */
  @Column({ name: 'name', length: 255, comment: 'Tên role' })
  name: string;

  /**
   * <PERSON><PERSON> tả role
   */
  @Column({ name: 'description', length: 500, comment: '<PERSON>ô tả' })
  description: string;

  /**
   * <PERSON>uan hệ nhiều-nhiều với bảng permissions
   */
  @ManyToMany(() => Permission)
  @JoinTable({
    name: 'employee_role_has_permission',
    joinColumn: {
      name: 'role_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'permission_id',
      referencedColumnName: 'id',
    },
  })
  permissions: Permission[];
}
