import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { AffiliatePointConversionHistory } from '../entities/affiliate-point-conversion-history.entity';
import { AffiliatePointConversionQueryDto as UserPointConversionQueryDto } from '../user/dto';
import { AffiliatePointConversionQueryDto as AdminPointConversionQueryDto } from '../admin/dto';
import { PaginatedResult } from '@/common/response';
import { PointConversionStatus } from '../enums';

/**
 * Repository cho AffiliatePointConversionHistory
 * Extends Repository<AffiliatePointConversionHistory> theo Repository Standard #2
 */
@Injectable()
export class AffiliatePointConversionHistoryRepository extends Repository<AffiliatePointConversionHistory> {
  constructor(dataSource: DataSource) {
    super(AffiliatePointConversionHistory, dataSource.createEntityManager());
  }

  /**
   * Tì<PERSON> lịch sử chuyển đổi điểm theo ID
   * @param id ID của bản ghi chuyển đổi
   * @returns Thông tin lịch sử chuyển đổi hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<AffiliatePointConversionHistory | null> {
    return this.createQueryBuilder('conversion')
      .where('conversion.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm lịch sử chuyển đổi điểm theo ID tài khoản affiliate
   * @param affiliateAccountId ID tài khoản affiliate
   * @returns Danh sách lịch sử chuyển đổi
   */
  async findByAffiliateAccountId(
    affiliateAccountId: number,
  ): Promise<AffiliatePointConversionHistory[]> {
    return this.createQueryBuilder('conversion')
      .where('conversion.affiliateAccountId = :affiliateAccountId', { affiliateAccountId })
      .orderBy('conversion.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm danh sách lịch sử chuyển đổi điểm với phân trang (User)
   * @param affiliateAccountId ID tài khoản affiliate
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử chuyển đổi với phân trang
   */
  async findWithPagination(
    affiliateAccountId: number,
    queryDto: UserPointConversionQueryDto,
  ): Promise<PaginatedResult<AffiliatePointConversionHistory>> {
    const {
      page = 1,
      limit = 10,
      begin,
      end,
      status,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query
    const queryBuilder = this.createQueryBuilder('conversion').where(
      'conversion.affiliateAccountId = :affiliateAccountId',
      {
        affiliateAccountId,
      },
    );

    // Thêm điều kiện thời gian nếu có
    if (begin) {
      queryBuilder.andWhere('conversion.createdAt >= :begin', { begin });
    }

    if (end) {
      queryBuilder.andWhere('conversion.createdAt <= :end', { end });
    }

    // Thêm điều kiện trạng thái nếu có
    if (status) {
      queryBuilder.andWhere('conversion.status = :status', { status });
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(`conversion.${sortBy}`, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu
    const items = await queryBuilder.getMany();

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Tìm danh sách lịch sử chuyển đổi điểm với phân trang (Admin)
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử chuyển đổi với phân trang
   */
  async findWithPaginationForAdmin(
    queryDto: AdminPointConversionQueryDto,
  ): Promise<PaginatedResult<AffiliatePointConversionHistory>> {
    const {
      page = 1,
      limit = 10,
      begin,
      end,
      status,
      affiliateAccountId,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query
    const queryBuilder = this.createQueryBuilder('conversion');

    // Thêm điều kiện tài khoản affiliate nếu có
    if (affiliateAccountId) {
      queryBuilder.andWhere('conversion.affiliateAccountId = :affiliateAccountId', {
        affiliateAccountId,
      });
    }

    // Thêm điều kiện thời gian nếu có
    if (begin) {
      queryBuilder.andWhere('conversion.createdAt >= :begin', { begin });
    }

    if (end) {
      queryBuilder.andWhere('conversion.createdAt <= :end', { end });
    }

    // Thêm điều kiện trạng thái nếu có
    if (status) {
      queryBuilder.andWhere('conversion.status = :status', { status });
    }

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      // Tìm kiếm theo ID của conversion
      const searchNumber = !isNaN(Number(search)) ? Number(search) : null;
      if (searchNumber !== null) {
        queryBuilder.andWhere('conversion.id = :searchId', { searchId: searchNumber });
      }
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(`conversion.${sortBy}`, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu
    const items = await queryBuilder.getMany();

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Tạo bản ghi lịch sử chuyển đổi điểm mới
   * @param data Dữ liệu bản ghi mới
   * @returns Bản ghi đã tạo
   */
  async createPointConversion(
    data: Partial<AffiliatePointConversionHistory>,
  ): Promise<AffiliatePointConversionHistory> {
    const newConversion = this.create({
      ...data,
      status: data.status || PointConversionStatus.SUCCESS,
      createdAt: data.createdAt || Math.floor(Date.now() / 1000),
    });

    return this.save(newConversion);
  }

  /**
   * Tính tổng số điểm đã chuyển đổi của một tài khoản affiliate
   * @param affiliateAccountId ID tài khoản affiliate
   * @returns Tổng số điểm đã chuyển đổi
   */
  async calculateTotalPointsConverted(
    affiliateAccountId: number,
  ): Promise<number> {
    const result = await this.createQueryBuilder('conversion')
      .select('SUM(conversion.pointsConverted)', 'total')
      .where('conversion.affiliateAccountId = :affiliateAccountId', {
        affiliateAccountId,
      })
      .andWhere('conversion.status = :status', {
        status: PointConversionStatus.SUCCESS,
      })
      .getRawOne();

    return parseInt(result?.total) || 0;
  }
}
