import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AffiliateAccountService } from '@modules/affiliate/admin/services';
import {
  AffiliateAccountDto,
  AffiliateAccountQueryDto,
  AffiliateAccountStatus,
} from '../dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';

/**
 * Controller xử lý các API liên quan đến quản lý tài khoản affiliate cho admin
 */
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.ADMIN_AFFILIATE_ACCOUNT)
@ApiExtraModels(
  ApiResponseDto,
  AffiliateAccountDto,
  PaginatedResult,
  ApiErrorResponseDto
)
@Controller('admin/affiliate/accounts')
export class AffiliateAccountController {
  constructor(
    private readonly affiliateAccountService: AffiliateAccountService,
  ) {}

  /**
   * Lấy danh sách tài khoản affiliate với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tài khoản affiliate với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách tài khoản affiliate',
    description: 'Lấy danh sách tài khoản affiliate với phân trang, hỗ trợ tìm kiếm và lọc theo trạng thái'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách tài khoản affiliate thành công',
    schema: ApiResponseDto.getPaginatedSchema(AffiliateAccountDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED
  )
  async getAccounts(
    @Query() queryDto: AffiliateAccountQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateAccountDto>>> {
    const accounts = await this.affiliateAccountService.getAccounts(queryDto);
    return ApiResponseDto.paginated(
      accounts,
      'Lấy danh sách tài khoản affiliate thành công',
    );
  }

  /**
   * Lấy thông tin chi tiết tài khoản affiliate theo ID
   * @param id ID của tài khoản affiliate
   * @returns Thông tin chi tiết tài khoản affiliate
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết tài khoản affiliate',
    description: 'Lấy thông tin chi tiết của một tài khoản affiliate dựa trên ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của tài khoản affiliate',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết tài khoản affiliate thành công',
    schema: ApiResponseDto.getSchema(AffiliateAccountDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND
  )
  async getAccountById(
    @Param('id') id: number,
  ): Promise<ApiResponseDto<AffiliateAccountDto>> {
    const account = await this.affiliateAccountService.getAccountById(id);
    return ApiResponseDto.success(
      account,
      'Lấy thông tin chi tiết tài khoản affiliate thành công',
    );
  }

  /**
   * Cập nhật trạng thái tài khoản affiliate
   * @param id ID của tài khoản affiliate
   * @param body Thông tin cập nhật
   * @returns Thông tin tài khoản affiliate đã cập nhật
   */
  @Patch(':id/status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái tài khoản affiliate',
    description: 'Cập nhật trạng thái của một tài khoản affiliate (ACTIVE, INACTIVE, PENDING, REJECTED)'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của tài khoản affiliate',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái tài khoản affiliate thành công',
    schema: ApiResponseDto.getSchema(AffiliateAccountDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
    AFFILIATE_ERROR_CODES.ACCOUNT_STATUS_UPDATE_FAILED
  )
  async updateAccountStatus(
    @Param('id') id: number,
    @Body() body: { status: AffiliateAccountStatus },
  ): Promise<ApiResponseDto<AffiliateAccountDto>> {
    const account = await this.affiliateAccountService.updateAccountStatus(
      id,
      body.status,
    );
    return ApiResponseDto.updated(
      account,
      'Cập nhật trạng thái tài khoản affiliate thành công',
    );
  }
}
