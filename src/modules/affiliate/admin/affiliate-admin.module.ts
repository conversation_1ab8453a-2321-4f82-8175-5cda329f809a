import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import {
  AffiliateAccount,
  AffiliateClick,
  AffiliateCustomerOrder,
  AffiliateWithdrawHistory,
  AffiliateRank,
  AffiliatePointConversionHistory,
  AffiliateContract
} from '@modules/affiliate/entities';
import { User } from '@modules/user/entities';

// Controllers
import {
  AffiliateOverviewController,
  AffiliateAccountController,
  AffiliateWithdrawalController,
  AffiliateRankController,
  AffiliatePointConversionController,
  AffiliateClickController,
  AffiliateContractController
} from './controllers';

// Services
import {
  AffiliateOverviewService,
  AffiliateAccountService,
  AffiliateWithdrawalService,
  AffiliateRankService,
  AffiliatePointConversionService,
  AffiliateClickService,
  AffiliateContractService
} from './services';

// Repositories
import {
  AffiliateAccountRepository,
  AffiliateClickRepository,
  AffiliateCustomerOrderRepository,
  AffiliateWithdrawHistoryRepository,
  AffiliateRankRepository,
  AffiliatePointConversionHistoryRepository,
  AffiliateContractRepository
} from '@modules/affiliate/repositories';
import { UserRepository } from '@modules/user/repositories';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AffiliateAccount,
      AffiliateClick,
      AffiliateCustomerOrder,
      AffiliateWithdrawHistory,
      AffiliateRank,
      AffiliatePointConversionHistory,
      AffiliateContract,
      User
    ])
  ],
  controllers: [
    AffiliateOverviewController,
    AffiliateAccountController,
    AffiliateWithdrawalController,
    AffiliateRankController,
    AffiliatePointConversionController,
    AffiliateClickController,
    AffiliateContractController
  ],
  providers: [
    // Services
    AffiliateOverviewService,
    AffiliateAccountService,
    AffiliateWithdrawalService,
    AffiliateRankService,
    AffiliatePointConversionService,
    AffiliateClickService,
    AffiliateContractService,

    // Repositories
    AffiliateAccountRepository,
    AffiliateClickRepository,
    AffiliateCustomerOrderRepository,
    AffiliateWithdrawHistoryRepository,
    AffiliateRankRepository,
    AffiliatePointConversionHistoryRepository,
    AffiliateContractRepository,
    UserRepository
  ],
  exports: [
    AffiliateOverviewService,
    AffiliateAccountService,
    AffiliateWithdrawalService,
    AffiliateRankService,
    AffiliatePointConversionService,
    AffiliateClickService,
    AffiliateContractService
  ]
})
export class AffiliateAdminModule {}
