import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsIn, IsOptional, IsString, IsUUID } from 'class-validator';
import { QueryDto } from '@common/dto';

/**
 * Enum cho các trường sắp xếp
 */
export enum FacebookPageSortBy {
  CREATED_AT = 'created_at',
  PAGE_NAME = 'page_name',
  PERSONAL_NAME = 'personal_name',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * DTO cho truy vấn danh sách trang Facebook
 */
export class FacebookPageQueryDto extends QueryDto {
  @ApiProperty({
    description: 'ID của tài khoản Facebook cá nhân',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsUUID()
  facebookPersonalId?: string;

  @ApiProperty({
    description: 'Trường sắp xếp',
    required: false,
    enum: FacebookPageSortBy,
    default: FacebookPageSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(FacebookPageSortBy)
  sortBy?: FacebookPageSortBy = FacebookPageSortBy.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp (ASC hoặc DESC)',
    required: false,
    enum: SortOrder,
    default: SortOrder.DESC,
  })
  @IsOptional()
  @IsIn(['ASC', 'DESC', 'asc', 'desc'])
  sortOrder?: SortOrder = SortOrder.DESC;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm theo tên trang',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  keyword?: string;
}
