import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho việc trả về thông tin website
 */
export class WebsiteResponseDto {
  @ApiProperty({
    description: 'ID của website',
    example: 1,
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'ID của người dùng sở hữu website',
    example: 1,
  })
  @Expose()
  userId: number;

  @ApiProperty({
    description: 'Tên website do người dùng đặt',
    example: 'Website của tôi',
  })
  @Expose()
  websiteName: string;

  @ApiProperty({
    description: 'Tên miền hoặc địa chỉ host của website',
    example: 'example.com',
  })
  @Expose()
  host: string;

  @ApiProperty({
    description: 'Trạng thái xác minh của website (TRUE nếu đã xác minh)',
    example: false,
  })
  @Expose()
  verify: boolean;

  @ApiProperty({
    description: 'Thời điểm tạo bản ghi',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'ID agent được kết nối với website',
    example: '123e4567-e89b-12d3-a456-426614174000',
    nullable: true,
  })
  @Expose()
  agentId: string;
}
