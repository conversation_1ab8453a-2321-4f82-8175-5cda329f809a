import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { TaskStatus } from '@modules/task/enums';

/**
 * Entity đại diện cho bảng user_tasks trong cơ sở dữ liệu
 * Bảng lưu thông tin định nghĩa của nhiệm vụ
 */
@Entity('user_tasks')
export class UserTask {
  /**
   * Khóa chính của bảng user_tasks, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'task_id' })
  taskId: string;

  /**
   * ID người dùng tạo nhiệm vụ
   */
  @Column({ name: 'user_id', type: 'int', nullable: false })
  userId: number;

  /**
   * ID của agent (tham chiếu đến bảng agents)
   * <PERSON><PERSON> thể null nếu task không liên kết với agent cụ thể
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId: string | null;

  /**
   * Tên của nhiệm vụ
   */
  @Column({ name: 'task_name', type: 'varchar', length: 255, nullable: false })
  taskName: string;

  /**
   * Mô tả chi tiết nhiệm vụ
   */
  @Column({ name: 'task_description', type: 'text', nullable: true })
  taskDescription: string;

  /**
   * Trạng thái nhiệm vụ (active, suspended, v.v.)
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.PENDING,
  })
  status: TaskStatus;

  /**
   * Trạng thái hoạt động của nhiệm vụ
   */
  @Column({ name: 'active', type: 'boolean', default: true })
  active: boolean;

  /**
   * Thời điểm tạo nhiệm vụ (Unix epoch)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật nhiệm vụ (Unix epoch)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;

  /**
   * Thời điểm xóa nhiệm vụ (Unix epoch) - soft delete
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number;
}
