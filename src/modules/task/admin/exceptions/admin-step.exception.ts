import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common';
import { STEP_ERROR_CODES } from '@modules/task/exceptions';

/**
 * Mã lỗi cho các thao tác liên quan đến step của admin
 * Phạm vi mã lỗi: 10500 - 10599
 */
export const ADMIN_STEP_ERROR_CODES = {
  // Kế thừa các mã lỗi từ STEP_ERROR_CODES
  ...STEP_ERROR_CODES,
  
  // Mã lỗi riêng cho admin
  ADMIN_STEP_NOT_FOUND: new ErrorCode(10500, 'Không tìm thấy bước admin', HttpStatus.NOT_FOUND),
  ADMIN_STEP_CREATION_FAILED: new ErrorCode(10501, 'Tạo bước admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  ADMIN_STEP_UPDATE_FAILED: new ErrorCode(10502, 'Cập nhật bước admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  ADMIN_STEP_DELETE_FAILED: new ErrorCode(10503, 'Xóa bước admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  ADMIN_STEP_FETCH_FAILED: new ErrorCode(10504, 'Lấy thông tin bước admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi xác thực và phân quyền
  ADMIN_STEP_UNAUTHORIZED: new ErrorCode(10510, 'Không có quyền truy cập bước admin này', HttpStatus.FORBIDDEN),
  
  // Lỗi dữ liệu
  ADMIN_STEP_INVALID_DATA: new ErrorCode(10520, 'Dữ liệu bước admin không hợp lệ', HttpStatus.BAD_REQUEST),
  ADMIN_STEP_REORDER_FAILED: new ErrorCode(10521, 'Sắp xếp lại thứ tự các bước thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
};
