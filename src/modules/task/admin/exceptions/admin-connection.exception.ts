import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common';
import { CONNECTION_ERROR_CODES } from '../../exceptions/connection.exceptions';

/**
 * Mã lỗi cho các thao tác liên quan đến connection của admin
 * Phạm vi mã lỗi: 10600 - 10699
 */
export const ADMIN_CONNECTION_ERROR_CODES = {
  // Kế thừa các mã lỗi từ CONNECTION_ERROR_CODES
  ...CONNECTION_ERROR_CODES,
  
  // Mã lỗi riêng cho admin
  ADMIN_CONNECTION_NOT_FOUND: new ErrorCode(10600, 'Không tìm thấy kết nối admin', HttpStatus.NOT_FOUND),
  ADMIN_CONNECTION_CREATION_FAILED: new ErrorCode(10601, 'Tạo kết nối admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  ADMIN_CONNECTION_UPDATE_FAILED: new ErrorCode(10602, 'Cập nhật kết nối admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  ADMIN_CONNECTION_DELETE_FAILED: new ErrorCode(10603, 'Xóa kết nối admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  ADMIN_CONNECTION_FETCH_FAILED: new ErrorCode(10604, 'Lấy thông tin kết nối admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi xác thực và phân quyền
  ADMIN_CONNECTION_UNAUTHORIZED: new ErrorCode(10610, 'Không có quyền truy cập kết nối admin này', HttpStatus.FORBIDDEN),
  
  // Lỗi dữ liệu
  ADMIN_CONNECTION_INVALID_DATA: new ErrorCode(10620, 'Dữ liệu kết nối admin không hợp lệ', HttpStatus.BAD_REQUEST),
};
