import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common';
import { TASK_ERROR_CODES } from '@modules/task/exceptions';

/**
 * Mã lỗi cho các thao tác liên quan đến task của admin
 * Phạm vi mã lỗi: 10400 - 10499
 */
export const ADMIN_TASK_ERROR_CODES = {
  // Kế thừa các mã lỗi từ TASK_ERROR_CODES
  ...TASK_ERROR_CODES,

  // Mã lỗi riêng cho admin
  ADMIN_TASK_NOT_FOUND: new ErrorCode(10400, 'Không tìm thấy nhiệm vụ admin', HttpStatus.NOT_FOUND),
  ADMIN_TASK_CREATION_FAILED: new ErrorCode(10401, 'Tạo nhiệm vụ admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  ADMIN_TASK_UPDATE_FAILED: new ErrorCode(10402, 'Cập nhật nhiệm vụ admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  ADMIN_TASK_DELETE_FAILED: new ErrorCode(10403, 'Xóa nhiệm vụ admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  ADMIN_TASK_FETCH_FAILED: new ErrorCode(10404, 'Lấy thông tin nhiệm vụ admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),

  // Lỗi xác thực và phân quyền
  ADMIN_TASK_UNAUTHORIZED: new ErrorCode(10410, 'Không có quyền truy cập nhiệm vụ admin này', HttpStatus.FORBIDDEN),

  // Lỗi dữ liệu
  ADMIN_TASK_INVALID_DATA: new ErrorCode(10420, 'Dữ liệu nhiệm vụ admin không hợp lệ', HttpStatus.BAD_REQUEST),
  ADMIN_TASK_MISSING_AGENT: new ErrorCode(10421, 'Thiếu thông tin agent cho nhiệm vụ admin', HttpStatus.BAD_REQUEST),
  ADMIN_TASK_AGENT_NOT_FOUND: new ErrorCode(10422, 'Không tìm thấy agent cho nhiệm vụ admin', HttpStatus.NOT_FOUND),
  ADMIN_TASK_AGENT_NOT_AVAILABLE: new ErrorCode(10423, 'Agent này không khả dụng cho nhiệm vụ admin', HttpStatus.BAD_REQUEST),
};
