import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common';
import { EXECUTION_ERROR_CODES } from '@modules/task/exceptions';

/**
 * Mã lỗi cho các thao tác liên quan đến execution của admin
 * Phạm vi mã lỗi: 10700 - 10799
 */
export const ADMIN_EXECUTION_ERROR_CODES = {
  // Kế thừa các mã lỗi từ EXECUTION_ERROR_CODES
  ...EXECUTION_ERROR_CODES,
  
  // Mã lỗi riêng cho admin
  ADMIN_EXECUTION_NOT_FOUND: new ErrorCode(10700, 'Không tìm thấy phiên thực thi admin', HttpStatus.NOT_FOUND),
  ADMIN_EXECUTION_FETCH_FAILED: new ErrorCode(10701, 'Lấy thông tin phiên thực thi admin thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi xác thực và phân quyền
  ADMIN_EXECUTION_UNAUTHORIZED: new ErrorCode(10710, 'Không có quyền truy cập phiên thực thi admin này', HttpStatus.FORBIDDEN),
};
