import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {  IsNotEmpty, IsObject, IsOptional, IsString, MaxLength } from 'class-validator';
import { StepType } from '@modules/task/interfaces/step-config.interface';

/**
 * DTO cho việc tạo mới step
 */
export class CreateStepDto {
  /**
   * Tên của bước
   */
  @ApiProperty({
    description: 'Tên của bước',
    example: 'Bước phân tích dữ liệu'
  })
  @IsNotEmpty({ message: 'Tên bước là bắt buộc' })
  @IsString({ message: 'Tên bước phải là chuỗi' })
  @MaxLength(255, { message: 'Tên bước không được vượt quá 255 ký tự' })
  stepName: string;

  /**
   * <PERSON>ô tả chi tiết của bước
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON> tả chi tiết của bước',
    example: '<PERSON><PERSON><PERSON><PERSON> này sẽ phân tích dữ liệu từ các nguồn khác nhau'
  })
  @IsOptional()
  @IsString({ message: 'Mô tả bước phải là chuỗi' })
  stepDescription?: string;

  /**
   * Loại bước (prompt, google_sheet, google_doc, google_calendar, email, facebook_page, gen_image, gen_video, agent)
   */
  @ApiProperty({
    description: 'Loại bước',
    enum: ['prompt', 'google_sheet', 'google_doc', 'google_calendar', 'email', 'facebook_page', 'gen_image', 'gen_video', 'agent'],
    example: 'prompt'
  })
  @IsNotEmpty({ message: 'Loại bước là bắt buộc' })
  stepType: StepType;

  /**
   * Cấu hình chi tiết của bước ở dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình chi tiết của bước ở dạng JSON',
    examples: {
      prompt: {
        summary: 'Cấu hình cho bước loại prompt',
        value: {
          promptText: 'Nhập thông tin khách hàng',
          inputType: 'text',
          required: true,
          defaultValue: ''
        }
      },
      google_sheet: {
        summary: 'Cấu hình cho bước loại google_sheet',
        value: {
          sheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          range: 'Sheet1!A1:B10',
          action: 'read',
          credentialId: '123e4567-e89b-12d3-a456-426614174000'
        }
      },
      google_doc: {
        summary: 'Cấu hình cho bước loại google_doc',
        value: {
          docId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          action: 'append',
          content: 'Nội dung cần thêm vào tài liệu',
          credentialId: '123e4567-e89b-12d3-a456-426614174000'
        }
      },
      google_calendar: {
        summary: 'Cấu hình cho bước loại google_calendar',
        value: {
          calendarId: 'primary',
          action: 'create_event',
          eventDetails: {
            summary: 'Cuộc họp với khách hàng',
            description: 'Thảo luận về dự án mới',
            start: { dateTime: '2023-06-01T09:00:00+07:00', timeZone: 'Asia/Ho_Chi_Minh' },
            end: { dateTime: '2023-06-01T10:00:00+07:00', timeZone: 'Asia/Ho_Chi_Minh' },
            attendees: [{ email: '<EMAIL>' }]
          },
          credentialId: '123e4567-e89b-12d3-a456-426614174000'
        }
      },
      email: {
        summary: 'Cấu hình cho bước loại email',
        value: {
          to: ['<EMAIL>'],
          subject: 'Thông báo mới',
          body: 'Xin chào,\n\nĐây là email thông báo từ hệ thống.\n\nTrân trọng,\nAdmin'
        }
      },
      facebook_page: {
        summary: 'Cấu hình cho bước loại facebook_page',
        value: {
          pageId: '123456789012345',
          action: 'post',
          content: {
            message: 'Thông báo mới từ công ty chúng tôi!',
            link: 'https://example.com/thong-bao'
          },
          credentialId: '123e4567-e89b-12d3-a456-426614174000'
        }
      },
      gen_image: {
        summary: 'Cấu hình cho bước loại gen_image',
        value: {
          prompt: 'Một bức tranh phong cảnh về Việt Nam với núi non hùng vĩ',
          resolution: '512x512',
          model: 'dalle-2'
        }
      },
      gen_video: {
        summary: 'Cấu hình cho bước loại gen_video',
        value: {
          prompt: 'Một video ngắn về thành phố Hồ Chí Minh với các toà nhà cao tầng',
          duration: 15,
          resolution: '1280x720',
          model: 'runway'
        }
      },
      agent: {
        summary: 'Cấu hình cho bước loại agent',
        value: {
          agentId: '123e4567-e89b-12d3-a456-426614174000',
          parameters: {
            input: 'Dữ liệu đầu vào cho agent',
            options: {
              temperature: 0.7,
              maxTokens: 1000
            }
          }
        }
      }
    }
  })
  @IsNotEmpty({ message: 'Cấu hình bước là bắt buộc' })
  @IsObject({ message: 'Cấu hình bước phải là đối tượng JSON' })
  stepConfig: Record<string, any>;
}
