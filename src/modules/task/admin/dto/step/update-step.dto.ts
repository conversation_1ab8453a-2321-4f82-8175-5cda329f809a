import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsObject, IsOptional, IsString, MaxLength } from 'class-validator';
import { StepType } from '@modules/task/interfaces/step-config.interface';

/**
 * DTO cho việc cập nhật step
 */
export class UpdateStepDto {
  /**
   * Tên của bước
   */
  @ApiPropertyOptional({
    description: 'Tên của bước',
    example: 'Bước phân tích dữ liệu (đã cập nhật)'
  })
  @IsOptional()
  @IsString({ message: 'Tên bước phải là chuỗi' })
  @MaxLength(255, { message: 'Tên bước không được vượt quá 255 ký tự' })
  stepName?: string;

  /**
   * Mô tả chi tiết của bước
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON> tả chi tiết của bước',
    example: '<PERSON><PERSON><PERSON><PERSON> này sẽ phân tích dữ liệu từ các nguồn kh<PERSON> (đã cập nhật)'
  })
  @IsOptional()
  @IsString({ message: 'Mô tả bước phải là chuỗi' })
  stepDescription?: string;

  /**
   * Loại bước (prompt, google_sheet, google_doc, google_calendar, email, facebook_page, gen_image, gen_video, agent)
   */
  @ApiPropertyOptional({
    description: 'Loại bước',
    enum: ['prompt', 'google_sheet', 'google_doc', 'google_calendar', 'email', 'facebook_page', 'gen_image', 'gen_video', 'agent'],
    example: 'prompt'
  })
  @IsOptional()
  stepType?: StepType;

  /**
   * Cấu hình chi tiết của bước ở dạng JSON
   */
  @ApiPropertyOptional({
    description: 'Cấu hình chi tiết của bước ở dạng JSON',
    examples: {
      prompt: {
        summary: 'Cấu hình cho bước loại prompt',
        value: {
          promptText: 'Nhập thông tin khách hàng (đã cập nhật)',
          inputType: 'text',
          required: true,
          defaultValue: 'Giá trị mặc định'
        }
      },
      google_sheet: {
        summary: 'Cấu hình cho bước loại google_sheet',
        value: {
          sheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          range: 'Sheet1!A1:C20',
          action: 'write',
          data: [
            { name: 'Nguyễn Văn A', email: '<EMAIL>', phone: '0123456789' },
            { name: 'Trần Thị B', email: '<EMAIL>', phone: '0987654321' }
          ],
          credentialId: '123e4567-e89b-12d3-a456-426614174000'
        }
      },
      google_doc: {
        summary: 'Cấu hình cho bước loại google_doc',
        value: {
          docId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          action: 'replace',
          content: 'Nội dung mới thay thế toàn bộ tài liệu',
          credentialId: '123e4567-e89b-12d3-a456-426614174000'
        }
      },
      google_calendar: {
        summary: 'Cấu hình cho bước loại google_calendar',
        value: {
          calendarId: 'primary',
          action: 'update_event',
          eventDetails: {
            eventId: 'abc123xyz456',
            summary: 'Cuộc họp với khách hàng (đã cập nhật)',
            description: 'Thảo luận về tiến độ dự án',
            start: { dateTime: '2023-06-02T10:00:00+07:00', timeZone: 'Asia/Ho_Chi_Minh' },
            end: { dateTime: '2023-06-02T11:30:00+07:00', timeZone: 'Asia/Ho_Chi_Minh' },
            attendees: [
              { email: '<EMAIL>' },
              { email: '<EMAIL>' }
            ]
          },
          credentialId: '123e4567-e89b-12d3-a456-426614174000'
        }
      },
      email: {
        summary: 'Cấu hình cho bước loại email',
        value: {
          to: ['<EMAIL>', '<EMAIL>'],
          subject: 'Thông báo cập nhật',
          body: 'Xin chào,\n\nĐây là email thông báo cập nhật từ hệ thống.\n\nTrân trọng,\nAdmin'
        }
      },
      facebook_page: {
        summary: 'Cấu hình cho bước loại facebook_page',
        value: {
          pageId: '123456789012345',
          action: 'comment',
          content: {
            commentId: '987654321098765',
            message: 'Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi!'
          },
          credentialId: '123e4567-e89b-12d3-a456-426614174000'
        }
      },
      gen_image: {
        summary: 'Cấu hình cho bước loại gen_image',
        value: {
          prompt: 'Một bức tranh phong cảnh về Hà Nội với Hồ Gươm và cầu Thê Húc',
          resolution: '1024x1024',
          model: 'dalle-3'
        }
      },
      gen_video: {
        summary: 'Cấu hình cho bước loại gen_video',
        value: {
          prompt: 'Một video ngắn về Đà Nẵng với cầu Rồng và biển Mỹ Khê',
          duration: 30,
          resolution: '1920x1080',
          model: 'gen-2'
        }
      },
      agent: {
        summary: 'Cấu hình cho bước loại agent',
        value: {
          agentId: '123e4567-e89b-12d3-a456-426614174000',
          parameters: {
            input: 'Dữ liệu đầu vào mới cho agent',
            options: {
              temperature: 0.5,
              maxTokens: 2000,
              topP: 0.9
            }
          }
        }
      }
    }
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình bước phải là đối tượng JSON' })
  stepConfig?: Record<string, any>;
}
