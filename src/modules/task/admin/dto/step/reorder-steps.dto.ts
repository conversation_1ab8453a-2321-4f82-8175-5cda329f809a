import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsUUID } from 'class-validator';

/**
 * DTO cho việc sắp xếp lại thứ tự các step
 */
export class ReorderStepsDto {
  /**
   * Danh sách ID của các step theo thứ tự mới
   */
  @ApiProperty({
    description: 'Danh sách ID của các step theo thứ tự mới',
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************'
    ],
    type: [String]
  })
  @IsArray({ message: 'Danh sách ID step phải là mảng' })
  @ArrayMinSize(1, { message: 'Danh sách ID step không được rỗng' })
  @IsUUID('all', { each: true, message: 'Mỗi ID step phải là UUID hợp lệ' })
  stepIds: string[];
}
