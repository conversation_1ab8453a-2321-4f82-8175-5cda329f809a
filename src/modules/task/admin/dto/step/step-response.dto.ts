import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { StepType } from '@modules/task/interfaces/step-config.interface';

/**
 * DTO cho việc trả về thông tin step
 */
@Exclude()
export class StepResponseDto {
  /**
   * ID của step
   */
  @Expose()
  @ApiProperty({
    description: 'ID của step',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  stepId: string;

  /**
   * ID của task
   */
  @Expose()
  @ApiProperty({
    description: 'ID của task',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  taskId: string;

  /**
   * Thứ tự xuất hiện của bước trong nhiệm vụ
   */
  @Expose()
  @ApiProperty({
    description: 'Thứ tự xuất hiện của bước trong nhiệm vụ',
    example: 1
  })
  orderIndex: number;

  /**
   * Tên của bước
   */
  @Expose()
  @ApiProperty({
    description: 'Tên của bước',
    example: 'Bước phân tích dữ liệu'
  })
  stepName: string;

  /**
   * Mô tả chi tiết của bước
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết của bước',
    example: 'Bước này sẽ phân tích dữ liệu từ các nguồn khác nhau'
  })
  stepDescription?: string;

  /**
   * Loại bước (prompt, google_sheet, google_doc, google_calendar, email, facebook_page, gen_image, gen_video, agent)
   */
  @Expose()
  @ApiProperty({
    description: 'Loại bước',
    enum: ['prompt', 'google_sheet', 'google_doc', 'google_calendar', 'email', 'facebook_page', 'gen_image', 'gen_video', 'agent'],
    example: 'prompt'
  })
  stepType: StepType;

  /**
   * Cấu hình chi tiết của bước ở dạng JSON
   */
  @Expose()
  @ApiProperty({
    description: 'Cấu hình chi tiết của bước ở dạng JSON',
    example: {
      content: 'Nội dung prompt',
      variables: ['variable1', 'variable2'],
      options: {
        temperature: 0.7
      }
    }
  })
  stepConfig: Record<string, any>;

  /**
   * Thời điểm tạo bước (Unix epoch)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm tạo bước (Unix epoch)',
    example: 1625097600000
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật bước (Unix epoch)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm cập nhật bước (Unix epoch)',
    example: 1625097600000
  })
  updatedAt: number;
}
