import { ApiBody, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';
import { StepType } from '@modules/task/interfaces/step-config.interface';

/**
 * DTO cho việc truy vấn danh sách step
 */
export class QueryStepDto extends QueryDto {
  /**
   * Tìm kiếm theo tên step
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên step',
    example: 'phân tích'
  })
  @IsOptional()
  @IsString()
  override search?: string = undefined;

  /**
   * Lọc theo loại step
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại step',
    enum: ['prompt', 'google_sheet', 'google_doc', 'google_calendar', 'email', 'facebook_page', 'gen_image', 'gen_video', 'agent'],
    example: 'prompt'
  })
  @IsOptional()
  @IsEnum(['prompt', 'google_sheet', 'google_doc', 'google_calendar', 'email', 'facebook_page', 'gen_image', 'gen_video', 'agent'])
  stepType?: StepType;
}

/**
 * Decorator tùy chỉnh cho ApiBody của endpoint tạo mới bước
 */
export const ApiCreateStepBody = () => {
  return ApiBody({
    description: 'Dữ liệu tạo bước',
    examples: {
      prompt: {
        summary: 'Tạo bước loại prompt',
        value: {
          stepName: 'Bước nhập thông tin',
          stepDescription: 'Bước này yêu cầu người dùng nhập thông tin',
          stepType: 'prompt',
          stepConfig: {
            promptText: 'Nhập thông tin khách hàng',
            inputType: 'text',
            required: true
          }
        }
      },
      google_sheet: {
        summary: 'Tạo bước loại google_sheet',
        value: {
          stepName: 'Bước lấy dữ liệu từ Google Sheet',
          stepDescription: 'Bước này sẽ lấy dữ liệu từ Google Sheet',
          stepType: 'google_sheet',
          stepConfig: {
            sheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
            range: 'Sheet1!A1:B10',
            action: 'read',
            credentialId: '123e4567-e89b-12d3-a456-426614174000'
          }
        }
      },
      google_doc: {
        summary: 'Tạo bước loại google_doc',
        value: {
          stepName: 'Bước tạo tài liệu Google Doc',
          stepDescription: 'Bước này sẽ tạo hoặc cập nhật tài liệu Google Doc',
          stepType: 'google_doc',
          stepConfig: {
            docId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
            action: 'append',
            content: 'Nội dung cần thêm vào tài liệu',
            credentialId: '123e4567-e89b-12d3-a456-426614174000'
          }
        }
      },
      google_calendar: {
        summary: 'Tạo bước loại google_calendar',
        value: {
          stepName: 'Bước tạo sự kiện lịch',
          stepDescription: 'Bước này sẽ tạo sự kiện trên Google Calendar',
          stepType: 'google_calendar',
          stepConfig: {
            calendarId: 'primary',
            action: 'create_event',
            eventDetails: {
              summary: 'Cuộc họp với khách hàng',
              description: 'Thảo luận về dự án mới',
              start: { dateTime: '2023-06-01T09:00:00+07:00', timeZone: 'Asia/Ho_Chi_Minh' },
              end: { dateTime: '2023-06-01T10:00:00+07:00', timeZone: 'Asia/Ho_Chi_Minh' },
              attendees: [{ email: '<EMAIL>' }]
            },
            credentialId: '123e4567-e89b-12d3-a456-426614174000'
          }
        }
      },
      email: {
        summary: 'Tạo bước loại email',
        value: {
          stepName: 'Bước gửi email',
          stepDescription: 'Bước này sẽ gửi email thông báo',
          stepType: 'email',
          stepConfig: {
            to: ['<EMAIL>'],
            subject: 'Thông báo mới',
            body: 'Xin chào,\n\nĐây là email thông báo từ hệ thống.\n\nTrân trọng,\nAdmin'
          }
        }
      },
      facebook_page: {
        summary: 'Tạo bước loại facebook_page',
        value: {
          stepName: 'Bước đăng bài lên Facebook Page',
          stepDescription: 'Bước này sẽ đăng bài lên trang Facebook',
          stepType: 'facebook_page',
          stepConfig: {
            pageId: '123456789012345',
            action: 'post',
            content: {
              message: 'Thông báo mới từ công ty chúng tôi!',
              link: 'https://example.com/thong-bao'
            },
            credentialId: '123e4567-e89b-12d3-a456-426614174000'
          }
        }
      },
      gen_image: {
        summary: 'Tạo bước loại gen_image',
        value: {
          stepName: 'Bước tạo hình ảnh',
          stepDescription: 'Bước này sẽ tạo hình ảnh từ mô tả',
          stepType: 'gen_image',
          stepConfig: {
            prompt: 'Một bức tranh phong cảnh về Việt Nam với núi non hùng vĩ',
            resolution: '512x512',
            model: 'dalle-2'
          }
        }
      },
      gen_video: {
        summary: 'Tạo bước loại gen_video',
        value: {
          stepName: 'Bước tạo video',
          stepDescription: 'Bước này sẽ tạo video từ mô tả',
          stepType: 'gen_video',
          stepConfig: {
            prompt: 'Một video ngắn về thành phố Hồ Chí Minh với các toà nhà cao tầng',
            duration: 15,
            resolution: '1280x720',
            model: 'runway'
          }
        }
      },
      agent: {
        summary: 'Tạo bước loại agent',
        value: {
          stepName: 'Bước xử lý bằng agent',
          stepDescription: 'Bước này sẽ sử dụng agent để xử lý dữ liệu',
          stepType: 'agent',
          stepConfig: {
            agentId: '123e4567-e89b-12d3-a456-426614174000',
            parameters: {
              input: 'Dữ liệu đầu vào cho agent',
              options: {
                temperature: 0.7,
                maxTokens: 1000
              }
            }
          }
        }
      }
    }
  });
};
