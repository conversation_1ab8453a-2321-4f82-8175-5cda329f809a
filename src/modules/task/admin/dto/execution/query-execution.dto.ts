import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';
import { TaskExecutionStatus } from '../../../enums/task-execution-status.enum';

/**
 * DTO cho việc truy vấn danh sách execution
 */
export class QueryExecutionDto extends QueryDto {
  /**
   * Lọc theo trạng thái
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái',
    enum: TaskExecutionStatus,
    example: TaskExecutionStatus.SUCCESS
  })
  @IsOptional()
  @IsEnum(TaskExecutionStatus, { message: 'Trạng thái không hợp lệ' })
  status?: TaskExecutionStatus;

  /**
   * Lọc theo thời gian bắt đầu từ (Unix epoch)
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> theo thời gian bắt đầu từ (Unix epoch)',
    example: 1625097600000
  })
  @IsOptional()
  @Type(() => Number)
  startTimeFrom?: number;

  /**
   * Lọc theo thời gian bắt đầu đến (Unix epoch)
   */
  @ApiPropertyOptional({
    description: 'Lọc theo thời gian bắt đầu đến (Unix epoch)',
    example: 1625097600000
  })
  @IsOptional()
  @Type(() => Number)
  startTimeTo?: number;
}
