import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { TaskExecutionStatus } from '../../../enums/task-execution-status.enum';

/**
 * DTO cho việc trả về thông tin chi tiết execution
 */
@Exclude()
export class ExecutionDetailResponseDto {
  /**
   * ID của execution
   */
  @Expose()
  @ApiProperty({
    description: 'ID của execution',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  taskExecutionId: string;

  /**
   * ID của task
   */
  @Expose()
  @ApiProperty({
    description: 'ID của task',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  taskId: string;

  /**
   * Thời gian bắt đầu phiên thực thi (Unix epoch)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời gian bắt đầu phiên thực thi (Unix epoch)',
    example: 1625097600000
  })
  startTime: number;

  /**
   * Thời gian kết thúc phiên thực thi (Unix epoch)
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Thời gian kết thúc phiên thực thi (Unix epoch)',
    example: 1625097600000
  })
  endTime?: number;

  /**
   * Trạng thái tổng thể của phiên thực thi
   */
  @Expose()
  @ApiProperty({
    description: 'Trạng thái tổng thể của phiên thực thi',
    enum: TaskExecutionStatus,
    example: TaskExecutionStatus.SUCCESS
  })
  overallStatus: TaskExecutionStatus;

  /**
   * Chi tiết thực thi từng bước
   */
  @Expose()
  @ApiProperty({
    description: 'Chi tiết thực thi từng bước',
    example: [
      {
        stepId: '123e4567-e89b-12d3-a456-426614174000',
        stepName: 'Bước phân tích dữ liệu',
        stepType: 'PROMPT',
        status: 'SUCCESS',
        startTime: 1625097600000,
        endTime: 1625097600000,
        input: { key: 'value' },
        output: { result: 'output value' }
      }
    ]
  })
  executionDetails: Record<string, any>;

  /**
   * Thời điểm ghi log phiên thực thi (Unix epoch)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm ghi log phiên thực thi (Unix epoch)',
    example: 1625097600000
  })
  createdAt: number;
}
