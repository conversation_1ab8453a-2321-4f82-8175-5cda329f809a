import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { TaskExecutionStatus } from '../../../enums/task-execution-status.enum';

/**
 * DTO cho việc trả về thông tin execution
 */
@Exclude()
export class ExecutionResponseDto {
  /**
   * ID của execution
   */
  @Expose()
  @ApiProperty({
    description: 'ID của execution',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  taskExecutionId: string;

  /**
   * ID của task
   */
  @Expose()
  @ApiProperty({
    description: 'ID của task',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  taskId: string;

  /**
   * Thời gian bắt đầu phiên thực thi (Unix epoch)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời gian bắt đầu phiên thực thi (Unix epoch)',
    example: 1625097600000
  })
  startTime: number;

  /**
   * Thời gian kết thúc phiên thực thi (Unix epoch)
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Thời gian kết thúc phiên thực thi (Unix epoch)',
    example: 1625097600000
  })
  endTime?: number;

  /**
   * Trạng thái tổng thể của phiên thực thi
   */
  @Expose()
  @ApiProperty({
    description: 'Trạng thái tổng thể của phiên thực thi',
    enum: TaskExecutionStatus,
    example: TaskExecutionStatus.SUCCESS
  })
  overallStatus: TaskExecutionStatus;

  /**
   * Thời điểm ghi log phiên thực thi (Unix epoch)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm ghi log phiên thực thi (Unix epoch)',
    example: 1625097600000
  })
  createdAt: number;
}
