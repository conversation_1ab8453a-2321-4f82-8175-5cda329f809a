import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin nhân viên
 */
export class EmployeeInfoDto {
  /**
   * ID của nhân viên
   */
  @ApiProperty({
    description: 'ID của nhân viên',
    example: 1,
  })
  id: number;

  /**
   * Tên đầy đủ của nhân viên
   */
  @ApiProperty({
    description: 'Tên đầy đủ của nhân viên',
    example: 'Nguyễn Văn A',
  })
  fullName: string;

  /**
   * Email của nhân viên
   */
  @ApiProperty({
    description: 'Email của nhân viên',
    example: '<EMAIL>',
  })
  email: string;

  /**
   * Avatar của nhân viên
   */
  @ApiPropertyOptional({
    description: 'Avatar của nhân viên',
    example: 'https://cdn.redai.vn/employees/avatar-123.jpg',
  })
  avatar?: string | null;
}
