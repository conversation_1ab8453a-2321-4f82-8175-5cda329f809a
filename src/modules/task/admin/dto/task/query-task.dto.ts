import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho việc truy vấn danh sách task
 */
export class QueryTaskDto extends QueryDto {
  /**
   * Tìm kiếm theo tên task
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên task',
    example: 'phân tích'
  })
  @IsOptional()
  @IsString()
  override search?: string = undefined;

  /**
   * Lọc theo ID của agent
   */
  @ApiPropertyOptional({
    description: 'Lọc theo ID của agent',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID('all', { message: 'ID của agent phải là UUID hợp lệ' })
  agentId?: string;

  /**
   * Lọ<PERSON> theo trạng thái hoạt động
   */
  @ApiPropertyOptional({
    description: '<PERSON>ọc theo trạng thái hoạt động',
    example: true
  })
  @IsOptional()
  @IsBoolean({ message: 'Trạng thái hoạt động phải là boolean' })
  @Type(() => Boolean)
  active?: boolean;
}
