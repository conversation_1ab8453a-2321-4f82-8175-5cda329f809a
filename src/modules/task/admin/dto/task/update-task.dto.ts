import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

/**
 * DTO cho việc cập nhật task
 */
export class UpdateTaskDto {
  /**
   * Tên của nhiệm vụ
   */
  @ApiPropertyOptional({
    description: 'Tên của nhiệm vụ',
    example: 'Nhiệm vụ phân tích dữ liệu khách hàng (đã cập nhật)'
  })
  @IsOptional()
  @IsString({ message: 'Tên nhiệm vụ phải là chuỗi' })
  @MaxLength(255, { message: 'Tên nhiệm vụ không được vượt quá 255 ký tự' })
  taskName?: string;

  /**
   * <PERSON>ô tả chi tiết nhiệm vụ
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON> tả chi tiết nhiệm vụ',
    example: '<PERSON>hi<PERSON><PERSON> vụ này sẽ phân tích dữ liệu khách hàng để đưa ra các đề xuất marketing (đã cập nhật)'
  })
  @IsOptional()
  @IsString({ message: 'Mô tả nhiệm vụ phải là chuỗi' })
  taskDescription?: string;

  /**
   * ID của agent
   */
  @ApiPropertyOptional({
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID('all', { message: 'ID của agent phải là UUID hợp lệ' })
  agentId?: string;

  /**
   * Trạng thái hoạt động của nhiệm vụ
   */
  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động của nhiệm vụ',
    example: true
  })
  @IsOptional()
  @IsBoolean({ message: 'Trạng thái hoạt động phải là boolean' })
  active?: boolean;
}
