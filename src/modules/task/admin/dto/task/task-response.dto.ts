import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';
import { EmployeeInfoDto } from '../common/employee-info.dto';

/**
 * DTO cho việc trả về thông tin task
 */
@Exclude()
export class TaskResponseDto {
  /**
   * ID của task
   */
  @Expose()
  @ApiProperty({
    description: 'ID của task',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  taskId: string;

  /**
   * ID của agent
   */
  @Expose()
  @ApiProperty({
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  agentId: string;

  /**
   * Tên của nhiệm vụ
   */
  @Expose()
  @ApiProperty({
    description: 'Tên của nhiệm vụ',
    example: 'Nhiệm vụ phân tích dữ liệu khách hàng'
  })
  taskName: string;

  /**
   * <PERSON><PERSON> tả chi tiết nhiệm vụ
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết nhiệm vụ',
    example: 'Nhiệm vụ này sẽ phân tích dữ liệu khách hàng để đưa ra các đề xuất marketing'
  })
  taskDescription?: string;

  /**
   * Trạng thái hoạt động của nhiệm vụ
   */
  @Expose()
  @ApiProperty({
    description: 'Trạng thái hoạt động của nhiệm vụ',
    example: true
  })
  active: boolean;

  /**
   * ID của nhân viên tạo nhiệm vụ
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'ID của nhân viên tạo nhiệm vụ',
    example: 1
  })
  createdBy?: number;

  /**
   * Thông tin chi tiết của nhân viên tạo nhiệm vụ
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Thông tin chi tiết của nhân viên tạo nhiệm vụ',
    type: EmployeeInfoDto
  })
  createdByInfo?: EmployeeInfoDto;

  /**
   * ID của nhân viên cập nhật nhiệm vụ
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'ID của nhân viên cập nhật nhiệm vụ',
    example: 1
  })
  updatedBy?: number;

  /**
   * Thông tin chi tiết của nhân viên cập nhật nhiệm vụ
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Thông tin chi tiết của nhân viên cập nhật nhiệm vụ',
    type: EmployeeInfoDto
  })
  updatedByInfo?: EmployeeInfoDto;

  /**
   * Thời điểm tạo nhiệm vụ (Unix epoch)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm tạo nhiệm vụ (Unix epoch)',
    example: 1625097600000
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật nhiệm vụ (Unix epoch)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm cập nhật nhiệm vụ (Unix epoch)',
    example: 1625097600000
  })
  updatedAt: number;
}
