import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

/**
 * DTO cho việc tạo mới task
 */
export class CreateTaskDto {
  /**
   * Tên của nhiệm vụ
   */
  @ApiProperty({
    description: 'Tên của nhiệm vụ',
    example: 'Nhiệm vụ phân tích dữ liệu khách hàng'
  })
  @IsNotEmpty({ message: 'Tên nhiệm vụ là bắt buộc' })
  @IsString({ message: 'Tên nhiệm vụ phải là chuỗi' })
  @MaxLength(255, { message: 'Tên nhiệm vụ không được vượt quá 255 ký tự' })
  taskName: string;

  /**
   * <PERSON>ô tả chi tiết nhiệm vụ
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON> tả chi tiết nhiệm vụ',
    example: '<PERSON>hiệ<PERSON> vụ này sẽ phân tích dữ liệu khách hàng để đưa ra các đề xuất marketing'
  })
  @IsOptional()
  @IsString({ message: 'Mô tả nhiệm vụ phải là chuỗi' })
  taskDescription?: string;

  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsNotEmpty({ message: 'ID của agent là bắt buộc' })
  @IsUUID('all', { message: 'ID của agent phải là UUID hợp lệ' })
  agentId: string;

  /**
   * Trạng thái hoạt động của nhiệm vụ
   */
  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động của nhiệm vụ',
    default: true,
    required: false
  })
  @IsOptional()
  @IsBoolean({ message: 'Trạng thái hoạt động phải là boolean' })
  active?: boolean = true;
}
