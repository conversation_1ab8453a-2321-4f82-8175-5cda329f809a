import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

/**
 * DTO cho việc cập nhật connection
 */
export class UpdateConnectionDto {
  /**
   * ID của step nguồn
   */
  @ApiPropertyOptional({
    description: 'ID của step nguồn',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID('all', { message: 'ID của step nguồn phải là UUID hợp lệ' })
  fromStepId?: string;

  /**
   * ID của step đích
   */
  @ApiPropertyOptional({
    description: 'ID của step đích',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID('all', { message: 'ID của step đích phải là UUID hợp lệ' })
  toStepId?: string;

  /**
   * Tên trường trong output của step nguồn
   */
  @ApiPropertyOptional({
    description: 'Tên trường trong output của step nguồn',
    example: 'updated_result'
  })
  @IsOptional()
  @IsString({ message: 'Tên trường output phải là chuỗi' })
  @MaxLength(255, { message: 'Tên trường output không được vượt quá 255 ký tự' })
  outputField?: string;

  /**
   * Tên trường trong input của step đích
   */
  @ApiPropertyOptional({
    description: 'Tên trường trong input của step đích',
    example: 'updated_prompt_variable'
  })
  @IsOptional()
  @IsString({ message: 'Tên trường input phải là chuỗi' })
  @MaxLength(255, { message: 'Tên trường input không được vượt quá 255 ký tự' })
  inputField?: string;
}
