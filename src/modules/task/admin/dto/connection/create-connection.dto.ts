import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID, MaxLength } from 'class-validator';

/**
 * DTO cho việc tạo mới connection
 */
export class CreateConnectionDto {
  /**
   * ID của step nguồn
   */
  @ApiProperty({
    description: 'ID của step nguồn',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsNotEmpty({ message: 'ID của step nguồn là bắt buộc' })
  @IsUUID('all', { message: 'ID của step nguồn phải là UUID hợp lệ' })
  fromStepId: string;

  /**
   * ID của step đích
   */
  @ApiProperty({
    description: 'ID của step đích',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsNotEmpty({ message: 'ID của step đích là bắt buộ<PERSON>' })
  @IsUUID('all', { message: 'ID của step đích phải là UUID hợp lệ' })
  toStepId: string;

  /**
   * Tên trường trong output của step nguồn
   */
  @ApiProperty({
    description: 'Tên trường trong output của step nguồn',
    example: 'result'
  })
  @IsNotEmpty({ message: 'Tên trường output là bắt buộc' })
  @IsString({ message: 'Tên trường output phải là chuỗi' })
  @MaxLength(255, { message: 'Tên trường output không được vượt quá 255 ký tự' })
  outputField: string;

  /**
   * Tên trường trong input của step đích
   */
  @ApiProperty({
    description: 'Tên trường trong input của step đích',
    example: 'prompt_variable'
  })
  @IsNotEmpty({ message: 'Tên trường input là bắt buộc' })
  @IsString({ message: 'Tên trường input phải là chuỗi' })
  @MaxLength(255, { message: 'Tên trường input không được vượt quá 255 ký tự' })
  inputField: string;
}
