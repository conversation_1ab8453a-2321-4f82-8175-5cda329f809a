import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO cho việc trả về thông tin connection
 */
@Exclude()
export class ConnectionResponseDto {
  /**
   * ID của connection
   */
  @Expose()
  @ApiProperty({
    description: 'ID của connection',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  connectionId: string;

  /**
   * ID của task
   */
  @Expose()
  @ApiProperty({
    description: 'ID của task',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  taskId: string;

  /**
   * ID của step nguồn
   */
  @Expose()
  @ApiProperty({
    description: 'ID của step nguồn',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  fromStepId: string;

  /**
   * ID của step đích
   */
  @Expose()
  @ApiProperty({
    description: 'ID của step đích',
    example: '123e4567-e89b-12d3-a456-426614174001'
  })
  toStepId: string;

  /**
   * Tên trường trong output của step nguồn
   */
  @Expose()
  @ApiProperty({
    description: 'Tên trường trong output của step nguồn',
    example: 'result'
  })
  outputField: string;

  /**
   * Tên trường trong input của step đích
   */
  @Expose()
  @ApiProperty({
    description: 'Tên trường trong input của step đích',
    example: 'prompt_variable'
  })
  inputField: string;

  /**
   * Thời điểm tạo kết nối (Unix epoch)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm tạo kết nối (Unix epoch)',
    example: 1625097600000
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật kết nối (Unix epoch)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm cập nhật kết nối (Unix epoch)',
    example: 1625097600000
  })
  updatedAt: number;
}
