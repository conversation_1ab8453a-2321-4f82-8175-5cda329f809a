import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateConnectionDto } from '../../../dto';

describe('CreateConnectionDto', () => {
  it('should validate a valid DTO with all fields', async () => {
    // Arrange
    const dto = plainToInstance(CreateConnectionDto, {
      fromStepId: '123e4567-e89b-12d3-a456-************',
      toStepId: '123e4567-e89b-12d3-a456-************',
      outputField: 'result',
      inputField: 'prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when fromStepId is missing', async () => {
    // Arrange
    const dto = plainToInstance(CreateConnectionDto, {
      toStepId: '123e4567-e89b-12d3-a456-************',
      outputField: 'result',
      inputField: 'prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('fromStepId');
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail validation when toStepId is missing', async () => {
    // Arrange
    const dto = plainToInstance(CreateConnectionDto, {
      fromStepId: '123e4567-e89b-12d3-a456-************',
      outputField: 'result',
      inputField: 'prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('toStepId');
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail validation when outputField is missing', async () => {
    // Arrange
    const dto = plainToInstance(CreateConnectionDto, {
      fromStepId: '123e4567-e89b-12d3-a456-************',
      toStepId: '123e4567-e89b-12d3-a456-************',
      inputField: 'prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('outputField');
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail validation when inputField is missing', async () => {
    // Arrange
    const dto = plainToInstance(CreateConnectionDto, {
      fromStepId: '123e4567-e89b-12d3-a456-************',
      toStepId: '123e4567-e89b-12d3-a456-************',
      outputField: 'result'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('inputField');
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail validation when fromStepId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(CreateConnectionDto, {
      fromStepId: 'invalid-uuid',
      toStepId: '123e4567-e89b-12d3-a456-************',
      outputField: 'result',
      inputField: 'prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('fromStepId');
    expect(errors[0].constraints).toHaveProperty('isUuid');
  });

  it('should fail validation when toStepId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(CreateConnectionDto, {
      fromStepId: '123e4567-e89b-12d3-a456-************',
      toStepId: 'invalid-uuid',
      outputField: 'result',
      inputField: 'prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('toStepId');
    expect(errors[0].constraints).toHaveProperty('isUuid');
  });

  it('should fail validation when outputField exceeds max length', async () => {
    // Arrange
    const dto = plainToInstance(CreateConnectionDto, {
      fromStepId: '123e4567-e89b-12d3-a456-************',
      toStepId: '123e4567-e89b-12d3-a456-************',
      outputField: 'a'.repeat(256), // 256 characters, max is 255
      inputField: 'prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('outputField');
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });

  it('should fail validation when inputField exceeds max length', async () => {
    // Arrange
    const dto = plainToInstance(CreateConnectionDto, {
      fromStepId: '123e4567-e89b-12d3-a456-************',
      toStepId: '123e4567-e89b-12d3-a456-************',
      outputField: 'result',
      inputField: 'a'.repeat(256) // 256 characters, max is 255
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('inputField');
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });
});
