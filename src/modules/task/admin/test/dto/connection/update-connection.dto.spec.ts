import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateConnectionDto } from '../../../dto';

describe('UpdateConnectionDto', () => {
  it('should validate a valid DTO with all fields', async () => {
    // Arrange
    const dto = plainToInstance(UpdateConnectionDto, {
      fromStepId: '123e4567-e89b-12d3-a456-426614174000',
      toStepId: '123e4567-e89b-12d3-a456-426614174001',
      outputField: 'updated_result',
      inputField: 'updated_prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with partial fields', async () => {
    // Arrange
    const dto = plainToInstance(UpdateConnectionDto, {
      outputField: 'updated_result',
      inputField: 'updated_prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate an empty DTO (all fields optional)', async () => {
    // Arrange
    const dto = plainToInstance(UpdateConnectionDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when fromStepId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(UpdateConnectionDto, {
      fromStepId: 'invalid-uuid',
      outputField: 'updated_result',
      inputField: 'updated_prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('fromStepId');
    expect(errors[0].constraints).toHaveProperty('isUuid');
  });

  it('should fail validation when toStepId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(UpdateConnectionDto, {
      toStepId: 'invalid-uuid',
      outputField: 'updated_result',
      inputField: 'updated_prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('toStepId');
    expect(errors[0].constraints).toHaveProperty('isUuid');
  });

  it('should fail validation when outputField exceeds max length', async () => {
    // Arrange
    const dto = plainToInstance(UpdateConnectionDto, {
      outputField: 'a'.repeat(256), // 256 characters, max is 255
      inputField: 'updated_prompt_variable'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('outputField');
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });

  it('should fail validation when inputField exceeds max length', async () => {
    // Arrange
    const dto = plainToInstance(UpdateConnectionDto, {
      outputField: 'updated_result',
      inputField: 'a'.repeat(256) // 256 characters, max is 255
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('inputField');
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });
});
