import { plainToInstance } from 'class-transformer';
import { ConnectionResponseDto } from '../../../dto';

describe('ConnectionResponseDto', () => {
  it('should transform and expose only the defined properties', () => {
    // Arrange
    const rawData = {
      connectionId: '123e4567-e89b-12d3-a456-426614174020',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174010',
      toStepId: '123e4567-e89b-12d3-a456-426614174011',
      outputField: 'analysis_result',
      inputField: 'data',
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      extraProperty: 'This should be excluded'
    };

    // Act
    const dto = plainToInstance(ConnectionResponseDto, rawData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toHaveProperty('connectionId', '123e4567-e89b-12d3-a456-426614174020');
    expect(dto).toHaveProperty('taskId', '123e4567-e89b-12d3-a456-426614174000');
    expect(dto).toHaveProperty('fromStepId', '123e4567-e89b-12d3-a456-426614174010');
    expect(dto).toHaveProperty('toStepId', '123e4567-e89b-12d3-a456-426614174011');
    expect(dto).toHaveProperty('outputField', 'analysis_result');
    expect(dto).toHaveProperty('inputField', 'data');
    expect(dto).toHaveProperty('createdAt', 1625097600000);
    expect(dto).toHaveProperty('updatedAt', 1625097600000);
    expect(dto).not.toHaveProperty('extraProperty');
  });

  it('should handle missing optional properties', () => {
    // Arrange
    const rawData = {
      connectionId: '123e4567-e89b-12d3-a456-426614174020',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174010',
      toStepId: '123e4567-e89b-12d3-a456-426614174011',
      outputField: 'analysis_result',
      inputField: 'data',
      createdAt: 1625097600000
      // updatedAt is missing
    };

    // Act
    const dto = plainToInstance(ConnectionResponseDto, rawData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toHaveProperty('connectionId', '123e4567-e89b-12d3-a456-426614174020');
    expect(dto).toHaveProperty('taskId', '123e4567-e89b-12d3-a456-426614174000');
    expect(dto).toHaveProperty('fromStepId', '123e4567-e89b-12d3-a456-426614174010');
    expect(dto).toHaveProperty('toStepId', '123e4567-e89b-12d3-a456-426614174011');
    expect(dto).toHaveProperty('outputField', 'analysis_result');
    expect(dto).toHaveProperty('inputField', 'data');
    expect(dto).toHaveProperty('createdAt', 1625097600000);
    expect(dto.updatedAt).toBeUndefined();
  });
});
