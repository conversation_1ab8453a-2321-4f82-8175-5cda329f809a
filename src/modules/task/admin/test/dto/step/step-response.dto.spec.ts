import { plainToInstance } from 'class-transformer';
import { StepResponseDto } from '../../../dto';

describe('StepResponseDto', () => {
  it('phải chuyển đổi dữ liệu step loại prompt thành DTO hợp lệ với đầy đủ thông tin', () => {
    // Arrange
    const stepData = {
      stepId: '123e4567-e89b-12d3-a456-426614174010',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      orderIndex: 1,
      stepName: '<PERSON><PERSON><PERSON>c phân tích dữ liệu',
      stepDescription: '<PERSON><PERSON><PERSON>c này sẽ phân tích dữ liệu từ các nguồn khác nhau',
      stepType: 'prompt',
      stepConfig: {
        promptText: '<PERSON>ân tích dữ liệu sau đây và đưa ra các insights: {{data}}',
        inputType: 'text',
        required: true,
        variables: ['data'],
        options: {
          temperature: 0.7,
          maxTokens: 2000
        }
      },
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const stepDto = plainToInstance(StepResponseDto, stepData);

    // Assert
    expect(stepDto).toBeInstanceOf(StepResponseDto);
    expect(stepDto.stepId).toBe('123e4567-e89b-12d3-a456-426614174010');
    expect(stepDto.taskId).toBe('123e4567-e89b-12d3-a456-426614174000');
    expect(stepDto.orderIndex).toBe(1);
    expect(stepDto.stepName).toBe('Bước phân tích dữ liệu');
    expect(stepDto.stepDescription).toBe('Bước này sẽ phân tích dữ liệu từ các nguồn khác nhau');
    expect(stepDto.stepType).toBe('prompt');
    expect(stepDto.stepConfig).toEqual({
      promptText: 'Phân tích dữ liệu sau đây và đưa ra các insights: {{data}}',
      inputType: 'text',
      required: true,
      variables: ['data'],
      options: {
        temperature: 0.7,
        maxTokens: 2000
      }
    });
    expect(stepDto.createdAt).toBe(1625097600000);
    expect(stepDto.updatedAt).toBe(1625097600000);
  });

  it('phải chuyển đổi dữ liệu step loại google_sheet thành DTO hợp lệ với đầy đủ thông tin', () => {
    // Arrange
    const stepData = {
      stepId: '123e4567-e89b-12d3-a456-426614174011',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      orderIndex: 2,
      stepName: 'Bước xuất dữ liệu ra Google Sheet',
      stepDescription: 'Bước này sẽ xuất dữ liệu phân tích ra Google Sheet',
      stepType: 'google_sheet',
      stepConfig: {
        spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
        sheetName: 'Data Analysis',
        range: 'A1:F10',
        operation: 'write',
        data: '{{analysis_result}}'
      },
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const stepDto = plainToInstance(StepResponseDto, stepData);

    // Assert
    expect(stepDto).toBeInstanceOf(StepResponseDto);
    expect(stepDto.stepId).toBe('123e4567-e89b-12d3-a456-426614174011');
    expect(stepDto.stepType).toBe('google_sheet');
    expect(stepDto.stepConfig).toEqual({
      spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
      sheetName: 'Data Analysis',
      range: 'A1:F10',
      operation: 'write',
      data: '{{analysis_result}}'
    });
  });

  it('phải chuyển đổi dữ liệu step loại agent thành DTO hợp lệ với đầy đủ thông tin', () => {
    // Arrange
    const stepData = {
      stepId: '123e4567-e89b-12d3-a456-426614174012',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      orderIndex: 3,
      stepName: 'Bước sử dụng agent phân tích nâng cao',
      stepDescription: 'Bước này sẽ sử dụng agent để phân tích dữ liệu nâng cao',
      stepType: 'agent',
      stepConfig: {
        agentId: '123e4567-e89b-12d3-a456-426614174001',
        parameters: {
          input: '{{analysis_result}}',
          options: {
            temperature: 0.5,
            maxTokens: 2000,
            topP: 0.9
          }
        }
      },
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const stepDto = plainToInstance(StepResponseDto, stepData);

    // Assert
    expect(stepDto).toBeInstanceOf(StepResponseDto);
    expect(stepDto.stepId).toBe('123e4567-e89b-12d3-a456-426614174012');
    expect(stepDto.stepType).toBe('agent');
    expect(stepDto.stepConfig).toEqual({
      agentId: '123e4567-e89b-12d3-a456-426614174001',
      parameters: {
        input: '{{analysis_result}}',
        options: {
          temperature: 0.5,
          maxTokens: 2000,
          topP: 0.9
        }
      }
    });
  });

  it('phải chuyển đổi dữ liệu step thành DTO hợp lệ khi không có mô tả', () => {
    // Arrange
    const stepData = {
      stepId: '123e4567-e89b-12d3-a456-426614174010',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      orderIndex: 1,
      stepName: 'Bước phân tích dữ liệu',
      stepType: 'prompt',
      stepConfig: {
        promptText: 'Phân tích dữ liệu sau đây và đưa ra các insights: {{data}}',
        inputType: 'text',
        required: true,
        variables: ['data']
      },
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const stepDto = plainToInstance(StepResponseDto, stepData);

    // Assert
    expect(stepDto).toBeInstanceOf(StepResponseDto);
    expect(stepDto.stepId).toBe('123e4567-e89b-12d3-a456-426614174010');
    expect(stepDto.stepDescription).toBeUndefined();
  });
});
