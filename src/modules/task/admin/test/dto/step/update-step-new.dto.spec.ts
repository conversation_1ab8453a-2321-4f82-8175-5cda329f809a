import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';

// Mock StepType enum
enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image',
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

// Mock UpdateStepDto for testing
class UpdateStepDto {
  stepName?: string;
  stepDescription?: string;
  stepType?: StepType;
  stepConfig?: any;
}

// Mock validate function
jest.mock('class-validator', () => ({
  validate: jest.fn(),
}));

describe('UpdateStepDto', () => {
  beforeEach(() => {
    // Reset mock before each test
    (validate as jest.Mock).mockReset();
  });

  it('should validate a valid DTO with all fields', async () => {
    // Arrange
    const dto = plainToInstance(UpdateStepDto, {
      stepName: 'Updated Step',
      stepDescription: 'This is an updated step',
      stepType: StepType.PROMPT,
      stepConfig: {
        promptText: 'This is an updated prompt text',
        inputType: 'text',
        required: true
      }
    });

    // Mock validation result - no errors
    (validate as jest.Mock).mockResolvedValue([]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with partial fields', async () => {
    // Arrange
    const dto = plainToInstance(UpdateStepDto, {
      stepName: 'Updated Step Name Only',
    });

    // Mock validation result - no errors
    (validate as jest.Mock).mockResolvedValue([]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate an empty DTO', async () => {
    // Arrange
    const dto = plainToInstance(UpdateStepDto, {});

    // Mock validation result - no errors
    (validate as jest.Mock).mockResolvedValue([]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when stepName exceeds maximum length', async () => {
    // Arrange
    const dto = plainToInstance(UpdateStepDto, {
      stepName: 'A'.repeat(256), // Exceeds 255 character limit
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          maxLength: 'Tên bước không được vượt quá 255 ký tự'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });

  it('should fail validation when stepType is invalid', async () => {
    // Arrange
    const dto = plainToInstance(UpdateStepDto, {
      stepType: 'invalid-type',
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isEnum: 'Loại bước phải là một trong các giá trị: prompt, google_sheet, google_doc, google_calendar, email, facebook_page, gen_image, gen_video, agent'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('should fail validation when stepConfig is not an object', async () => {
    // Arrange
    const dto = plainToInstance(UpdateStepDto, {
      stepConfig: 'not-an-object'
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isObject: 'Cấu hình bước phải là đối tượng'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isObject');
  });
});
