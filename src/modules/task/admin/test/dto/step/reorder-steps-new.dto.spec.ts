import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';

// Mock ReorderStepsDto for testing
class ReorderStepsDto {
  stepIds: string[];
}

// Mock validate function
jest.mock('class-validator', () => ({
  validate: jest.fn(),
}));

describe('ReorderStepsDto', () => {
  beforeEach(() => {
    // Reset mock before each test
    (validate as jest.Mock).mockReset();
  });

  it('should validate a valid DTO with step IDs', async () => {
    // Arrange
    const dto = plainToInstance(ReorderStepsDto, {
      stepIds: [
        '123e4567-e89b-12d3-a456-************',
        '123e4567-e89b-12d3-a456-426614174001',
        '123e4567-e89b-12d3-a456-************'
      ]
    });

    // Mock validation result - no errors
    (validate as jest.Mock).mockResolvedValue([]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when stepIds is missing', async () => {
    // Arrange
    const dto = plainToInstance(ReorderStepsDto, {});

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isArray: 'Danh sách ID là bắt buộc và phải là một mảng'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isArray');
  });

  it('should fail validation when stepIds is an empty array', async () => {
    // Arrange
    const dto = plainToInstance(ReorderStepsDto, {
      stepIds: []
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          arrayMinSize: 'Danh sách ID phải có ít nhất 1 phần tử'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('arrayMinSize');
  });

  it('should fail validation when stepIds contains invalid UUIDs', async () => {
    // Arrange
    const dto = plainToInstance(ReorderStepsDto, {
      stepIds: [
        '123e4567-e89b-12d3-a456-************',
        'not-a-uuid',
        '123e4567-e89b-12d3-a456-************'
      ]
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isUuid: 'Mỗi ID trong danh sách phải là UUID hợp lệ'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isUuid');
  });
});
