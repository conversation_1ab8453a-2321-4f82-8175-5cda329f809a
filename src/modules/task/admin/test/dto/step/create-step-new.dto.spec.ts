import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';

// Mock StepType enum
enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image',
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

// Mock CreateStepDto for testing
class CreateStepDto {
  stepName: string;
  stepDescription?: string;
  stepType: StepType;
  stepConfig: any;
}

// Mock validate function
jest.mock('class-validator', () => ({
  validate: jest.fn(),
}));

describe('CreateStepDto', () => {
  beforeEach(() => {
    // Reset mock before each test
    (validate as jest.Mock).mockReset();
  });

  it('should validate a valid DTO with prompt type', async () => {
    // Arrange
    const dto = plainToInstance(CreateStepDto, {
      stepName: 'Test Step',
      stepDescription: 'This is a test step',
      stepType: StepType.PROMPT,
      stepConfig: {
        promptText: 'This is a prompt text',
        inputType: 'text',
        required: true
      }
    });

    // Mock validation result - no errors
    (validate as jest.Mock).mockResolvedValue([]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with google_sheet type', async () => {
    // Arrange
    const dto = plainToInstance(CreateStepDto, {
      stepName: 'Google Sheet Step',
      stepType: StepType.GOOGLE_SHEET,
      stepConfig: {
        sheetId: '1234567890',
        range: 'A1:D10'
      }
    });

    // Mock validation result - no errors
    (validate as jest.Mock).mockResolvedValue([]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when stepName is missing', async () => {
    // Arrange
    const dto = plainToInstance(CreateStepDto, {
      stepType: StepType.PROMPT,
      stepConfig: {
        promptText: 'This is a prompt text'
      }
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isNotEmpty: 'Tên bước là bắt buộc'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail validation when stepName exceeds maximum length', async () => {
    // Arrange
    const dto = plainToInstance(CreateStepDto, {
      stepName: 'A'.repeat(256), // Exceeds 255 character limit
      stepType: StepType.PROMPT,
      stepConfig: {
        promptText: 'This is a prompt text'
      }
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          maxLength: 'Tên bước không được vượt quá 255 ký tự'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });

  it('should fail validation when stepType is missing', async () => {
    // Arrange
    const dto = plainToInstance(CreateStepDto, {
      stepName: 'Test Step',
      stepConfig: {
        promptText: 'This is a prompt text'
      }
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isNotEmpty: 'Loại bước là bắt buộc'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail validation when stepType is invalid', async () => {
    // Arrange
    const dto = plainToInstance(CreateStepDto, {
      stepName: 'Test Step',
      stepType: 'invalid-type',
      stepConfig: {
        promptText: 'This is a prompt text'
      }
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isEnum: 'Loại bước phải là một trong các giá trị: prompt, google_sheet, google_doc, google_calendar, email, facebook_page, gen_image, gen_video, agent'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('should fail validation when stepConfig is missing', async () => {
    // Arrange
    const dto = plainToInstance(CreateStepDto, {
      stepName: 'Test Step',
      stepType: StepType.PROMPT
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isObject: 'Cấu hình bước là bắt buộc và phải là đối tượng'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isObject');
  });

  it('should fail validation when stepConfig is not an object', async () => {
    // Arrange
    const dto = plainToInstance(CreateStepDto, {
      stepName: 'Test Step',
      stepType: StepType.PROMPT,
      stepConfig: 'not-an-object'
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isObject: 'Cấu hình bước phải là đối tượng'
        }
      }
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isObject');
  });

  it('should validate with complex stepConfig object', async () => {
    // Arrange
    const dto = plainToInstance(CreateStepDto, {
      stepName: 'Complex Step',
      stepType: StepType.AGENT,
      stepConfig: {
        agentId: '123e4567-e89b-12d3-a456-426614174000',
        inputs: [
          { name: 'input1', type: 'text', required: true },
          { name: 'input2', type: 'number', required: false }
        ],
        outputs: [
          { name: 'output1', type: 'text' }
        ]
      }
    });

    // Mock validation result - no errors
    (validate as jest.Mock).mockResolvedValue([]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });
});
