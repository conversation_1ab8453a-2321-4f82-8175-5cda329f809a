import { plainToInstance } from 'class-transformer';
import { ExecutionDetailResponseDto } from '../../../dto';
import { TaskExecutionStatus } from '../../../../enums';

describe('ExecutionDetailResponseDto', () => {
  it('should transform and expose only the defined properties', () => {
    // Arrange
    const rawData = {
      taskExecutionId: '123e4567-e89b-12d3-a456-426614174030',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      startTime: 1625097600000,
      endTime: 1625097900000,
      overallStatus: TaskExecutionStatus.SUCCESS,
      executionDetails: [
        {
          stepId: '123e4567-e89b-12d3-a456-426614174010',
          stepName: 'Bước phân tích dữ liệu',
          stepType: 'PROMPT',
          status: 'SUCCESS',
          startTime: 1625097600000,
          endTime: 1625097700000,
          input: { data: 'Raw data for analysis' },
          output: { analysis_result: 'Analyzed data with insights' }
        },
        {
          stepId: '123e4567-e89b-12d3-a456-426614174011',
          stepName: 'Bước tạo báo cáo',
          stepType: 'PROMPT',
          status: 'SUCCESS',
          startTime: 1625097700000,
          endTime: 1625097900000,
          input: { analysis_result: 'Analyzed data with insights' },
          output: { report: 'Final report with recommendations' }
        }
      ],
      createdAt: 1625097600000,
      extraProperty: 'This should be excluded'
    };

    // Act
    const dto = plainToInstance(ExecutionDetailResponseDto, rawData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toHaveProperty('taskExecutionId', '123e4567-e89b-12d3-a456-426614174030');
    expect(dto).toHaveProperty('taskId', '123e4567-e89b-12d3-a456-426614174000');
    expect(dto).toHaveProperty('startTime', 1625097600000);
    expect(dto).toHaveProperty('endTime', 1625097900000);
    expect(dto).toHaveProperty('overallStatus', TaskExecutionStatus.SUCCESS);
    expect(dto).toHaveProperty('executionDetails');
    expect(dto.executionDetails).toHaveLength(2);
    expect(dto).toHaveProperty('createdAt', 1625097600000);
    expect(dto).not.toHaveProperty('extraProperty');
  });

  it('should handle missing optional properties', () => {
    // Arrange
    const rawData = {
      taskExecutionId: '123e4567-e89b-12d3-a456-426614174030',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      startTime: 1625097600000,
      // endTime is missing (optional)
      overallStatus: TaskExecutionStatus.RUNNING,
      executionDetails: [
        {
          stepId: '123e4567-e89b-12d3-a456-426614174010',
          stepName: 'Bước phân tích dữ liệu',
          stepType: 'PROMPT',
          status: 'SUCCESS',
          startTime: 1625097600000,
          endTime: 1625097700000,
          input: { data: 'Raw data for analysis' },
          output: { analysis_result: 'Analyzed data with insights' }
        },
        {
          stepId: '123e4567-e89b-12d3-a456-426614174011',
          stepName: 'Bước tạo báo cáo',
          stepType: 'PROMPT',
          status: 'RUNNING',
          startTime: 1625097700000,
          // endTime is missing for running step
          input: { analysis_result: 'Analyzed data with insights' },
          // output is missing for running step
        }
      ],
      createdAt: 1625097600000
    };

    // Act
    const dto = plainToInstance(ExecutionDetailResponseDto, rawData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toHaveProperty('taskExecutionId', '123e4567-e89b-12d3-a456-426614174030');
    expect(dto).toHaveProperty('taskId', '123e4567-e89b-12d3-a456-426614174000');
    expect(dto).toHaveProperty('startTime', 1625097600000);
    expect(dto.endTime).toBeUndefined();
    expect(dto).toHaveProperty('overallStatus', TaskExecutionStatus.RUNNING);
    expect(dto).toHaveProperty('executionDetails');
    expect(dto.executionDetails).toHaveLength(2);
    expect(dto).toHaveProperty('createdAt', 1625097600000);
  });

  it('should handle execution details with error information', () => {
    // Arrange
    const rawData = {
      taskExecutionId: '123e4567-e89b-12d3-a456-426614174030',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      startTime: 1625097600000,
      endTime: 1625097800000,
      overallStatus: TaskExecutionStatus.FAILED,
      executionDetails: [
        {
          stepId: '123e4567-e89b-12d3-a456-426614174010',
          stepName: 'Bước phân tích dữ liệu',
          stepType: 'PROMPT',
          status: 'SUCCESS',
          startTime: 1625097600000,
          endTime: 1625097700000,
          input: { data: 'Raw data for analysis' },
          output: { analysis_result: 'Analyzed data with insights' }
        },
        {
          stepId: '123e4567-e89b-12d3-a456-426614174011',
          stepName: 'Bước tạo báo cáo',
          stepType: 'PROMPT',
          status: 'FAILED',
          startTime: 1625097700000,
          endTime: 1625097800000,
          input: { analysis_result: 'Analyzed data with insights' },
          error: {
            code: 'PROMPT_ERROR',
            message: 'Failed to generate report'
          }
        }
      ],
      createdAt: 1625097600000
    };

    // Act
    const dto = plainToInstance(ExecutionDetailResponseDto, rawData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toHaveProperty('overallStatus', TaskExecutionStatus.FAILED);
    expect(dto.executionDetails).toHaveLength(2);
    expect(dto.executionDetails[1]).toHaveProperty('status', 'FAILED');
    expect(dto.executionDetails[1]).toHaveProperty('error');
    expect(dto.executionDetails[1].error).toHaveProperty('code', 'PROMPT_ERROR');
    expect(dto.executionDetails[1].error).toHaveProperty('message', 'Failed to generate report');
  });
});
