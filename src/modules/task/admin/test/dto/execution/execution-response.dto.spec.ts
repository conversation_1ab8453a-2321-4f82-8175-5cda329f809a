import { plainToInstance } from 'class-transformer';
import { ExecutionResponseDto } from '../../../dto';
import { TaskExecutionStatus } from '../../../../enums';

describe('ExecutionResponseDto', () => {
  it('should transform and expose only the defined properties', () => {
    // Arrange
    const rawData = {
      taskExecutionId: '123e4567-e89b-12d3-a456-426614174030',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      startTime: 1625097600000,
      endTime: 1625097900000,
      overallStatus: TaskExecutionStatus.SUCCESS,
      createdAt: 1625097600000,
      extraProperty: 'This should be excluded'
    };

    // Act
    const dto = plainToInstance(ExecutionResponseDto, rawData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toHaveProperty('taskExecutionId', '123e4567-e89b-12d3-a456-426614174030');
    expect(dto).toHaveProperty('taskId', '123e4567-e89b-12d3-a456-426614174000');
    expect(dto).toHaveProperty('startTime', 1625097600000);
    expect(dto).toHaveProperty('endTime', 1625097900000);
    expect(dto).toHaveProperty('overallStatus', TaskExecutionStatus.SUCCESS);
    expect(dto).toHaveProperty('createdAt', 1625097600000);
    expect(dto).not.toHaveProperty('extraProperty');
  });

  it('should handle missing optional properties', () => {
    // Arrange
    const rawData = {
      taskExecutionId: '123e4567-e89b-12d3-a456-426614174030',
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      startTime: 1625097600000,
      // endTime is missing (optional)
      overallStatus: TaskExecutionStatus.RUNNING,
      createdAt: 1625097600000
    };

    // Act
    const dto = plainToInstance(ExecutionResponseDto, rawData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toHaveProperty('taskExecutionId', '123e4567-e89b-12d3-a456-426614174030');
    expect(dto).toHaveProperty('taskId', '123e4567-e89b-12d3-a456-426614174000');
    expect(dto).toHaveProperty('startTime', 1625097600000);
    expect(dto.endTime).toBeUndefined();
    expect(dto).toHaveProperty('overallStatus', TaskExecutionStatus.RUNNING);
    expect(dto).toHaveProperty('createdAt', 1625097600000);
  });

  it('should handle all execution status types', () => {
    // Test each status type
    for (const status of Object.values(TaskExecutionStatus)) {
      // Arrange
      const rawData = {
        taskExecutionId: '123e4567-e89b-12d3-a456-426614174030',
        taskId: '123e4567-e89b-12d3-a456-426614174000',
        startTime: 1625097600000,
        endTime: status === TaskExecutionStatus.RUNNING ? undefined : 1625097900000,
        overallStatus: status,
        createdAt: 1625097600000
      };

      // Act
      const dto = plainToInstance(ExecutionResponseDto, rawData, { excludeExtraneousValues: true });

      // Assert
      expect(dto).toHaveProperty('overallStatus', status);
    }
  });
});
