import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';

// Mock CreateTaskDto for testing
class CreateTaskDto {
  taskName: string;
  agentId: string;
  taskDescription?: string;
  active?: boolean = true;
}

// Mock validate function
jest.mock('class-validator', () => ({
  validate: jest.fn(),
}));

describe('CreateTaskDto', () => {
  beforeEach(() => {
    // Reset mock before each test
    (validate as jest.Mock).mockReset();
  });

  it('should validate a valid DTO with all fields', async () => {
    // Arrange
    const dto = plainToInstance(CreateTaskDto, {
      taskName: 'Test Task',
      agentId: '123e4567-e89b-12d3-a456-426614174000',
      taskDescription: 'This is a test task',
      active: true,
    });

    // Mock validation result - no errors
    (validate as jest.Mock).mockResolvedValue([]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with minimal required fields', async () => {
    // Arrange
    const dto = plainToInstance(CreateTaskDto, {
      taskName: 'Test Task',
      agentId: '123e4567-e89b-12d3-a456-426614174000',
    });

    // Mock validation result - no errors
    (validate as jest.Mock).mockResolvedValue([]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.active).toBe(true); // Default value
  });

  it('should fail validation when taskName is missing', async () => {
    // Arrange
    const dto = plainToInstance(CreateTaskDto, {
      agentId: '123e4567-e89b-12d3-a456-426614174000',
      active: true,
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isNotEmpty: 'Tên nhiệm vụ là bắt buộc',
        },
      },
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail validation when taskName exceeds maximum length', async () => {
    // Arrange
    const dto = plainToInstance(CreateTaskDto, {
      taskName: 'A'.repeat(256), // Exceeds 255 character limit
      agentId: '123e4567-e89b-12d3-a456-426614174000',
      active: true,
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          maxLength: 'Tên nhiệm vụ không được vượt quá 255 ký tự',
        },
      },
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });

  it('should fail validation when agentId is missing', async () => {
    // Arrange
    const dto = plainToInstance(CreateTaskDto, {
      taskName: 'Test Task',
      active: true,
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isNotEmpty: 'ID của agent là bắt buộc',
        },
      },
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail validation when agentId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(CreateTaskDto, {
      taskName: 'Test Task',
      agentId: 'not-a-uuid',
      active: true,
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isUuid: 'ID của agent phải là UUID hợp lệ',
        },
      },
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isUuid');
  });

  it('should fail validation when active is not a boolean', async () => {
    // Arrange
    const dto = plainToInstance(CreateTaskDto, {
      taskName: 'Test Task',
      agentId: '123e4567-e89b-12d3-a456-************',
      active: 'not-a-boolean', // Not a boolean
    });

    // Mock validation result - with errors
    (validate as jest.Mock).mockResolvedValue([
      {
        constraints: {
          isBoolean: 'Trạng thái active phải là boolean',
        },
      },
    ]);

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isBoolean');
  });
});
