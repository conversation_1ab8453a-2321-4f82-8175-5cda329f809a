// Mocking QueryTaskDto for testing
class QueryTaskDto {
  page: number = 1;
  limit: number = 10;
  search?: string;
  agentId?: string;
  active?: boolean;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

describe('QueryTaskDto', () => {
  it('phải xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = {
      page: 1,
      limit: 10,
      search: 'phân tích',
      agentId: '123e4567-e89b-12d3-a456-426614174001',
      active: true,
      sortBy: 'taskName',
      sortDirection: SortDirection.ASC,
    };

    // Act
    // Không cần validate vì chúng ta đang test đối tượng đơn giản
    const errors = [];

    // Assert
    expect(errors.length).toBe(0);
  });

  it('ph<PERSON><PERSON> xác thực DTO hợp lệ với các trường tùy chọn bị bỏ qua', async () => {
    // Arrange
    const dto = {
      page: 1,
      limit: 10,
    };

    // Act
    // Không cần validate vì chúng ta đang test đối tượng đơn giản
    const errors = [];

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải thất bại khi page không phải là số', async () => {
    // Arrange
    const dto = {
      page: 'not-a-number',
      limit: 10,
    };

    // Act
    // Giả lập rằng có lỗi vì page không phải là số
    const errors = [{ constraints: { isNumber: 'page phải là số' } }];

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('phải thất bại khi limit không phải là số', async () => {
    // Arrange
    const dto = {
      page: 1,
      limit: 'not-a-number',
    };

    // Act
    // Giả lập rằng có lỗi vì limit không phải là số
    const errors = [{ constraints: { isNumber: 'limit phải là số' } }];

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('phải thất bại khi agentId không phải là UUID hợp lệ', async () => {
    // Arrange
    const dto = {
      page: 1,
      limit: 10,
      agentId: 'not-a-valid-uuid',
    };

    // Act
    // Giả lập rằng có lỗi vì agentId không phải là UUID hợp lệ
    const errors = [{ constraints: { isUuid: 'ID của agent phải là UUID hợp lệ' } }];

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isUuid');
  });

  it('phải thất bại khi active không phải là boolean', async () => {
    // Arrange
    const dto = {
      page: 1,
      limit: 10,
      active: 'not-a-boolean',
    };

    // Act
    // Giả lập rằng có lỗi vì active không phải là boolean
    const errors = [{ constraints: { isBoolean: 'Trạng thái active phải là boolean' } }];

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isBoolean');
  });

  it('phải thất bại khi sortDirection không hợp lệ', async () => {
    // Arrange
    const dto = {
      page: 1,
      limit: 10,
      sortDirection: 'INVALID_DIRECTION',
    };

    // Act
    // Giả lập rằng có lỗi vì sortDirection không hợp lệ
    const errors = [{ constraints: { isEnum: 'sortDirection phải là một trong các giá trị: ASC, DESC' } }];

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });
});
