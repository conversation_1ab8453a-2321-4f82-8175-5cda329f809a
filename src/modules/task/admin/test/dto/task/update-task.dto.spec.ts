import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateTaskDto } from '../../__mocks__/dto/update-task.dto';

describe('UpdateTaskDto', () => {
  it('phải xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(UpdateTaskDto, {
      taskName: 'Nhiệm vụ phân tích dữ liệu (đã cập nhật)',
      taskDescription: 'Nhiệm vụ này sẽ phân tích dữ liệu từ nhiều nguồn khác nhau (đã cập nhật)',
      agentId: '123e4567-e89b-12d3-a456-426614174001',
      active: false
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('ph<PERSON><PERSON> xác thực DTO hợp lệ khi chỉ cập nhật một số trường', async () => {
    // Arrange
    const dto = plainToInstance(UpdateTaskDto, {
      taskName: 'Nhiệm vụ phân tích dữ liệu (đã cập nhật)',
      active: false
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải xác thực DTO hợp lệ khi không có trường nào được cập nhật', async () => {
    // Arrange
    const dto = plainToInstance(UpdateTaskDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải thất bại khi tên nhiệm vụ quá dài', async () => {
    // Arrange
    const longName = 'A'.repeat(256); // Dài hơn 255 ký tự
    const dto = plainToInstance(UpdateTaskDto, {
      taskName: longName
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });

  it('phải thất bại khi ID của agent không phải là UUID hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(UpdateTaskDto, {
      agentId: 'not-a-valid-uuid'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isUuid');
  });

  it('phải thất bại khi active không phải là boolean', async () => {
    // Arrange
    const dto = plainToInstance(UpdateTaskDto, {
      active: 'not-a-boolean' // Không phải boolean
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isBoolean');
  });
});
