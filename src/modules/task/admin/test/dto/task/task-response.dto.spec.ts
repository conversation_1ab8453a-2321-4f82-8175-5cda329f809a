import { plainToInstance } from 'class-transformer';
import { TaskResponseDto } from '@modules/task/admin/dto';
import { EmployeeInfoDto } from '../../__mocks__/@modules/employee/dto/employee-info.dto';

describe('TaskResponseDto', () => {
  it('phải chuyển đổi dữ liệu task thành DTO hợp lệ với đầy đủ thông tin', () => {
    // Arrange
    const employeeInfo: EmployeeInfoDto = {
      id: 1,
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
      avatar: 'https://cdn.redai.vn/employees/avatar.jpg',
    };

    const taskData = {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      agentId: '123e4567-e89b-12d3-a456-426614174001',
      taskName: '<PERSON>hi<PERSON><PERSON> vụ phân tích dữ liệu',
      taskDescription: '<PERSON><PERSON><PERSON><PERSON> vụ này sẽ phân tích dữ liệu từ nhiều nguồn khác nhau',
      active: true,
      createdBy: 1,
      createdByInfo: employeeInfo,
      updatedBy: 1,
      updatedByInfo: employeeInfo,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const taskDto = plainToInstance(TaskResponseDto, taskData);

    // Assert
    expect(taskDto).toBeInstanceOf(TaskResponseDto);
    expect(taskDto.taskId).toBe('123e4567-e89b-12d3-a456-426614174000');
    expect(taskDto.agentId).toBe('123e4567-e89b-12d3-a456-426614174001');
    expect(taskDto.taskName).toBe('Nhiệm vụ phân tích dữ liệu');
    expect(taskDto.taskDescription).toBe('Nhiệm vụ này sẽ phân tích dữ liệu từ nhiều nguồn khác nhau');
    expect(taskDto.active).toBe(true);
    expect(taskDto.createdBy).toBe(1);
    expect(taskDto.createdByInfo).toEqual(employeeInfo);
    expect(taskDto.updatedBy).toBe(1);
    expect(taskDto.updatedByInfo).toEqual(employeeInfo);
    expect(taskDto.createdAt).toBe(1625097600000);
    expect(taskDto.updatedAt).toBe(1625097600000);
  });

  it('phải chuyển đổi dữ liệu task thành DTO hợp lệ khi không có mô tả', () => {
    // Arrange
    const employeeInfo: EmployeeInfoDto = {
      id: 1,
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
      avatar: 'https://cdn.redai.vn/employees/avatar.jpg',
    };

    const taskData = {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      agentId: '123e4567-e89b-12d3-a456-426614174001',
      taskName: 'Nhiệm vụ phân tích dữ liệu',
      active: true,
      createdBy: 1,
      createdByInfo: employeeInfo,
      updatedBy: 1,
      updatedByInfo: employeeInfo,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const taskDto = plainToInstance(TaskResponseDto, taskData);

    // Assert
    expect(taskDto).toBeInstanceOf(TaskResponseDto);
    expect(taskDto.taskId).toBe('123e4567-e89b-12d3-a456-426614174000');
    expect(taskDto.taskDescription).toBeUndefined();
  });

  it('phải chuyển đổi dữ liệu task thành DTO hợp lệ khi không có thông tin người tạo/cập nhật', () => {
    // Arrange
    const taskData = {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      agentId: '123e4567-e89b-12d3-a456-426614174001',
      taskName: 'Nhiệm vụ phân tích dữ liệu',
      taskDescription: 'Nhiệm vụ này sẽ phân tích dữ liệu từ nhiều nguồn khác nhau',
      active: true,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const taskDto = plainToInstance(TaskResponseDto, taskData);

    // Assert
    expect(taskDto).toBeInstanceOf(TaskResponseDto);
    expect(taskDto.taskId).toBe('123e4567-e89b-12d3-a456-426614174000');
    expect(taskDto.createdBy).toBeUndefined();
    expect(taskDto.createdByInfo).toBeUndefined();
    expect(taskDto.updatedBy).toBeUndefined();
    expect(taskDto.updatedByInfo).toBeUndefined();
  });
});
