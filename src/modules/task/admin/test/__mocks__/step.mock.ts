import { StepResponseDto } from '@modules/task/admin/dto';
import { StepType } from '@modules/task/interfaces/step-config.interface';

/**
 * Mock data cho step loại prompt
 */
export const mockPromptStep: any = {
  stepId: '123e4567-e89b-12d3-a456-426614174010',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  orderIndex: 1,
  stepName: 'Bước phân tích dữ liệu',
  stepDescription: 'B<PERSON>ớc này sẽ phân tích dữ liệu từ các nguồn khác nhau',
  stepType: 'prompt',
  stepConfig: {
    promptText: 'Phân tích dữ liệu sau đây và đưa ra các insights: {{data}}',
    inputType: 'text',
    required: true,
    variables: ['data'],
    options: {
      temperature: 0.7,
      maxTokens: 2000
    }
  },
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho step loại google_sheet
 */
export const mockGoogleSheetStep: any = {
  stepId: '123e4567-e89b-12d3-a456-426614174011',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  orderIndex: 2,
  stepName: 'Bước xuất dữ liệu ra Google Sheet',
  stepDescription: 'Bước này sẽ xuất dữ liệu phân tích ra Google Sheet',
  stepType: 'google_sheet',
  stepConfig: {
    spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
    sheetName: 'Data Analysis',
    range: 'A1:F10',
    operation: 'write',
    data: '{{analysis_result}}'
  },
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho step loại agent
 */
export const mockAgentStep: any = {
  stepId: '123e4567-e89b-12d3-a456-426614174012',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  orderIndex: 3,
  stepName: 'Bước sử dụng agent phân tích nâng cao',
  stepDescription: 'Bước này sẽ sử dụng agent để phân tích dữ liệu nâng cao',
  stepType: 'agent',
  stepConfig: {
    agentId: '123e4567-e89b-12d3-a456-426614174001',
    parameters: {
      input: '{{analysis_result}}',
      options: {
        temperature: 0.5,
        maxTokens: 2000,
        topP: 0.9
      }
    }
  },
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho danh sách step
 */
export const mockSteps: any[] = [
  mockPromptStep,
  mockGoogleSheetStep,
  mockAgentStep,
];

/**
 * Mock data cho DTO phản hồi step loại prompt
 */
export const mockPromptStepResponseDto: StepResponseDto = {
  stepId: '123e4567-e89b-12d3-a456-426614174010',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  orderIndex: 1,
  stepName: 'Bước phân tích dữ liệu',
  stepDescription: 'Bước này sẽ phân tích dữ liệu từ các nguồn khác nhau',
  stepType: 'prompt' as StepType,
  stepConfig: {
    promptText: 'Phân tích dữ liệu sau đây và đưa ra các insights: {{data}}',
    inputType: 'text',
    required: true,
    variables: ['data'],
    options: {
      temperature: 0.7,
      maxTokens: 2000
    }
  },
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho DTO phản hồi step loại google_sheet
 */
export const mockGoogleSheetStepResponseDto: StepResponseDto = {
  stepId: '123e4567-e89b-12d3-a456-426614174011',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  orderIndex: 2,
  stepName: 'Bước xuất dữ liệu ra Google Sheet',
  stepDescription: 'Bước này sẽ xuất dữ liệu phân tích ra Google Sheet',
  stepType: 'google_sheet' as StepType,
  stepConfig: {
    spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
    sheetName: 'Data Analysis',
    range: 'A1:F10',
    operation: 'write',
    data: '{{analysis_result}}'
  },
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho DTO phản hồi step loại agent
 */
export const mockAgentStepResponseDto: StepResponseDto = {
  stepId: '123e4567-e89b-12d3-a456-426614174012',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  orderIndex: 3,
  stepName: 'Bước sử dụng agent phân tích nâng cao',
  stepDescription: 'Bước này sẽ sử dụng agent để phân tích dữ liệu nâng cao',
  stepType: 'agent' as StepType,
  stepConfig: {
    agentId: '123e4567-e89b-12d3-a456-426614174001',
    parameters: {
      input: '{{analysis_result}}',
      options: {
        temperature: 0.5,
        maxTokens: 2000,
        topP: 0.9
      }
    }
  },
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho danh sách DTO phản hồi step
 */
export const mockStepResponseDtos: StepResponseDto[] = [
  mockPromptStepResponseDto,
  mockGoogleSheetStepResponseDto,
  mockAgentStepResponseDto,
];
