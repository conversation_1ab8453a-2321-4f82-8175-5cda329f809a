export class ApiResponseDto<T> {
  result: T;
  message: string;
  statusCode: number;

  constructor(result: T, message: string = 'Success', statusCode: number = 200) {
    this.result = result;
    this.message = message;
    this.statusCode = statusCode;
  }

  static success<T>(result: T, message: string = 'Success'): ApiResponseDto<T> {
    return new ApiResponseDto<T>(result, message, 200);
  }

  static created<T>(result: T, message: string = 'Created'): ApiResponseDto<T> {
    return new ApiResponseDto<T>(result, message, 201);
  }

  static paginated<T>(result: any, message: string = 'Success'): ApiResponseDto<T> {
    return new ApiResponseDto<T>(result, message, 200);
  }

  static getSchema(type: any = null) {
    return {};
  }

  static getPaginatedSchema(type: any = null) {
    return {};
  }

  static getArraySchema(type: any = null) {
    return {};
  }
}

export class PaginatedResult<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
