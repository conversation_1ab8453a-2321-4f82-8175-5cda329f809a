export class AppException extends Error {
  constructor(public errorCode: any, message: string) {
    super(message);
    this.name = 'AppException';
  }
}

export class ErrorCode {
  code: string;
  message: string;
  status: number;

  constructor(code: string, message: string, status: number = 400) {
    this.code = code;
    this.message = message;
    this.status = status;
  }

  static readonly INTERNAL_SERVER_ERROR = 500;
}
