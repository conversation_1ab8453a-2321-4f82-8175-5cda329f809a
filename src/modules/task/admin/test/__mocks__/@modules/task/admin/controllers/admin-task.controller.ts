import { ApiResponseDto } from '@common/response/api-response-dto';
import { CreateTaskDto, QueryTaskDto, TaskResponseDto, UpdateTaskDto } from '@modules/task/admin/dto';

export class AdminTaskController {
  constructor(private readonly adminTaskService: any) {}

  async findAll(queryDto: QueryTaskDto, employee: any): Promise<ApiResponseDto<any>> {
    const result = await this.adminTaskService.findAll(queryDto);
    return ApiResponseDto.paginated(result);
  }

  async findOne(taskId: string, employee: any): Promise<ApiResponseDto<TaskResponseDto>> {
    const result = await this.adminTaskService.findOne(taskId);
    return ApiResponseDto.success(result);
  }

  async create(createTaskDto: CreateTaskDto, employee: any): Promise<ApiResponseDto<TaskResponseDto>> {
    const result = await this.adminTaskService.create(createTaskDto, Number(employee.id));
    return ApiResponseDto.created(result);
  }

  async update(taskId: string, updateTaskDto: UpdateTaskDto, employee: any): Promise<ApiResponseDto<TaskResponseDto>> {
    const result = await this.adminTaskService.update(taskId, updateTaskDto, Number(employee.id));
    return ApiResponseDto.success(result);
  }

  async remove(taskId: string, employee: any): Promise<ApiResponseDto<null>> {
    await this.adminTaskService.remove(taskId, Number(employee.id));
    return ApiResponseDto.success(null, 'Xóa task thành công');
  }
}
