export class CreateTaskDto {
  taskName: string;
  taskDescription?: string;
  agentId: string;
  active?: boolean = true;

  constructor(data: Partial<CreateTaskDto> = {}) {
    Object.assign(this, data);
  }
}

export class UpdateTaskDto {
  taskName?: string;
  taskDescription?: string;
  agentId?: string;
  active?: boolean;

  constructor(data: Partial<UpdateTaskDto> = {}) {
    Object.assign(this, data);
  }
}

export class QueryTaskDto {
  page: number = 1;
  limit: number = 10;
  search?: string;
  agentId?: string;
  active?: boolean;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';

  constructor(data: Partial<QueryTaskDto> = {}) {
    Object.assign(this, data);
  }
}

export class TaskResponseDto {
  taskId: string;
  agentId: string;
  taskName: string;
  taskDescription?: string;
  active: boolean;
  createdBy?: number;
  createdByInfo?: any;
  updatedBy?: number;
  updatedByInfo?: any;
  createdAt: number;
  updatedAt: number;
}

export class CreateStepDto {
  stepName: string;
  stepDescription?: string;
  stepType: string;
  stepConfig: Record<string, any>;
}

export class UpdateStepDto {
  stepName?: string;
  stepDescription?: string;
  stepType?: string;
  stepConfig?: Record<string, any>;
}

export class ReorderStepsDto {
  stepIds: string[];
}

export class StepResponseDto {
  stepId: string;
  taskId: string;
  orderIndex: number;
  stepName: string;
  stepDescription?: string;
  stepType: string;
  stepConfig: Record<string, any>;
  createdAt: number;
  updatedAt: number;
}

export class CreateConnectionDto {
  fromStepId: string;
  toStepId: string;
  outputField: string;
  inputField: string;
}

export class UpdateConnectionDto {
  fromStepId?: string;
  toStepId?: string;
  outputField?: string;
  inputField?: string;
}

export class ConnectionResponseDto {
  connectionId: string;
  taskId: string;
  fromStepId: string;
  toStepId: string;
  outputField: string;
  inputField: string;
  createdAt: number;
}

export class CreateExecutionDto {
  startTime: number;
}

export class UpdateExecutionDto {
  endTime?: number;
  overallStatus?: string;
  executionDetails?: Record<string, any>;
}

export class ExecutionResponseDto {
  taskExecutionId: string;
  taskId: string;
  startTime: number;
  endTime?: number;
  overallStatus: string;
  executionDetails: Record<string, any>;
  createdAt: number;
}

export class QueryExecutionDto {
  page: number = 1;
  limit: number = 10;
  status?: string;
  startTimeFrom?: number;
  startTimeTo?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

export class ExecutionDetailResponseDto {
  taskExecutionId: string;
  taskId: string;
  startTime: number;
  endTime?: number;
  overallStatus: string;
  executionDetails: Record<string, any>;
  createdAt: number;
}
