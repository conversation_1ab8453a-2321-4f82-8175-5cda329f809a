export class AdminTaskExecutionRepository {
  findExecutionById(executionId: string): Promise<any> {
    return Promise.resolve(null);
  }

  findExecutionsWithPagination(
    taskId: string,
    page: number,
    limit: number,
    sortBy: string,
    sortDirection: string,
    status?: string,
    startTimeFrom?: number,
    startTimeTo?: number,
  ): Promise<[any[], number]> {
    return Promise.resolve([[], 0]);
  }
}

export class AdminTaskRepository {
  findTaskById(taskId: string): Promise<any> {
    return Promise.resolve(null);
  }

  findTaskByIdWithEmployeeInfo(taskId: string): Promise<any> {
    return Promise.resolve(null);
  }

  findTasksWithPagination(
    page: number,
    limit: number,
    sortBy: string,
    sortDirection: string,
    search?: string,
    agentId?: string,
    active?: boolean,
  ): Promise<[any[], number]> {
    return Promise.resolve([[], 0]);
  }

  createTask(taskData: any): Promise<any> {
    return Promise.resolve(null);
  }

  updateTask(taskId: string, taskData: any): Promise<any> {
    return Promise.resolve(null);
  }

  softDeleteTask(taskId: string, employeeId: number): Promise<void> {
    return Promise.resolve();
  }
}

export class AdminStepRepository {
  findStepById(stepId: string): Promise<any> {
    return Promise.resolve(null);
  }

  findStepsByTaskId(taskId: string): Promise<any[]> {
    return Promise.resolve([]);
  }

  findStepsByIds(stepIds: string[]): Promise<any[]> {
    return Promise.resolve([]);
  }

  getMaxOrderIndex(taskId: string): Promise<number> {
    return Promise.resolve(0);
  }

  createStep(stepData: any): Promise<any> {
    return Promise.resolve(null);
  }

  updateStep(stepId: string, stepData: any): Promise<any> {
    return Promise.resolve(null);
  }

  deleteStep(stepId: string): Promise<void> {
    return Promise.resolve();
  }

  reorderStepsAfterDelete(taskId: string, orderIndex: number): Promise<void> {
    return Promise.resolve();
  }

  updateStepsOrder(stepIds: string[]): Promise<any[]> {
    return Promise.resolve([]);
  }
}

export class AdminStepConnectionRepository {
  findConnectionById(connectionId: string): Promise<any> {
    return Promise.resolve(null);
  }

  findConnectionsByTaskId(taskId: string): Promise<any[]> {
    return Promise.resolve([]);
  }

  createConnection(connectionData: any): Promise<any> {
    return Promise.resolve(null);
  }

  updateConnection(connectionId: string, connectionData: any): Promise<any> {
    return Promise.resolve(null);
  }

  deleteConnection(connectionId: string): Promise<void> {
    return Promise.resolve();
  }
}
