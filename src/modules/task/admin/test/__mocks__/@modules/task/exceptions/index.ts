import { ErrorCode } from '../../../@common/exceptions';

export const ADMIN_TASK_ERROR_CODES = {
  ADMIN_TASK_FETCH_FAILED: new ErrorCode('ADMIN_TASK_FETCH_FAILED', 'Lỗi khi lấy danh sách task', 400),
  ADMIN_TASK_NOT_FOUND: new ErrorCode('ADMIN_TASK_NOT_FOUND', 'Task không tồn tại', 404),
  ADMIN_TASK_CREATION_FAILED: new ErrorCode('ADMIN_TASK_CREATION_FAILED', 'Lỗi khi tạo task', 400),
  ADMIN_TASK_UPDATE_FAILED: new ErrorCode('ADMIN_TASK_UPDATE_FAILED', 'Lỗi khi cập nhật task', 400),
  ADMIN_TASK_DELETE_FAILED: new ErrorCode('ADMIN_TASK_DELETE_FAILED', 'Lỗi khi xóa task', 400),
  ADMIN_TASK_INVALID_DATA: new ErrorCode('ADMIN_TASK_INVALID_DATA', 'D<PERSON> liệu task không hợp lệ', 400),
  ADMIN_TASK_MISSING_AGENT: new ErrorCode('ADMIN_TASK_MISSING_AGENT', 'Thiếu thông tin agent', 400),
  ADMIN_TASK_AGENT_NOT_FOUND: new ErrorCode('ADMIN_TASK_AGENT_NOT_FOUND', 'Không tìm thấy agent', 404),
  ADMIN_TASK_UNAUTHORIZED: new ErrorCode('ADMIN_TASK_UNAUTHORIZED', 'Không có quyền truy cập task', 403),
  TASK_NAME_REQUIRED: new ErrorCode('TASK_NAME_REQUIRED', 'Tên nhiệm vụ là bắt buộc', 400),
  TASK_NAME_TOO_LONG: new ErrorCode('TASK_NAME_TOO_LONG', 'Tên nhiệm vụ quá dài', 400),
};

export const ADMIN_STEP_ERROR_CODES = {
  ADMIN_STEP_FETCH_FAILED: new ErrorCode('ADMIN_STEP_FETCH_FAILED', 'Lỗi khi lấy danh sách các bước', 400),
  ADMIN_STEP_NOT_FOUND: new ErrorCode('ADMIN_STEP_NOT_FOUND', 'Step không tồn tại', 404),
  ADMIN_STEP_CREATION_FAILED: new ErrorCode('ADMIN_STEP_CREATION_FAILED', 'Lỗi khi tạo bước', 400),
  ADMIN_STEP_UPDATE_FAILED: new ErrorCode('ADMIN_STEP_UPDATE_FAILED', 'Lỗi khi cập nhật bước', 400),
  ADMIN_STEP_DELETE_FAILED: new ErrorCode('ADMIN_STEP_DELETE_FAILED', 'Lỗi khi xóa bước', 400),
  ADMIN_STEP_INVALID_DATA: new ErrorCode('ADMIN_STEP_INVALID_DATA', 'Dữ liệu bước không hợp lệ', 400),
  ADMIN_STEP_UNAUTHORIZED: new ErrorCode('ADMIN_STEP_UNAUTHORIZED', 'Không có quyền truy cập bước', 403),
  STEP_NAME_REQUIRED: new ErrorCode('STEP_NAME_REQUIRED', 'Tên bước là bắt buộc', 400),
  STEP_NAME_TOO_LONG: new ErrorCode('STEP_NAME_TOO_LONG', 'Tên bước quá dài', 400),
  STEP_INVALID_TYPE: new ErrorCode('STEP_INVALID_TYPE', 'Loại bước không hợp lệ', 400),
  STEP_CONFIG_INVALID: new ErrorCode('STEP_CONFIG_INVALID', 'Cấu hình bước không hợp lệ', 400),
};

export const ADMIN_CONNECTION_ERROR_CODES = {
  ADMIN_CONNECTION_FETCH_FAILED: new ErrorCode('ADMIN_CONNECTION_FETCH_FAILED', 'Lỗi khi lấy danh sách kết nối', 400),
  ADMIN_CONNECTION_NOT_FOUND: new ErrorCode('ADMIN_CONNECTION_NOT_FOUND', 'Kết nối không tồn tại', 404),
  ADMIN_CONNECTION_CREATION_FAILED: new ErrorCode('ADMIN_CONNECTION_CREATION_FAILED', 'Lỗi khi tạo kết nối', 400),
  ADMIN_CONNECTION_UPDATE_FAILED: new ErrorCode('ADMIN_CONNECTION_UPDATE_FAILED', 'Lỗi khi cập nhật kết nối', 400),
  ADMIN_CONNECTION_DELETE_FAILED: new ErrorCode('ADMIN_CONNECTION_DELETE_FAILED', 'Lỗi khi xóa kết nối', 400),
  ADMIN_CONNECTION_INVALID_DATA: new ErrorCode('ADMIN_CONNECTION_INVALID_DATA', 'Dữ liệu kết nối không hợp lệ', 400),
  ADMIN_CONNECTION_UNAUTHORIZED: new ErrorCode('ADMIN_CONNECTION_UNAUTHORIZED', 'Không có quyền truy cập kết nối', 403),
};

export const ADMIN_EXECUTION_ERROR_CODES = {
  ADMIN_EXECUTION_FETCH_FAILED: new ErrorCode('ADMIN_EXECUTION_FETCH_FAILED', 'Lỗi khi lấy danh sách lịch sử thực thi', 400),
  ADMIN_EXECUTION_NOT_FOUND: new ErrorCode('ADMIN_EXECUTION_NOT_FOUND', 'Lịch sử thực thi không tồn tại', 404),
  ADMIN_EXECUTION_CREATION_FAILED: new ErrorCode('ADMIN_EXECUTION_CREATION_FAILED', 'Lỗi khi tạo lịch sử thực thi', 400),
  ADMIN_EXECUTION_UPDATE_FAILED: new ErrorCode('ADMIN_EXECUTION_UPDATE_FAILED', 'Lỗi khi cập nhật lịch sử thực thi', 400),
  ADMIN_EXECUTION_INVALID_DATA: new ErrorCode('ADMIN_EXECUTION_INVALID_DATA', 'Dữ liệu lịch sử thực thi không hợp lệ', 400),
  ADMIN_EXECUTION_UNAUTHORIZED: new ErrorCode('ADMIN_EXECUTION_UNAUTHORIZED', 'Không có quyền truy cập lịch sử thực thi', 403),
};
