import { ApiResponseDto } from '@common/response/api-response-dto';
import { CreateStepDto, ReorderStepsDto, StepResponseDto, UpdateStepDto } from '@modules/task/admin/dto';

export class AdminStepController {
  constructor(private readonly adminStepService: any) {}

  async findAll(taskId: string, employee: any): Promise<ApiResponseDto<StepResponseDto[]>> {
    const result = await this.adminStepService.findAllByTaskId(taskId);
    return ApiResponseDto.success(result);
  }

  async findOne(taskId: string, stepId: string, employee: any): Promise<ApiResponseDto<StepResponseDto>> {
    const result = await this.adminStepService.findOne(stepId);
    return ApiResponseDto.success(result);
  }

  async create(taskId: string, createStepDto: CreateStepDto, employee: any): Promise<ApiResponseDto<StepResponseDto>> {
    const result = await this.adminStepService.create(taskId, createStepDto);
    return ApiResponseDto.created(result);
  }

  async update(taskId: string, stepId: string, updateStepDto: UpdateStepDto, employee: any): Promise<ApiResponseDto<StepResponseDto>> {
    const result = await this.adminStepService.update(stepId, updateStepDto);
    return ApiResponseDto.success(result);
  }

  async remove(taskId: string, stepId: string, employee: any): Promise<ApiResponseDto<null>> {
    await this.adminStepService.remove(stepId);
    return ApiResponseDto.success(null, 'Xóa bước thành công');
  }

  async reorder(taskId: string, reorderStepsDto: ReorderStepsDto, employee: any): Promise<ApiResponseDto<StepResponseDto[]>> {
    const result = await this.adminStepService.reorder(taskId, reorderStepsDto);
    return ApiResponseDto.success(result);
  }
}
