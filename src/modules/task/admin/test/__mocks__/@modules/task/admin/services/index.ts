export class AdminExecutionService {
  findAllByTaskId(taskId: string, queryDto: any): Promise<any> {
    return Promise.resolve({
      items: [],
      meta: {
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: 10,
        totalPages: 0,
        currentPage: 1,
      },
    });
  }

  findOne(taskId: string, executionId: string): Promise<any> {
    return Promise.resolve(null);
  }
}
