export class ValidationHelper {
  validateTaskExists(task: any): void {
    if (!task) {
      throw new Error('Task không tồn tại');
    }
  }

  validateExecutionExists(execution: any): void {
    if (!execution) {
      throw new Error('<PERSON>hô<PERSON> tìm thấy phiên thực thi');
    }
  }

  validateExecutionBelongsToTask(execution: any, taskId: string): void {
    if (execution.taskId !== taskId) {
      throw new Error('<PERSON>ần thực thi không thuộc task này');
    }
  }

  validateTaskCreation(taskName: string, agentId: string): void {
    if (!taskName) {
      throw new Error('Tên nhiệm vụ là bắt buộc');
    }
    if (!agentId) {
      throw new Error('ID của agent là bắt buộc');
    }
  }

  validateAgentExists(agentId: string): Promise<void> {
    return Promise.resolve();
  }
}
