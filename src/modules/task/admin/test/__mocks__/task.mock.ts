import { PaginatedResult } from '@common/response/api-response-dto';
import { TaskResponseDto } from '@modules/task/admin/dto';
import { EmployeeInfoDto } from '@modules/task/admin/dto/common/employee-info.dto';

/**
 * Mock data cho task
 */
export const mockTask: any = {
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  agentId: '123e4567-e89b-12d3-a456-426614174001',
  taskName: 'Nhiệm vụ phân tích dữ liệu',
  taskDescription: '<PERSON>hiệ<PERSON> vụ này sẽ phân tích dữ liệu từ nhiều nguồn khác nhau',
  active: true,
  createdBy: 1,
  updatedBy: 1,
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  deletedAt: null,
};

/**
 * Mock data cho task của admin
 */
export const mockAdminTask: any = {
  taskId: '123e4567-e89b-12d3-a456-426614174002',
  agentId: '123e4567-e89b-12d3-a456-426614174003',
  taskName: 'Nhiệm vụ tổng hợp báo cáo',
  taskDescription: 'Nhiệm vụ này sẽ tổng hợp báo cáo từ nhiều nguồn dữ liệu',
  active: true,
  createdBy: 2,
  updatedBy: 2,
  createdAt: 1625097700000,
  updatedAt: 1625097700000,
  deletedAt: null,
};

/**
 * Mock data cho danh sách task
 */
export const mockTasks: any[] = [
  mockTask,
  mockAdminTask,
];

/**
 * Mock data cho thông tin nhân viên
 */
export const mockEmployeeInfo: EmployeeInfoDto = {
  id: 1,
  fullName: 'Nguyễn Văn A',
  email: '<EMAIL>',
  avatar: 'https://cdn.redai.vn/employees/avatar.jpg',
};

/**
 * Mock data cho DTO phản hồi task
 */
export const mockTaskResponseDto: TaskResponseDto = {
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  agentId: '123e4567-e89b-12d3-a456-426614174001',
  taskName: 'Nhiệm vụ phân tích dữ liệu',
  taskDescription: 'Nhiệm vụ này sẽ phân tích dữ liệu từ nhiều nguồn khác nhau',
  active: true,
  createdBy: 1,
  createdByInfo: mockEmployeeInfo,
  updatedBy: 1,
  updatedByInfo: mockEmployeeInfo,
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho DTO phản hồi task của admin
 */
export const mockAdminTaskResponseDto: TaskResponseDto = {
  taskId: '123e4567-e89b-12d3-a456-426614174002',
  agentId: '123e4567-e89b-12d3-a456-426614174003',
  taskName: 'Nhiệm vụ tổng hợp báo cáo',
  taskDescription: 'Nhiệm vụ này sẽ tổng hợp báo cáo từ nhiều nguồn dữ liệu',
  active: true,
  createdBy: 2,
  createdByInfo: {
    id: 2,
    fullName: 'Trần Thị B',
    email: '<EMAIL>',
    avatar: 'https://cdn.redai.vn/employees/avatar2.jpg',
  },
  updatedBy: 2,
  updatedByInfo: {
    id: 2,
    fullName: 'Trần Thị B',
    email: '<EMAIL>',
    avatar: 'https://cdn.redai.vn/employees/avatar2.jpg',
  },
  createdAt: 1625097700000,
  updatedAt: 1625097700000,
};

/**
 * Mock data cho kết quả phân trang
 */
export const mockPaginatedResult: PaginatedResult<any> = {
  items: mockTasks,
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1,
  },
};

/**
 * Mock data cho DTO phản hồi task có phân trang
 */
export const mockPaginatedTaskResponseDto: PaginatedResult<TaskResponseDto> = {
  items: [mockTaskResponseDto, mockAdminTaskResponseDto],
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1,
  },
};
