import { ConnectionResponseDto } from '@modules/task/admin/dto';

/**
 * Mock data cho connection
 */
export const mockConnection: any = {
  connectionId: '123e4567-e89b-12d3-a456-426614174020',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  fromStepId: '123e4567-e89b-12d3-a456-426614174010',
  toStepId: '123e4567-e89b-12d3-a456-426614174011',
  outputField: 'analysis_result',
  inputField: 'data',
  createdAt: 1625097600000,
};

/**
 * Mock data cho connection khác
 */
export const mockConnection2: any = {
  connectionId: '123e4567-e89b-12d3-a456-426614174021',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  fromStepId: '123e4567-e89b-12d3-a456-426614174011',
  toStepId: '123e4567-e89b-12d3-a456-426614174012',
  outputField: 'processed_data',
  inputField: 'analysis_result',
  createdAt: 1625097600000,
};

/**
 * Mock data cho danh sách connection
 */
export const mockConnections: any[] = [
  mockConnection,
  mockConnection2,
];

/**
 * Mock data cho DTO phản hồi connection
 */
export const mockConnectionResponseDto: ConnectionResponseDto = {
  connectionId: '123e4567-e89b-12d3-a456-426614174020',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  fromStepId: '123e4567-e89b-12d3-a456-426614174010',
  toStepId: '123e4567-e89b-12d3-a456-426614174011',
  outputField: 'analysis_result',
  inputField: 'data',
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho DTO phản hồi connection khác
 */
export const mockConnectionResponseDto2: ConnectionResponseDto = {
  connectionId: '123e4567-e89b-12d3-a456-426614174021',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  fromStepId: '123e4567-e89b-12d3-a456-426614174011',
  toStepId: '123e4567-e89b-12d3-a456-426614174012',
  outputField: 'processed_data',
  inputField: 'analysis_result',
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho danh sách DTO phản hồi connection
 */
export const mockConnectionResponseDtos: ConnectionResponseDto[] = [
  mockConnectionResponseDto,
  mockConnectionResponseDto2,
];
