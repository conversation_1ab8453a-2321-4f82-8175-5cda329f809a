import { AdminTaskRepository, AdminStepRepository, AdminStepConnectionRepository, AdminTaskExecutionRepository } from '@modules/task/repositories';
import { mockTask, mockTasks, mockPaginatedResult } from './task.mock';
import { mockPromptStep, mockSteps } from './step.mock';
import { mockConnection, mockConnections } from './connection.mock';
import { mockCompletedExecution, mockExecutions } from './execution.mock';

/**
 * Mock cho AdminTaskRepository
 */
export const mockAdminTaskRepository: Partial<AdminTaskRepository> = {
  findTaskById: jest.fn().mockImplementation((taskId: string) => {
    if (taskId === '123e4567-e89b-12d3-a456-426614174000') {
      return Promise.resolve(mockTask);
    }
    return Promise.resolve(null);
  }),

  findTaskByIdWithEmployeeInfo: jest.fn().mockImplementation((taskId: string) => {
    if (taskId === '123e4567-e89b-12d3-a456-426614174000') {
      return Promise.resolve({
        ...mockTask,
        createdByInfo: {
          id: 1,
          fullName: 'Nguyễn Văn A',
          email: '<EMAIL>',
          avatar: 'https://cdn.redai.vn/employees/avatar.jpg'
        },
        updatedByInfo: {
          id: 1,
          fullName: 'Nguyễn Văn A',
          email: '<EMAIL>',
          avatar: 'https://cdn.redai.vn/employees/avatar.jpg'
        }
      });
    }
    return Promise.resolve(null);
  }),

  findTasksWithPagination: jest.fn().mockImplementation(
    (_page: number, _limit: number, _sortBy: string, _sortOrder: 'ASC' | 'DESC', _search?: string, _agentId?: string, _active?: boolean) => {
      return Promise.resolve([mockTasks, mockTasks.length]);
    }
  ),

  createTask: jest.fn().mockImplementation((taskData: any) => {
    return Promise.resolve({
      ...mockTask,
      ...taskData,
    });
  }),

  updateTask: jest.fn().mockImplementation((_taskId: string, taskData: any) => {
    return Promise.resolve({
      ...mockTask,
      ...taskData,
    });
  }),

  softDeleteTask: jest.fn().mockImplementation((_taskId: string, _employeeId: number) => {
    return Promise.resolve();
  }),
};

/**
 * Mock cho AdminStepRepository
 */
export const mockAdminStepRepository: Partial<AdminStepRepository> = {
  findStepById: jest.fn().mockImplementation((stepId: string) => {
    if (stepId === '123e4567-e89b-12d3-a456-426614174010') {
      return Promise.resolve(mockPromptStep);
    }
    return Promise.resolve(null);
  }),

  findStepsByTaskId: jest.fn().mockImplementation((_taskId: string) => {
    return Promise.resolve(mockSteps);
  }),

  findStepsByIds: jest.fn().mockImplementation((stepIds: string[]) => {
    return Promise.resolve(mockSteps.filter(step => stepIds.includes(step.stepId)));
  }),

  getMaxOrderIndex: jest.fn().mockImplementation((_taskId: string) => {
    return Promise.resolve(3);
  }),

  createStep: jest.fn().mockImplementation((stepData: any) => {
    return Promise.resolve({
      ...mockPromptStep,
      ...stepData,
    });
  }),

  updateStep: jest.fn().mockImplementation((_stepId: string, stepData: any) => {
    return Promise.resolve({
      ...mockPromptStep,
      ...stepData,
    });
  }),

  deleteStep: jest.fn().mockImplementation((_stepId: string) => {
    return Promise.resolve();
  }),

  updateStepsOrder: jest.fn().mockImplementation((stepIds: string[]) => {
    return Promise.resolve();
  }),

  reorderStepsAfterDelete: jest.fn().mockImplementation((_taskId: string, _deletedOrderIndex: number) => {
    return Promise.resolve();
  }),
};

/**
 * Mock cho AdminStepConnectionRepository
 */
export const mockAdminConnectionRepository: Partial<AdminStepConnectionRepository> = {
  findConnectionById: jest.fn().mockImplementation((connectionId: string) => {
    if (connectionId === '123e4567-e89b-12d3-a456-426614174020') {
      return Promise.resolve(mockConnection);
    }
    return Promise.resolve(null);
  }),

  findConnectionsByTaskId: jest.fn().mockImplementation((_taskId: string) => {
    return Promise.resolve(mockConnections);
  }),

  createConnection: jest.fn().mockImplementation((connectionData: any) => {
    return Promise.resolve({
      ...mockConnection,
      ...connectionData,
    });
  }),

  updateConnection: jest.fn().mockImplementation((_connectionId: string, connectionData: any) => {
    return Promise.resolve({
      ...mockConnection,
      ...connectionData,
    });
  }),

  deleteConnection: jest.fn().mockImplementation((_connectionId: string) => {
    return Promise.resolve();
  }),

  deleteConnectionsByStepId: jest.fn().mockImplementation((_stepId: string) => {
    return Promise.resolve();
  }),


};

/**
 * Mock cho AdminTaskExecutionRepository
 */
export const mockAdminTaskExecutionRepository: Partial<AdminTaskExecutionRepository> = {
  findExecutionById: jest.fn().mockImplementation((executionId: string) => {
    if (executionId === '123e4567-e89b-12d3-a456-426614174030') {
      return Promise.resolve(mockCompletedExecution);
    }
    return Promise.resolve(null);
  }),

  findExecutionsWithPagination: jest.fn().mockImplementation((_taskId: string, _page: number, _limit: number, _sortBy: string, _sortOrder: string, _status?: string, _startTimeFrom?: number, _startTimeTo?: number) => {
    return Promise.resolve([mockExecutions, mockExecutions.length]);
  }),

  save: jest.fn().mockImplementation((executionData: any) => {
    return Promise.resolve({
      ...mockCompletedExecution,
      ...executionData,
    });
  }),

  update: jest.fn().mockImplementation((_executionId: string, executionData: any) => {
    return Promise.resolve({
      ...mockCompletedExecution,
      ...executionData,
    });
  }),


};
