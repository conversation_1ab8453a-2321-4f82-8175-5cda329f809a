import { Is<PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON><PERSON>ption<PERSON>, <PERSON>UUI<PERSON>, <PERSON>Length } from 'class-validator';

export class CreateTaskDto {
  @IsNotEmpty({ message: 'Tên nhiệm vụ là bắt buộc' })
  @MaxLength(255, { message: 'Tê<PERSON> nhiệm vụ không được vượt quá 255 ký tự' })
  taskName: string;

  @IsOptional()
  @MaxLength(1000, { message: '<PERSON><PERSON> tả nhiệm vụ không được vượt quá 1000 ký tự' })
  taskDescription?: string;

  @IsNotEmpty({ message: 'ID của agent là bắt buộ<PERSON>' })
  @IsUUID('all', { message: 'ID của agent phải là UUID hợp lệ' })
  agentId: string;

  @IsOptional()
  @IsBoolean({ message: 'Trạng thái active phải là boolean' })
  active?: boolean = true;
}
