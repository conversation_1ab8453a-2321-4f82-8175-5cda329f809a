import { IsBoolean, IsOptional, IsUUID, MaxLength } from 'class-validator';

export class UpdateTaskDto {
  @IsOptional()
  @MaxLength(255, { message: 'Tê<PERSON> nhiệm vụ không được vượt quá 255 ký tự' })
  taskName?: string;

  @IsOptional()
  @MaxLength(1000, { message: 'Mô tả nhiệm vụ không được vượt quá 1000 ký tự' })
  taskDescription?: string;

  @IsOptional()
  @IsUUID('all', { message: 'ID của agent phải là UUID hợp lệ' })
  agentId?: string;

  @IsOptional()
  @IsBoolean({ message: 'Trạng thái active phải là boolean' })
  active?: boolean;
}
