import { IsBoolean, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';
import { SortDirection } from '../../__mocks__/@common/dto/query.dto';

export class QueryTaskDto {
  @IsNumber({}, { message: 'Trang phải là số' })
  @Type(() => Number)
  page: number = 1;

  @IsNumber({}, { message: 'Số lượng mỗi trang phải là số' })
  @Type(() => Number)
  limit: number = 10;

  @IsOptional()
  search?: string;

  @IsOptional()
  @IsUUID('all', { message: 'ID của agent phải là UUID hợp lệ' })
  agentId?: string;

  @IsOptional()
  @IsBoolean({ message: 'Trạng thái active phải là boolean' })
  @Type(() => Boolean)
  active?: boolean;

  @IsOptional()
  sortBy?: string;

  @IsOptional()
  sortDirection?: SortDirection;
}
