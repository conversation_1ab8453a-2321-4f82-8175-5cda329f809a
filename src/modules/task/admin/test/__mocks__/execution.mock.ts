import { ExecutionResponseDto, ExecutionDetailResponseDto } from '@modules/task/admin/dto';
import { TaskExecutionStatus } from '@modules/task/enums/task-execution-status.enum';

/**
 * Mock data cho execution đã hoàn thành
 */
export const mockCompletedExecution: any = {
  taskExecutionId: '123e4567-e89b-12d3-a456-426614174030',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  startTime: 1625097600000,
  endTime: 1625097900000,
  overallStatus: TaskExecutionStatus.SUCCESS,
  executionDetails: [
    {
      stepId: '123e4567-e89b-12d3-a456-426614174010',
      status: 'success',
      startTime: 1625097600000,
      endTime: 1625097700000,
      input: { data: 'Raw data for analysis' },
      output: { analysis_result: 'Analyzed data with insights' },
    },
    {
      stepId: '123e4567-e89b-12d3-a456-426614174011',
      status: 'success',
      startTime: 1625097700000,
      endTime: 1625097800000,
      input: { data: 'Analyzed data with insights' },
      output: { processed_data: 'Data exported to Google Sheet' },
    },
    {
      stepId: '123e4567-e89b-12d3-a456-426614174012',
      status: 'success',
      startTime: 1625097800000,
      endTime: 1625097900000,
      input: { analysis_result: 'Data exported to Google Sheet' },
      output: { final_result: 'Advanced analysis completed' },
    },
  ],
  createdAt: 1625097600000,
};

/**
 * Mock data cho execution đang chạy
 */
export const mockRunningExecution: any = {
  taskExecutionId: '123e4567-e89b-12d3-a456-426614174031',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  startTime: 1625097600000,
  endTime: null,
  overallStatus: TaskExecutionStatus.RUNNING,
  executionDetails: [
    {
      stepId: '123e4567-e89b-12d3-a456-426614174010',
      status: 'success',
      startTime: 1625097600000,
      endTime: 1625097700000,
      input: { data: 'Raw data for analysis' },
      output: { analysis_result: 'Analyzed data with insights' },
    },
    {
      stepId: '123e4567-e89b-12d3-a456-426614174011',
      status: 'running',
      startTime: 1625097700000,
      endTime: null,
      input: { data: 'Analyzed data with insights' },
      output: null,
    },
  ],
  createdAt: 1625097600000,
};

/**
 * Mock data cho execution thất bại
 */
export const mockFailedExecution: any = {
  taskExecutionId: '123e4567-e89b-12d3-a456-426614174032',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  startTime: 1625097600000,
  endTime: 1625097800000,
  overallStatus: TaskExecutionStatus.FAILED,
  executionDetails: [
    {
      stepId: '123e4567-e89b-12d3-a456-426614174010',
      status: 'success',
      startTime: 1625097600000,
      endTime: 1625097700000,
      input: { data: 'Raw data for analysis' },
      output: { analysis_result: 'Analyzed data with insights' },
    },
    {
      stepId: '123e4567-e89b-12d3-a456-426614174011',
      status: 'failed',
      startTime: 1625097700000,
      endTime: 1625097800000,
      input: { data: 'Analyzed data with insights' },
      output: null,
      error: {
        code: 'GOOGLE_SHEET_ACCESS_DENIED',
        message: 'Access denied to Google Sheet',
      },
    },
  ],
  createdAt: 1625097600000,
};

/**
 * Mock data cho danh sách execution
 */
export const mockExecutions: any[] = [
  mockCompletedExecution,
  mockRunningExecution,
  mockFailedExecution,
];

/**
 * Mock data cho DTO phản hồi execution đã hoàn thành
 */
export const mockCompletedExecutionResponseDto: ExecutionDetailResponseDto = {
  taskExecutionId: '123e4567-e89b-12d3-a456-426614174030',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  startTime: 1625097600000,
  endTime: 1625097900000,
  overallStatus: TaskExecutionStatus.SUCCESS,
  executionDetails: [
    {
      stepId: '123e4567-e89b-12d3-a456-426614174010',
      status: 'success',
      startTime: 1625097600000,
      endTime: 1625097700000,
      input: { data: 'Raw data for analysis' },
      output: { analysis_result: 'Analyzed data with insights' },
    },
    {
      stepId: '123e4567-e89b-12d3-a456-426614174011',
      status: 'success',
      startTime: 1625097700000,
      endTime: 1625097800000,
      input: { data: 'Analyzed data with insights' },
      output: { processed_data: 'Data exported to Google Sheet' },
    },
    {
      stepId: '123e4567-e89b-12d3-a456-426614174012',
      status: 'success',
      startTime: 1625097800000,
      endTime: 1625097900000,
      input: { analysis_result: 'Data exported to Google Sheet' },
      output: { final_result: 'Advanced analysis completed' },
    },
  ],
  createdAt: 1625097600000,
};

/**
 * Mock data cho DTO phản hồi execution đang chạy
 */
export const mockRunningExecutionResponseDto: ExecutionDetailResponseDto = {
  taskExecutionId: '123e4567-e89b-12d3-a456-426614174031',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  startTime: 1625097600000,
  endTime: undefined,
  overallStatus: TaskExecutionStatus.RUNNING,
  executionDetails: [
    {
      stepId: '123e4567-e89b-12d3-a456-426614174010',
      status: 'success',
      startTime: 1625097600000,
      endTime: 1625097700000,
      input: { data: 'Raw data for analysis' },
      output: { analysis_result: 'Analyzed data with insights' },
    },
    {
      stepId: '123e4567-e89b-12d3-a456-426614174011',
      status: 'running',
      startTime: 1625097700000,
      endTime: null,
      input: { data: 'Analyzed data with insights' },
      output: null,
    },
  ],
  createdAt: 1625097600000,
};

/**
 * Mock data cho DTO phản hồi execution thất bại
 */
export const mockFailedExecutionResponseDto: ExecutionDetailResponseDto = {
  taskExecutionId: '123e4567-e89b-12d3-a456-426614174032',
  taskId: '123e4567-e89b-12d3-a456-426614174000',
  startTime: 1625097600000,
  endTime: 1625097800000,
  overallStatus: TaskExecutionStatus.FAILED,
  executionDetails: [
    {
      stepId: '123e4567-e89b-12d3-a456-426614174010',
      status: 'success',
      startTime: 1625097600000,
      endTime: 1625097700000,
      input: { data: 'Raw data for analysis' },
      output: { analysis_result: 'Analyzed data with insights' },
    },
    {
      stepId: '123e4567-e89b-12d3-a456-426614174011',
      status: 'failed',
      startTime: 1625097700000,
      endTime: 1625097800000,
      input: { data: 'Analyzed data with insights' },
      output: null,
      error: {
        code: 'GOOGLE_SHEET_ACCESS_DENIED',
        message: 'Access denied to Google Sheet',
      },
    },
  ],
  createdAt: 1625097600000,
};

/**
 * Mock data cho danh sách DTO phản hồi execution
 */
export const mockExecutionResponseDtos: ExecutionDetailResponseDto[] = [
  mockCompletedExecutionResponseDto,
  mockRunningExecutionResponseDto,
  mockFailedExecutionResponseDto,
];
