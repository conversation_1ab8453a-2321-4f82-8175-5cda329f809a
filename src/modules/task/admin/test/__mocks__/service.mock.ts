import { AdminTaskService, AdminStepService, AdminConnectionService, AdminExecutionService } from '@modules/task/admin/services';
import {
  mockTaskResponseDto,
  mockPaginatedTaskResponseDto
} from './task.mock';
import {
  mockStepResponseDtos,
  mockPromptStepResponseDto
} from './step.mock';
import {
  mockConnectionResponseDtos,
  mockConnectionResponseDto
} from './connection.mock';
import {
  mockExecutionResponseDtos,
  mockCompletedExecutionResponseDto
} from './execution.mock';
import {
  CreateTaskDto,
  UpdateTaskDto,
  QueryTaskDto,
  CreateStepDto,
  UpdateStepDto,
  ReorderStepsDto,
  CreateConnectionDto,
  UpdateConnectionDto,
} from '@modules/task/admin/dto';

/**
 * Mock cho AdminTaskService
 */
export const mockAdminTaskService: Partial<AdminTaskService> = {
  findAll: jest.fn().mockImplementation((_queryDto: QueryTaskDto) => {
    return Promise.resolve(mockPaginatedTaskResponseDto);
  }),

  findOne: jest.fn().mockImplementation((taskId: string) => {
    if (taskId === '123e4567-e89b-12d3-a456-426614174000') {
      return Promise.resolve(mockTaskResponseDto);
    }
    throw new Error('Task not found');
  }),

  create: jest.fn().mockImplementation((_createTaskDto: CreateTaskDto, _employeeId: number) => {
    return Promise.resolve(mockTaskResponseDto);
  }),

  update: jest.fn().mockImplementation((_taskId: string, _updateTaskDto: UpdateTaskDto, _employeeId: number) => {
    return Promise.resolve(mockTaskResponseDto);
  }),

  remove: jest.fn().mockImplementation((_taskId: string, _employeeId: number) => {
    return Promise.resolve();
  }),
};

/**
 * Mock cho AdminStepService
 */
export const mockAdminStepService: Partial<AdminStepService> = {
  findAllByTaskId: jest.fn().mockImplementation((_taskId: string) => {
    return Promise.resolve(mockStepResponseDtos);
  }),

  findOne: jest.fn().mockImplementation((_taskId: string, stepId: string) => {
    if (stepId === '123e4567-e89b-12d3-a456-426614174010') {
      return Promise.resolve(mockPromptStepResponseDto);
    }
    throw new Error('Step not found');
  }),

  create: jest.fn().mockImplementation((_taskId: string, _createStepDto: CreateStepDto) => {
    return Promise.resolve(mockPromptStepResponseDto);
  }),

  update: jest.fn().mockImplementation((_taskId: string, _stepId: string, _updateStepDto: UpdateStepDto) => {
    return Promise.resolve(mockPromptStepResponseDto);
  }),

  remove: jest.fn().mockImplementation((_taskId: string, _stepId: string) => {
    return Promise.resolve();
  }),

  reorder: jest.fn().mockImplementation((_taskId: string, _reorderStepsDto: ReorderStepsDto) => {
    return Promise.resolve(mockStepResponseDtos);
  }),
};

/**
 * Mock cho AdminConnectionService
 */
export const mockAdminConnectionService: Partial<AdminConnectionService> = {
  findAllByTaskId: jest.fn().mockImplementation((_taskId: string) => {
    return Promise.resolve(mockConnectionResponseDtos);
  }),

  findOne: jest.fn().mockImplementation((connectionId: string) => {
    if (connectionId === '123e4567-e89b-12d3-a456-426614174020') {
      return Promise.resolve(mockConnectionResponseDto);
    }
    throw new Error('Connection not found');
  }),

  create: jest.fn().mockImplementation((_taskId: string, _createConnectionDto: CreateConnectionDto) => {
    return Promise.resolve(mockConnectionResponseDto);
  }),

  update: jest.fn().mockImplementation((_connectionId: string, _updateConnectionDto: UpdateConnectionDto) => {
    return Promise.resolve(mockConnectionResponseDto);
  }),

  remove: jest.fn().mockImplementation((_connectionId: string) => {
    return Promise.resolve();
  }),
};

/**
 * Mock cho AdminExecutionService
 */
export const mockAdminExecutionService: Partial<AdminExecutionService> = {
  findAllByTaskId: jest.fn().mockImplementation((_taskId: string, _queryDto: any) => {
    return Promise.resolve({
      items: mockExecutionResponseDtos,
      meta: {
        totalItems: mockExecutionResponseDtos.length,
        itemCount: mockExecutionResponseDtos.length,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1
      }
    });
  }),

  findOne: jest.fn().mockImplementation((_taskId: string, executionId: string) => {
    if (executionId === '123e4567-e89b-12d3-a456-426614174030') {
      return Promise.resolve(mockCompletedExecutionResponseDto);
    }
    throw new Error('Execution not found');
  }),
};
