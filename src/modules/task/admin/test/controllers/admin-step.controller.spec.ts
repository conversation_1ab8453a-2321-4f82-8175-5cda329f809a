import { Test, TestingModule } from '@nestjs/testing';

// Mock StepType enum
enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image',
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

// Mock AppException
class AppException extends Error {
  constructor(public code: string, message: string) {
    super(message);
  }
}

// Mock error codes
const ADMIN_STEP_ERROR_CODES = {
  ADMIN_STEP_NOT_FOUND: 'ADMIN_STEP_NOT_FOUND',
  ADMIN_STEP_FETCH_FAILED: 'ADMIN_STEP_FETCH_FAILED',
  STEP_NAME_REQUIRED: 'STEP_NAME_REQUIRED',
  ADMIN_STEP_INVALID_DATA: 'ADMIN_STEP_INVALID_DATA'
};

// Mock ApiResponseDto
class MockApiResponseDto {
  result: any;
  message: string;
  statusCode: number;

  constructor(result: any, message: string, statusCode: number = 200) {
    this.result = result;
    this.message = message;
    this.statusCode = statusCode;
  }

  static success(result: any, message: string = 'Success') {
    return new MockApiResponseDto(result, message, 200);
  }

  static created(result: any, message: string = 'Created') {
    return new MockApiResponseDto(result, message, 201);
  }
}

// Mock classes
class MockAdminStepController {
  constructor(private readonly adminStepService: any) {}

  async findAll(taskId: string, employee: any) {
    const result = await this.adminStepService.findAllByTaskId(taskId);
    return MockApiResponseDto.success(result);
  }

  async findOne(taskId: string, stepId: string, employee: any) {
    const result = await this.adminStepService.findOne(stepId);
    return MockApiResponseDto.success(result);
  }

  async create(taskId: string, createDto: any, employee: any) {
    const result = await this.adminStepService.create(taskId, createDto);
    return MockApiResponseDto.created(result);
  }

  async update(taskId: string, stepId: string, updateDto: any, employee: any) {
    const result = await this.adminStepService.update(stepId, updateDto);
    return MockApiResponseDto.success(result);
  }

  async remove(taskId: string, stepId: string, employee: any) {
    await this.adminStepService.remove(stepId);
    return MockApiResponseDto.success(null, 'Xóa bước thành công');
  }

  async reorder(taskId: string, reorderDto: any, employee: any) {
    const result = await this.adminStepService.reorder(taskId, reorderDto);
    return MockApiResponseDto.success(result);
  }
}

class MockAdminStepService {
  findAllByTaskId = jest.fn();
  findOne = jest.fn();
  create = jest.fn();
  update = jest.fn();
  remove = jest.fn();
  reorder = jest.fn();
}

// Mock DTOs
class CreateStepDto {
  stepName: string;
  stepDescription?: string;
  stepType: StepType;
  stepConfig: any;
}

class UpdateStepDto {
  stepName?: string;
  stepDescription?: string;
  stepConfig?: any;
}

class ReorderStepsDto {
  stepIds: string[];
}

describe('Controller quản lý step (Admin)', () => {
  let controller: AdminStepController;
  let service: AdminStepService;

  beforeEach(() => {
    service = mockAdminStepService as AdminStepService;
    controller = new AdminStepController(service);
  });

  it('phải được định nghĩa', () => {
    expect(controller).toBeDefined();
  });

  describe('lấy danh sách step của task', () => {
    it('phải trả về danh sách step của task', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const employee = { id: '1' };

      jest.spyOn(service, 'findAllByTaskId').mockResolvedValue(mockStepResponseDtos);

      // Act
      const result = await controller.findAll(taskId, employee);

      // Assert
      expect(service.findAllByTaskId).toHaveBeenCalledWith(taskId);
      expect(result.result).toEqual(mockStepResponseDtos);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi task không tồn tại', async () => {
      // Arrange
      const taskId = 'invalid-id';
      const employee = { id: '1' };

      jest.spyOn(service, 'findAllByTaskId').mockRejectedValue(
        new AppException(ADMIN_STEP_ERROR_CODES.ADMIN_STEP_FETCH_FAILED, 'Lỗi khi lấy danh sách các bước')
      );

      // Act & Assert
      await expect(controller.findAll(taskId, employee)).rejects.toThrow(AppException);
    });
  });

  describe('lấy step theo ID', () => {
    it('phải trả về thông tin chi tiết step theo ID', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const stepId = '123e4567-e89b-12d3-a456-426614174010';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockResolvedValue(mockPromptStepResponseDto);

      // Act
      const result = await controller.findOne(taskId, stepId, employee);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith(stepId);
      expect(result.result).toEqual(mockPromptStepResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi không tìm thấy step', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const stepId = 'invalid-id';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockRejectedValue(
        new AppException(ADMIN_STEP_ERROR_CODES.ADMIN_STEP_NOT_FOUND, 'Step không tồn tại')
      );

      // Act & Assert
      await expect(controller.findOne(taskId, stepId, employee)).rejects.toThrow(AppException);
    });
  });

  describe('tạo mới step', () => {
    it('phải tạo step mới thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const createDto = new CreateStepDto();
      createDto.stepName = 'Bước phân tích dữ liệu';
      createDto.stepDescription = 'Bước này sẽ phân tích dữ liệu từ các nguồn khác nhau';
      createDto.stepType = 'prompt' as StepType;
      createDto.stepConfig = {
        promptText: 'Phân tích dữ liệu sau đây và đưa ra các insights: {{data}}',
        inputType: 'text',
        required: true,
        variables: ['data'],
        options: {
          temperature: 0.7,
          maxTokens: 2000
        }
      };
      const employee = { id: '1' };

      jest.spyOn(service, 'create').mockResolvedValue(mockPromptStepResponseDto);

      // Act
      const result = await controller.create(taskId, createDto, employee);

      // Assert
      expect(service.create).toHaveBeenCalledWith(taskId, createDto);
      expect(result.result).toEqual(mockPromptStepResponseDto);
      expect(result.message).toBe('Created');
    });

    it('phải xử lý lỗi khi tạo step với dữ liệu không hợp lệ', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const createDto = new CreateStepDto();
      createDto.stepName = ''; // Tên không hợp lệ
      createDto.stepType = 'prompt' as StepType;
      const employee = { id: '1' };

      jest.spyOn(service, 'create').mockRejectedValue(
        new AppException(ADMIN_STEP_ERROR_CODES.STEP_NAME_REQUIRED, 'Tên bước là bắt buộc')
      );

      // Act & Assert
      await expect(controller.create(taskId, createDto, employee)).rejects.toThrow(AppException);
    });
  });

  describe('cập nhật step', () => {
    it('phải cập nhật step thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const stepId = '123e4567-e89b-12d3-a456-426614174010';
      const updateDto = new UpdateStepDto();
      updateDto.stepName = 'Bước phân tích dữ liệu (đã cập nhật)';
      updateDto.stepConfig = {
        promptText: 'Phân tích dữ liệu sau đây và đưa ra các insights chi tiết: {{data}}',
        inputType: 'text',
        required: true,
        variables: ['data'],
        options: {
          temperature: 0.8,
          maxTokens: 2500
        }
      };
      const employee = { id: '1' };

      jest.spyOn(service, 'update').mockResolvedValue(mockPromptStepResponseDto);

      // Act
      const result = await controller.update(taskId, stepId, updateDto, employee);

      // Assert
      expect(service.update).toHaveBeenCalledWith(stepId, updateDto);
      expect(result.result).toEqual(mockPromptStepResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi cập nhật step không tồn tại', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const stepId = 'invalid-id';
      const updateDto = new UpdateStepDto();
      updateDto.stepName = 'Bước phân tích dữ liệu (đã cập nhật)';
      const employee = { id: '1' };

      jest.spyOn(service, 'update').mockRejectedValue(
        new AppException(ADMIN_STEP_ERROR_CODES.ADMIN_STEP_NOT_FOUND, 'Step không tồn tại')
      );

      // Act & Assert
      await expect(controller.update(taskId, stepId, updateDto, employee)).rejects.toThrow(AppException);
    });
  });

  describe('xóa step', () => {
    it('phải xóa step thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const stepId = '123e4567-e89b-12d3-a456-426614174010';
      const employee = { id: '1' };

      jest.spyOn(service, 'remove').mockResolvedValue(undefined);

      // Act
      const result = await controller.remove(taskId, stepId, employee);

      // Assert
      expect(service.remove).toHaveBeenCalledWith(stepId);
      expect(result.result).toBeNull();
      expect(result.message).toBe('Xóa bước thành công');
    });

    it('phải xử lý lỗi khi xóa step không tồn tại', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const stepId = 'invalid-id';
      const employee = { id: '1' };

      jest.spyOn(service, 'remove').mockRejectedValue(
        new AppException(ADMIN_STEP_ERROR_CODES.ADMIN_STEP_NOT_FOUND, 'Step không tồn tại')
      );

      // Act & Assert
      await expect(controller.remove(taskId, stepId, employee)).rejects.toThrow(AppException);
    });
  });

  describe('sắp xếp lại thứ tự các step', () => {
    it('phải sắp xếp lại thứ tự các step thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const reorderDto = new ReorderStepsDto();
      reorderDto.stepIds = [
        '123e4567-e89b-12d3-a456-426614174012',
        '123e4567-e89b-12d3-a456-426614174010',
        '123e4567-e89b-12d3-a456-426614174011'
      ];
      const employee = { id: '1' };

      jest.spyOn(service, 'reorder').mockResolvedValue(mockStepResponseDtos);

      // Act
      const result = await controller.reorder(taskId, reorderDto, employee);

      // Assert
      expect(service.reorder).toHaveBeenCalledWith(taskId, reorderDto);
      expect(result.result).toEqual(mockStepResponseDtos);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi sắp xếp lại với dữ liệu không hợp lệ', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const reorderDto = new ReorderStepsDto();
      reorderDto.stepIds = []; // Danh sách rỗng không hợp lệ
      const employee = { id: '1' };

      jest.spyOn(service, 'reorder').mockRejectedValue(
        new AppException(ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA, 'Danh sách ID step không được rỗng')
      );

      // Act & Assert
      await expect(controller.reorder(taskId, reorderDto, employee)).rejects.toThrow(AppException);
    });
  });
});
