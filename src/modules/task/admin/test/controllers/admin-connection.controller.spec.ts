import { Test, TestingModule } from '@nestjs/testing';

// Mock AppException
class AppException extends Error {
  constructor(public code: string, message: string) {
    super(message);
  }
}

// Mock error codes
const ADMIN_CONNECTION_ERROR_CODES = {
  ADMIN_CONNECTION_NOT_FOUND: 'ADMIN_CONNECTION_NOT_FOUND',
  ADMIN_CONNECTION_FETCH_FAILED: 'ADMIN_CONNECTION_FETCH_FAILED',
  CONNECTION_SAME_STEP: 'CONNECTION_SAME_STEP'
};

// Mock ApiResponseDto
class MockApiResponseDto {
  result: any;
  message: string;
  statusCode: number;

  constructor(result: any, message: string, statusCode: number = 200) {
    this.result = result;
    this.message = message;
    this.statusCode = statusCode;
  }

  static success(result: any, message: string = 'Success') {
    return new MockApiResponseDto(result, message, 200);
  }

  static created(result: any, message: string = 'Created') {
    return new MockApiResponseDto(result, message, 201);
  }
}

// Mock classes
class MockAdminConnectionController {
  constructor(private readonly adminConnectionService: any) {}

  async findAll(taskId: string, employee: any) {
    const result = await this.adminConnectionService.findAllByTaskId(taskId);
    return MockApiResponseDto.success(result);
  }

  async findOne(taskId: string, connectionId: string, employee: any) {
    const result = await this.adminConnectionService.findOne(connectionId);
    return MockApiResponseDto.success(result);
  }

  async create(taskId: string, createDto: any, employee: any) {
    const result = await this.adminConnectionService.create(taskId, createDto);
    return MockApiResponseDto.created(result);
  }

  async update(taskId: string, connectionId: string, updateDto: any, employee: any) {
    const result = await this.adminConnectionService.update(taskId, connectionId, updateDto);
    return MockApiResponseDto.success(result);
  }

  async remove(taskId: string, connectionId: string, employee: any) {
    await this.adminConnectionService.remove(taskId, connectionId);
    return MockApiResponseDto.success(null, 'Xóa kết nối thành công');
  }
}

class MockAdminConnectionService {
  findAllByTaskId = jest.fn();
  findOne = jest.fn();
  create = jest.fn();
  update = jest.fn();
  remove = jest.fn();
}

// Mock DTOs
class CreateConnectionDto {
  fromStepId: string;
  toStepId: string;
  outputField: string;
  inputField: string;
}

class UpdateConnectionDto {
  outputField?: string;
  inputField?: string;
}

describe('Controller quản lý kết nối giữa các bước trong task (Admin)', () => {
  let controller: AdminConnectionController;
  let service: AdminConnectionService;

  beforeEach(() => {
    service = mockAdminConnectionService as AdminConnectionService;
    controller = new AdminConnectionController(service);
  });

  it('phải được định nghĩa', () => {
    expect(controller).toBeDefined();
  });

  describe('lấy danh sách kết nối', () => {
    it('phải trả về danh sách kết nối của một task', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const employee = { id: '1' };

      jest.spyOn(service, 'findAllByTaskId').mockResolvedValue(mockConnectionResponseDtos);

      // Act
      const result = await controller.findAll(taskId, employee);

      // Assert
      expect(service.findAllByTaskId).toHaveBeenCalledWith(taskId);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(mockConnectionResponseDtos);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi lấy danh sách kết nối', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const employee = { id: '1' };

      jest.spyOn(service, 'findAllByTaskId').mockRejectedValue(
        new AppException(
          ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_FETCH_FAILED,
          'Lỗi khi lấy danh sách kết nối'
        )
      );

      // Act & Assert
      await expect(controller.findAll(taskId, employee)).rejects.toThrow(AppException);
    });
  });

  describe('lấy thông tin chi tiết kết nối', () => {
    it('phải trả về thông tin chi tiết của một kết nối', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const connectionId = '123e4567-e89b-12d3-a456-426614174020';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockResolvedValue(mockConnectionResponseDto);

      // Act
      const result = await controller.findOne(taskId, connectionId, employee);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith(connectionId);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(mockConnectionResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi kết nối không tồn tại', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const connectionId = 'non-existent-id';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockRejectedValue(
        new AppException(
          ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_NOT_FOUND,
          'Không tìm thấy kết nối'
        )
      );

      // Act & Assert
      await expect(controller.findOne(taskId, connectionId, employee)).rejects.toThrow(AppException);
    });
  });

  describe('tạo mới kết nối', () => {
    it('phải tạo mới kết nối thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const createDto = new CreateConnectionDto();
      createDto.fromStepId = '123e4567-e89b-12d3-a456-426614174010';
      createDto.toStepId = '123e4567-e89b-12d3-a456-426614174011';
      createDto.outputField = 'analysis_result';
      createDto.inputField = 'data';
      const employee = { id: '1' };

      jest.spyOn(service, 'create').mockResolvedValue(mockConnectionResponseDto);

      // Act
      const result = await controller.create(taskId, createDto, employee);

      // Assert
      expect(service.create).toHaveBeenCalledWith(taskId, createDto);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(mockConnectionResponseDto);
      expect(result.message).toBe('Created');
    });

    it('phải xử lý lỗi khi tạo kết nối với dữ liệu không hợp lệ', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const createDto = new CreateConnectionDto();
      createDto.fromStepId = '123e4567-e89b-12d3-a456-426614174010';
      createDto.toStepId = '123e4567-e89b-12d3-a456-426614174010'; // Same step ID
      createDto.outputField = 'analysis_result';
      createDto.inputField = 'data';
      const employee = { id: '1' };

      jest.spyOn(service, 'create').mockRejectedValue(
        new AppException(
          ADMIN_CONNECTION_ERROR_CODES.CONNECTION_SAME_STEP,
          'Không thể kết nối một bước với chính nó'
        )
      );

      // Act & Assert
      await expect(controller.create(taskId, createDto, employee)).rejects.toThrow(AppException);
    });
  });

  describe('cập nhật kết nối', () => {
    it('phải cập nhật kết nối thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const connectionId = '123e4567-e89b-12d3-a456-426614174020';
      const updateDto = new UpdateConnectionDto();
      updateDto.outputField = 'updated_field';
      updateDto.inputField = 'updated_input';
      const employee = { id: '1' };

      const updatedConnection = {
        ...mockConnectionResponseDto,
        outputField: 'updated_field',
        inputField: 'updated_input'
      };

      jest.spyOn(service, 'update').mockResolvedValue(updatedConnection);

      // Act
      const result = await controller.update(taskId, connectionId, updateDto, employee);

      // Assert
      expect(service.update).toHaveBeenCalledWith(taskId, connectionId, updateDto);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(updatedConnection);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi cập nhật kết nối không tồn tại', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const connectionId = 'non-existent-id';
      const updateDto = new UpdateConnectionDto();
      const employee = { id: '1' };

      jest.spyOn(service, 'update').mockRejectedValue(
        new AppException(
          ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_NOT_FOUND,
          'Không tìm thấy kết nối'
        )
      );

      // Act & Assert
      await expect(controller.update(taskId, connectionId, updateDto, employee)).rejects.toThrow(AppException);
    });
  });

  describe('xóa kết nối', () => {
    it('phải xóa kết nối thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const connectionId = '123e4567-e89b-12d3-a456-426614174020';
      const employee = { id: '1' };

      jest.spyOn(service, 'remove').mockResolvedValue();

      // Act
      const result = await controller.remove(taskId, connectionId, employee);

      // Assert
      expect(service.remove).toHaveBeenCalledWith(taskId, connectionId);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toBeNull();
      expect(result.message).toBe('Xóa kết nối thành công');
    });

    it('phải xử lý lỗi khi xóa kết nối không tồn tại', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const connectionId = 'non-existent-id';
      const employee = { id: '1' };

      jest.spyOn(service, 'remove').mockRejectedValue(
        new AppException(
          ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_NOT_FOUND,
          'Không tìm thấy kết nối'
        )
      );

      // Act & Assert
      await expect(controller.remove(taskId, connectionId, employee)).rejects.toThrow(AppException);
    });
  });
});
