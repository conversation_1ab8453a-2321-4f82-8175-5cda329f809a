import { AdminTaskController } from '../__mocks__/@modules/task/admin/controllers';
import { AdminTaskService } from '../../services';
import { mockPaginatedTaskResponseDto, mockTaskResponseDto } from '../__mocks__/task.mock';
import { mockAdminTaskService } from '../__mocks__/service.mock';
import { CreateTaskDto, UpdateTaskDto } from '../../dto';
import { QueryTaskDto } from '../__mocks__/@modules/task/admin/dto';
import { AppException } from '../__mocks__/@common/exceptions';
import { ADMIN_TASK_ERROR_CODES } from '../__mocks__/@modules/task/exceptions';

describe('Controller quản lý task (Admin)', () => {
  let controller: AdminTaskController;
  let service: AdminTaskService;

  beforeEach(() => {
    service = mockAdminTaskService as AdminTaskService;
    controller = new AdminTaskController(service);
  });

  it('phải được định nghĩa', () => {
    expect(controller).toBeDefined();
  });

  describe('lấy danh sách task', () => {
    it('phải trả về danh sách task có phân trang', async () => {
      // Arrange
      const queryDto = new QueryTaskDto();
      queryDto.page = 1;
      queryDto.limit = 10;
      const employee = { id: '1' };

      jest.spyOn(service, 'findAll').mockResolvedValue(mockPaginatedTaskResponseDto);

      // Act
      const result = await controller.findAll(queryDto, employee);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(queryDto);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(mockPaginatedTaskResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý các tham số tìm kiếm và lọc', async () => {
      // Arrange
      const queryDto = new QueryTaskDto();
      queryDto.page = 1;
      queryDto.limit = 10;
      queryDto.search = 'phân tích';
      queryDto.agentId = '123e4567-e89b-12d3-a456-426614174001';
      queryDto.active = true;
      const employee = { id: '1' };

      jest.spyOn(service, 'findAll').mockResolvedValue(mockPaginatedTaskResponseDto);

      // Act
      const result = await controller.findAll(queryDto, employee);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(expect.objectContaining({
        search: 'phân tích',
        agentId: '123e4567-e89b-12d3-a456-426614174001',
        active: true
      }));
      expect(result.result).toEqual(mockPaginatedTaskResponseDto);
    });
  });

  describe('lấy task theo ID', () => {
    it('phải trả về thông tin chi tiết task theo ID', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockResolvedValue(mockTaskResponseDto);

      // Act
      const result = await controller.findOne(taskId, employee);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith(taskId);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(mockTaskResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi không tìm thấy task', async () => {
      // Arrange
      const taskId = 'invalid-id';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockRejectedValue(
        new AppException(ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND, 'Task không tồn tại')
      );

      // Act & Assert
      await expect(controller.findOne(taskId, employee)).rejects.toThrow(AppException);
    });
  });

  describe('tạo mới task', () => {
    it('phải tạo task mới thành công', async () => {
      // Arrange
      const createDto = new CreateTaskDto();
      createDto.taskName = 'Nhiệm vụ phân tích dữ liệu';
      createDto.taskDescription = 'Nhiệm vụ này sẽ phân tích dữ liệu từ nhiều nguồn khác nhau';
      createDto.agentId = '123e4567-e89b-12d3-a456-426614174001';
      createDto.active = true;
      const employee = { id: '1' };

      jest.spyOn(service, 'create').mockResolvedValue(mockTaskResponseDto);

      // Act
      const result = await controller.create(createDto, employee);

      // Assert
      expect(service.create).toHaveBeenCalledWith(createDto, 1);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(mockTaskResponseDto);
      expect(result.message).toBe('Created');
    });

    it('phải xử lý lỗi khi tạo task với dữ liệu không hợp lệ', async () => {
      // Arrange
      const createDto = new CreateTaskDto();
      createDto.taskName = ''; // Tên không hợp lệ
      createDto.agentId = '123e4567-e89b-12d3-a456-426614174001';
      const employee = { id: '1' };

      jest.spyOn(service, 'create').mockRejectedValue(
        new AppException(ADMIN_TASK_ERROR_CODES.TASK_NAME_REQUIRED, 'Tên nhiệm vụ là bắt buộc')
      );

      // Act & Assert
      await expect(controller.create(createDto, employee)).rejects.toThrow(AppException);
    });
  });

  describe('cập nhật task', () => {
    it('phải cập nhật task thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const updateDto = new UpdateTaskDto();
      updateDto.taskName = 'Nhiệm vụ phân tích dữ liệu (đã cập nhật)';
      updateDto.active = false;
      const employee = { id: '1' };

      jest.spyOn(service, 'update').mockResolvedValue(mockTaskResponseDto);

      // Act
      const result = await controller.update(taskId, updateDto, employee);

      // Assert
      expect(service.update).toHaveBeenCalledWith(taskId, updateDto, 1);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(mockTaskResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi cập nhật task không tồn tại', async () => {
      // Arrange
      const taskId = 'invalid-id';
      const updateDto = new UpdateTaskDto();
      updateDto.taskName = 'Nhiệm vụ phân tích dữ liệu (đã cập nhật)';
      const employee = { id: '1' };

      jest.spyOn(service, 'update').mockRejectedValue(
        new AppException(ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND, 'Task không tồn tại')
      );

      // Act & Assert
      await expect(controller.update(taskId, updateDto, employee)).rejects.toThrow(AppException);
    });
  });

  describe('xóa task', () => {
    it('phải xóa task thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const employee = { id: '1' };

      jest.spyOn(service, 'remove').mockResolvedValue(undefined);

      // Act
      const result = await controller.remove(taskId, employee);

      // Assert
      expect(service.remove).toHaveBeenCalledWith(taskId, 1);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toBeNull();
      expect(result.message).toBe('Xóa task thành công');
    });

    it('phải xử lý lỗi khi xóa task không tồn tại', async () => {
      // Arrange
      const taskId = 'invalid-id';
      const employee = { id: '1' };

      jest.spyOn(service, 'remove').mockRejectedValue(
        new AppException(ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND, 'Task không tồn tại')
      );

      // Act & Assert
      await expect(controller.remove(taskId, employee)).rejects.toThrow(AppException);
    });
  });
});
