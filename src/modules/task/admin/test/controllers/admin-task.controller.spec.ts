import { Test, TestingModule } from '@nestjs/testing';

// Mock TaskStatus enum
enum TaskStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

// Mock AppException
class AppException extends Error {
  constructor(public code: string, message: string) {
    super(message);
  }
}

// Mock error codes
const ADMIN_TASK_ERROR_CODES = {
  ADMIN_TASK_NOT_FOUND: 'ADMIN_TASK_NOT_FOUND',
  TASK_NAME_REQUIRED: 'TASK_NAME_REQUIRED'
};

// Mock ApiResponseDto
class MockApiResponseDto {
  result: any;
  message: string;
  statusCode: number;

  constructor(result: any, message: string, statusCode: number = 200) {
    this.result = result;
    this.message = message;
    this.statusCode = statusCode;
  }

  static success(result: any, message: string = 'Success') {
    return new MockApiResponseDto(result, message, 200);
  }

  static created(result: any, message: string = 'Created') {
    return new MockApiResponseDto(result, message, 201);
  }

  static paginated(result: any, message: string = 'Success') {
    return new MockApiResponseDto(result, message, 200);
  }
}

// Mock classes
class MockAdminTaskController {
  constructor(private readonly adminTaskService: any) {}

  async findAll(queryDto: any, employee: any) {
    const result = await this.adminTaskService.findAll(queryDto);
    return MockApiResponseDto.paginated(result);
  }

  async findOne(taskId: string, employee: any) {
    const result = await this.adminTaskService.findOne(taskId);
    return MockApiResponseDto.success(result);
  }

  async create(createDto: any, employee: any) {
    const result = await this.adminTaskService.create(createDto, Number(employee.id));
    return MockApiResponseDto.created(result);
  }

  async update(taskId: string, updateDto: any, employee: any) {
    const result = await this.adminTaskService.update(taskId, updateDto, Number(employee.id));
    return MockApiResponseDto.success(result);
  }

  async remove(taskId: string, employee: any) {
    await this.adminTaskService.remove(taskId, Number(employee.id));
    return MockApiResponseDto.success(null, 'Xóa task thành công');
  }
}

class MockAdminTaskService {
  findAll = jest.fn();
  findOne = jest.fn();
  create = jest.fn();
  update = jest.fn();
  remove = jest.fn();
}

// Mock DTOs
class CreateTaskDto {
  taskName: string;
  taskDescription?: string;
  agentId?: string;
  active?: boolean;
}

class UpdateTaskDto {
  taskName?: string;
  taskDescription?: string;
  agentId?: string;
  active?: boolean;
}

class QueryTaskDto {
  page?: number;
  limit?: number;
  search?: string;
  agentId?: string;
  active?: boolean;
}

describe('Controller quản lý task (Admin)', () => {
  let controller: MockAdminTaskController;
  let service: MockAdminTaskService;

  // Mock data
  const mockTaskResponseDto = {
    taskId: '123e4567-e89b-12d3-a456-426614174000',
    taskName: 'Nhiệm vụ phân tích dữ liệu',
    taskDescription: 'Nhiệm vụ này sẽ phân tích dữ liệu từ nhiều nguồn khác nhau',
    status: TaskStatus.DRAFT,
    active: true,
    agentId: '123e4567-e89b-12d3-a456-426614174001',
    createdAt: 1625097600000,
    updatedAt: 1625097600000
  };

  const mockPaginatedTaskResponseDto = {
    items: [mockTaskResponseDto],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1
    }
  };

  beforeEach(() => {
    service = new MockAdminTaskService();
    controller = new MockAdminTaskController(service);
  });

  it('phải được định nghĩa', () => {
    expect(controller).toBeDefined();
  });

  describe('lấy danh sách task', () => {
    it('phải trả về danh sách task có phân trang', async () => {
      // Arrange
      const queryDto = new QueryTaskDto();
      queryDto.page = 1;
      queryDto.limit = 10;
      const employee = { id: '1' };

      jest.spyOn(service, 'findAll').mockResolvedValue(mockPaginatedTaskResponseDto);

      // Act
      const result = await controller.findAll(queryDto, employee);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(queryDto);
      expect(result.constructor.name).toBe('MockApiResponseDto');
      expect(result.result).toEqual(mockPaginatedTaskResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý các tham số tìm kiếm và lọc', async () => {
      // Arrange
      const queryDto = new QueryTaskDto();
      queryDto.page = 1;
      queryDto.limit = 10;
      queryDto.search = 'phân tích';
      queryDto.agentId = '123e4567-e89b-12d3-a456-426614174001';
      queryDto.active = true;
      const employee = { id: '1' };

      jest.spyOn(service, 'findAll').mockResolvedValue(mockPaginatedTaskResponseDto);

      // Act
      const result = await controller.findAll(queryDto, employee);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(expect.objectContaining({
        search: 'phân tích',
        agentId: '123e4567-e89b-12d3-a456-426614174001',
        active: true
      }));
      expect(result.result).toEqual(mockPaginatedTaskResponseDto);
    });
  });

  describe('lấy task theo ID', () => {
    it('phải trả về thông tin chi tiết task theo ID', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockResolvedValue(mockTaskResponseDto);

      // Act
      const result = await controller.findOne(taskId, employee);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith(taskId);
      expect(result.constructor.name).toBe('MockApiResponseDto');
      expect(result.result).toEqual(mockTaskResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi không tìm thấy task', async () => {
      // Arrange
      const taskId = 'invalid-id';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockRejectedValue(
        new AppException(ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND, 'Task không tồn tại')
      );

      // Act & Assert
      await expect(controller.findOne(taskId, employee)).rejects.toThrow(AppException);
    });
  });

  describe('tạo mới task', () => {
    it('phải tạo task mới thành công', async () => {
      // Arrange
      const createDto = new CreateTaskDto();
      createDto.taskName = 'Nhiệm vụ phân tích dữ liệu';
      createDto.taskDescription = 'Nhiệm vụ này sẽ phân tích dữ liệu từ nhiều nguồn khác nhau';
      createDto.agentId = '123e4567-e89b-12d3-a456-426614174001';
      createDto.active = true;
      const employee = { id: '1' };

      jest.spyOn(service, 'create').mockResolvedValue(mockTaskResponseDto);

      // Act
      const result = await controller.create(createDto, employee);

      // Assert
      expect(service.create).toHaveBeenCalledWith(createDto, 1);
      expect(result.constructor.name).toBe('MockApiResponseDto');
      expect(result.result).toEqual(mockTaskResponseDto);
      expect(result.message).toBe('Created');
    });

    it('phải xử lý lỗi khi tạo task với dữ liệu không hợp lệ', async () => {
      // Arrange
      const createDto = new CreateTaskDto();
      createDto.taskName = ''; // Tên không hợp lệ
      createDto.agentId = '123e4567-e89b-12d3-a456-426614174001';
      const employee = { id: '1' };

      jest.spyOn(service, 'create').mockRejectedValue(
        new AppException(ADMIN_TASK_ERROR_CODES.TASK_NAME_REQUIRED, 'Tên nhiệm vụ là bắt buộc')
      );

      // Act & Assert
      await expect(controller.create(createDto, employee)).rejects.toThrow(AppException);
    });
  });

  describe('cập nhật task', () => {
    it('phải cập nhật task thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const updateDto = new UpdateTaskDto();
      updateDto.taskName = 'Nhiệm vụ phân tích dữ liệu (đã cập nhật)';
      updateDto.active = false;
      const employee = { id: '1' };

      jest.spyOn(service, 'update').mockResolvedValue(mockTaskResponseDto);

      // Act
      const result = await controller.update(taskId, updateDto, employee);

      // Assert
      expect(service.update).toHaveBeenCalledWith(taskId, updateDto, 1);
      expect(result.constructor.name).toBe('MockApiResponseDto');
      expect(result.result).toEqual(mockTaskResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi cập nhật task không tồn tại', async () => {
      // Arrange
      const taskId = 'invalid-id';
      const updateDto = new UpdateTaskDto();
      updateDto.taskName = 'Nhiệm vụ phân tích dữ liệu (đã cập nhật)';
      const employee = { id: '1' };

      jest.spyOn(service, 'update').mockRejectedValue(
        new AppException(ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND, 'Task không tồn tại')
      );

      // Act & Assert
      await expect(controller.update(taskId, updateDto, employee)).rejects.toThrow(AppException);
    });
  });

  describe('xóa task', () => {
    it('phải xóa task thành công', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const employee = { id: '1' };

      jest.spyOn(service, 'remove').mockResolvedValue(undefined);

      // Act
      const result = await controller.remove(taskId, employee);

      // Assert
      expect(service.remove).toHaveBeenCalledWith(taskId, 1);
      expect(result.constructor.name).toBe('MockApiResponseDto');
      expect(result.result).toBeNull();
      expect(result.message).toBe('Xóa task thành công');
    });

    it('phải xử lý lỗi khi xóa task không tồn tại', async () => {
      // Arrange
      const taskId = 'invalid-id';
      const employee = { id: '1' };

      jest.spyOn(service, 'remove').mockRejectedValue(
        new AppException(ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND, 'Task không tồn tại')
      );

      // Act & Assert
      await expect(controller.remove(taskId, employee)).rejects.toThrow(AppException);
    });
  });
});
