import { AdminExecutionController } from '../../controllers/admin-execution.controller';
import { AdminExecutionService } from '../../services/admin-execution.service';
import { mockAdminExecutionService } from '../__mocks__/service.mock';
import { mockCompletedExecutionResponseDto, mockExecutionResponseDtos } from '../__mocks__/execution.mock';
import { QueryExecutionDto } from '@modules/task/admin/dto';
import { ApiResponseDto } from '@common/response';
import { AppException } from '@common/exceptions/app.exception';
import { ADMIN_EXECUTION_ERROR_CODES } from '@modules/task/admin/exceptions';
import { TaskExecutionStatus } from '@modules/task/enums/task-execution-status.enum';

describe('Controller quản lý lịch sử thực thi task (Admin)', () => {
  let controller: AdminExecutionController;
  let service: AdminExecutionService;

  beforeEach(() => {
    service = mockAdminExecutionService as AdminExecutionService;
    controller = new AdminExecutionController(service);
  });

  it('phải được định nghĩa', () => {
    expect(controller).toBeDefined();
  });

  describe('lấy danh sách lịch sử thực thi', () => {
    it('phải trả về danh sách lịch sử thực thi có phân trang', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const queryDto = { page: 1, limit: 10 } as QueryExecutionDto;
      const employee = { id: '1' };

      const paginatedResult = {
        items: mockExecutionResponseDtos,
        meta: {
          totalItems: mockExecutionResponseDtos.length,
          itemCount: mockExecutionResponseDtos.length,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1
        }
      };

      jest.spyOn(service, 'findAllByTaskId').mockResolvedValue(paginatedResult);

      // Act
      const result = await controller.findAll(taskId, queryDto, employee);

      // Assert
      expect(service.findAllByTaskId).toHaveBeenCalledWith(taskId, queryDto);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(paginatedResult);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý các tham số lọc theo trạng thái và thời gian', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const queryDto = {
        page: 1,
        limit: 10,
        status: TaskExecutionStatus.SUCCESS,
        startTimeFrom: 1625097600000,
        startTimeTo: 1625097900000
      } as QueryExecutionDto;
      const employee = { id: '1' };

      const paginatedResult = {
        items: [mockExecutionResponseDtos[0]],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1
        }
      };

      jest.spyOn(service, 'findAllByTaskId').mockResolvedValue(paginatedResult);

      // Act
      const result = await controller.findAll(taskId, queryDto, employee);

      // Assert
      expect(service.findAllByTaskId).toHaveBeenCalledWith(taskId, queryDto);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(paginatedResult);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi lấy danh sách lịch sử thực thi', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const queryDto = { page: 1, limit: 10 } as QueryExecutionDto;
      const employee = { id: '1' };

      jest.spyOn(service, 'findAllByTaskId').mockRejectedValue(
        new AppException(
          ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_FETCH_FAILED,
          'Lỗi khi lấy danh sách lịch sử thực thi'
        )
      );

      // Act & Assert
      await expect(controller.findAll(taskId, queryDto, employee)).rejects.toThrow(AppException);
    });
  });

  describe('lấy thông tin chi tiết lần thực thi', () => {
    it('phải trả về thông tin chi tiết của lần thực thi', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const executionId = '123e4567-e89b-12d3-a456-426614174030';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockResolvedValue(mockCompletedExecutionResponseDto);

      // Act
      const result = await controller.findOne(taskId, executionId, employee);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith(taskId, executionId);
      expect(result.constructor.name).toBe('ApiResponseDto');
      expect(result.result).toEqual(mockCompletedExecutionResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi lần thực thi không tồn tại', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const executionId = 'non-existent-id';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockRejectedValue(
        new AppException(
          ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_NOT_FOUND,
          'Không tìm thấy phiên thực thi'
        )
      );

      // Act & Assert
      await expect(controller.findOne(taskId, executionId, employee)).rejects.toThrow(AppException);
    });

    it('phải xử lý lỗi khi lấy thông tin chi tiết lần thực thi', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const executionId = '123e4567-e89b-12d3-a456-426614174030';
      const employee = { id: '1' };

      jest.spyOn(service, 'findOne').mockRejectedValue(
        new AppException(
          ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_FETCH_FAILED,
          'Lỗi khi lấy thông tin chi tiết lần thực thi'
        )
      );

      // Act & Assert
      await expect(controller.findOne(taskId, executionId, employee)).rejects.toThrow(AppException);
    });
  });
});
