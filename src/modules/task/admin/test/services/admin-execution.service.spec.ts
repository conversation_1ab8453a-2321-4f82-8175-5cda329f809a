import { Test, TestingModule } from '@nestjs/testing';
import { AdminExecutionService } from '@modules/task/admin/services';
import { AdminTaskExecutionRepository, AdminTaskRepository } from '@modules/task/repositories';
import { ValidationHelper } from '@modules/task/admin/helpers';
import { QueryExecutionDto, ExecutionResponseDto, ExecutionDetailResponseDto } from '../__mocks__/@modules/task/admin/dto';
import { AppException } from '@common/exceptions/app.exception';
import { ErrorCode } from '@common/exceptions/app.exception';
import { ADMIN_EXECUTION_ERROR_CODES } from '../__mocks__/@modules/task/exceptions';
import { TaskExecutionStatus } from '@modules/task/enums/task-execution-status.enum';
import { mockCompletedExecution, mockExecutions } from '../__mocks__/execution.mock';
import { mockTask } from '../__mocks__/task.mock';

describe('AdminExecutionService', () => {
  let service: AdminExecutionService;
  let adminTaskExecutionRepository: AdminTaskExecutionRepository;
  let adminTaskRepository: AdminTaskRepository;
  let validationHelper: ValidationHelper;

  const mockTaskExecutionRepository = {
    findExecutionById: jest.fn(),
    findExecutionsWithPagination: jest.fn(),
  };

  const mockTaskRepository = {
    findTaskById: jest.fn(),
  };

  const mockValidationHelper = {
    validateTaskExists: jest.fn(),
    validateExecutionExists: jest.fn(),
    validateExecutionBelongsToTask: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminExecutionService,
        {
          provide: AdminTaskExecutionRepository,
          useValue: mockTaskExecutionRepository,
        },
        {
          provide: AdminTaskRepository,
          useValue: mockTaskRepository,
        },
        {
          provide: ValidationHelper,
          useValue: mockValidationHelper,
        },
      ],
    }).compile();

    service = module.get<AdminExecutionService>(AdminExecutionService);
    adminTaskExecutionRepository = module.get<AdminTaskExecutionRepository>(AdminTaskExecutionRepository);
    adminTaskRepository = module.get<AdminTaskRepository>(AdminTaskRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('phải được định nghĩa', () => {
    expect(service).toBeDefined();
  });

  describe('findAllByTaskId', () => {
    it('phải trả về danh sách lịch sử thực thi với phân trang', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const queryDto = new QueryExecutionDto();
      queryDto.page = 1;
      queryDto.limit = 10;


      jest.spyOn(adminTaskRepository, 'findTaskById').mockResolvedValue(mockTask);
      jest.spyOn(validationHelper, 'validateTaskExists').mockImplementation(() => {});
      jest.spyOn(adminTaskExecutionRepository, 'findExecutionsWithPagination').mockResolvedValue([
        mockExecutions,
        mockExecutions.length,
      ]);

      // Act
      const result = await service.findAllByTaskId(taskId, queryDto);

      // Assert
      expect(adminTaskRepository.findTaskById).toHaveBeenCalledWith(taskId);
      expect(validationHelper.validateTaskExists).toHaveBeenCalledWith(mockTask);
      expect(adminTaskExecutionRepository.findExecutionsWithPagination).toHaveBeenCalledWith(
        taskId,
        queryDto.page,
        queryDto.limit,
        'startTime',
        'DESC',
        undefined,
        undefined,
        undefined
      );
      expect(result.items).toHaveLength(mockExecutions.length);
      expect(result.meta.totalItems).toBe(mockExecutions.length);
    });

    it('phải áp dụng các bộ lọc khi truy vấn', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const queryDto = new QueryExecutionDto();
      queryDto.page = 1;
      queryDto.limit = 10;
      queryDto.status = TaskExecutionStatus.SUCCESS;
      queryDto.startTimeFrom = 1625097600000;
      queryDto.startTimeTo = 1625097900000;


      jest.spyOn(adminTaskRepository, 'findTaskById').mockResolvedValue(mockTask);
      jest.spyOn(validationHelper, 'validateTaskExists').mockImplementation(() => {});
      jest.spyOn(adminTaskExecutionRepository, 'findExecutionsWithPagination').mockResolvedValue([
        [mockExecutions[0]],
        1,
      ]);

      // Act
      const result = await service.findAllByTaskId(taskId, queryDto);

      // Assert
      expect(adminTaskExecutionRepository.findExecutionsWithPagination).toHaveBeenCalledWith(
        taskId,
        queryDto.page,
        queryDto.limit,
        'startTime',
        'DESC',
        TaskExecutionStatus.SUCCESS,
        1625097600000,
        1625097900000
      );
      expect(result.items).toHaveLength(1);
      expect(result.meta.totalItems).toBe(1);
    });

    it('phải xử lý lỗi khi task không tồn tại', async () => {
      // Arrange
      const taskId = 'non-existent-id';
      const queryDto = new QueryExecutionDto();
      queryDto.page = 1;
      queryDto.limit = 10;

      jest.spyOn(adminTaskRepository, 'findTaskById').mockResolvedValue(null);
      jest.spyOn(validationHelper, 'validateTaskExists').mockImplementation(() => {
        throw new Error('Task không tồn tại');
      });

      // Act & Assert
      await expect(service.findAllByTaskId(taskId, queryDto)).rejects.toThrow('Task không tồn tại');
    });

    it('phải xử lý lỗi khi truy vấn cơ sở dữ liệu thất bại', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const queryDto = new QueryExecutionDto();
      queryDto.page = 1;
      queryDto.limit = 10;

      jest.spyOn(adminTaskRepository, 'findTaskById').mockResolvedValue(mockTask);
      jest.spyOn(validationHelper, 'validateTaskExists').mockImplementation(() => {});
      jest.spyOn(adminTaskExecutionRepository, 'findExecutionsWithPagination').mockRejectedValue(
        new Error('Database error')
      );

      // Act & Assert
      await expect(service.findAllByTaskId(taskId, queryDto)).rejects.toThrow();
    });
  });

  describe('findOne', () => {
    it('phải trả về thông tin chi tiết của một lần thực thi', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const executionId = '123e4567-e89b-12d3-a456-426614174030';

      jest.spyOn(adminTaskRepository, 'findTaskById').mockResolvedValue(mockTask);
      jest.spyOn(validationHelper, 'validateTaskExists').mockImplementation(() => {});
      jest.spyOn(adminTaskExecutionRepository, 'findExecutionById').mockResolvedValue(mockCompletedExecution);
      jest.spyOn(validationHelper, 'validateExecutionExists').mockImplementation(() => {});
      jest.spyOn(validationHelper, 'validateExecutionBelongsToTask').mockImplementation(() => {});

      // Act
      const result = await service.findOne(taskId, executionId);

      // Assert
      expect(adminTaskRepository.findTaskById).toHaveBeenCalledWith(taskId);
      expect(validationHelper.validateTaskExists).toHaveBeenCalledWith(mockTask);
      expect(adminTaskExecutionRepository.findExecutionById).toHaveBeenCalledWith(executionId);
      expect(validationHelper.validateExecutionExists).toHaveBeenCalledWith(mockCompletedExecution);
      expect(validationHelper.validateExecutionBelongsToTask).toHaveBeenCalledWith(mockCompletedExecution, taskId);
      expect(result).toBeDefined();
      expect(result.taskExecutionId).toBe(executionId);
    });

    it('phải xử lý lỗi khi task không tồn tại', async () => {
      // Arrange
      const taskId = 'non-existent-id';
      const executionId = '123e4567-e89b-12d3-a456-426614174030';

      jest.spyOn(adminTaskRepository, 'findTaskById').mockResolvedValue(null);
      jest.spyOn(validationHelper, 'validateTaskExists').mockImplementation(() => {
        throw new Error('Task không tồn tại');
      });

      // Act & Assert
      await expect(service.findOne(taskId, executionId)).rejects.toThrow('Task không tồn tại');
    });

    it('phải xử lý lỗi khi lần thực thi không tồn tại', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const executionId = 'non-existent-id';

      jest.spyOn(adminTaskRepository, 'findTaskById').mockResolvedValue(mockTask);
      jest.spyOn(validationHelper, 'validateTaskExists').mockImplementation(() => {});
      jest.spyOn(adminTaskExecutionRepository, 'findExecutionById').mockResolvedValue(null);
      jest.spyOn(validationHelper, 'validateExecutionExists').mockImplementation(() => {
        throw new Error('Không tìm thấy phiên thực thi');
      });

      // Act & Assert
      await expect(service.findOne(taskId, executionId)).rejects.toThrow('Không tìm thấy phiên thực thi');
    });

    it('phải xử lý lỗi khi lần thực thi không thuộc task', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const executionId = '123e4567-e89b-12d3-a456-426614174030';
      const executionWithDifferentTaskId = {
        ...mockCompletedExecution,
        taskId: 'different-task-id',
      };

      jest.spyOn(adminTaskRepository, 'findTaskById').mockResolvedValue(mockTask);
      jest.spyOn(validationHelper, 'validateTaskExists').mockImplementation(() => {});
      jest.spyOn(adminTaskExecutionRepository, 'findExecutionById').mockResolvedValue(executionWithDifferentTaskId);
      jest.spyOn(validationHelper, 'validateExecutionExists').mockImplementation(() => {});
      jest.spyOn(validationHelper, 'validateExecutionBelongsToTask').mockImplementation(() => {
        throw new Error('Lần thực thi không thuộc task này');
      });

      // Act & Assert
      await expect(service.findOne(taskId, executionId)).rejects.toThrow('Lần thực thi không thuộc task này');
    });
  });
});
