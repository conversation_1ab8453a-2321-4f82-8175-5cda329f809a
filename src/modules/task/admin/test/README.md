# Tests cho Module Task Admin

## C<PERSON>u trúc thư mục

```
test/
├── __mocks__/                # Mock data và services
│   ├── task.mock.ts          # Mock data cho task
│   ├── step.mock.ts          # Mock data cho step
│   ├── connection.mock.ts    # Mock data cho connection
│   ├── execution.mock.ts     # Mock data cho execution
│   ├── repository.mock.ts    # Mock cho Repository
│   └── service.mock.ts       # Mock cho Service
├── controllers/              # Tests cho controllers
│   ├── admin-task.controller.spec.ts     # Test cho AdminTaskController
│   ├── admin-step.controller.spec.ts     # Test cho AdminStepController
│   ├── admin-connection.controller.spec.ts # Test cho AdminConnectionController
│   └── admin-execution.controller.spec.ts # Test cho AdminExecutionController
├── services/                 # Tests cho services
│   ├── admin-task.service.spec.ts        # Test cho AdminTaskService
│   ├── admin-step.service.spec.ts        # Test cho AdminStepService
│   ├── admin-connection.service.spec.ts  # Test cho AdminConnectionService
│   └── admin-execution.service.spec.ts   # Test cho AdminExecutionService
├── dto/                      # Tests cho DTOs
│   ├── task/                 # Tests cho DTOs liên quan đến task
│   ├── step/                 # Tests cho DTOs liên quan đến step
│   ├── connection/           # Tests cho DTOs liên quan đến connection
│   └── execution/            # Tests cho DTOs liên quan đến execution
├── jest.config.ts            # Cấu hình Jest cho module task admin
├── run-tests.js              # Script chạy test
└── README.md                 # Tài liệu hướng dẫn
```

## Cách chạy tests

```bash
# Chạy tất cả tests
npm run test:task-admin

# Chạy test cho một file cụ thể
npx jest src/modules/task/admin/test/controllers/admin-task.controller.spec.ts --config=src/modules/task/admin/test/jest.config.ts

# Chạy test với coverage
npx jest --config=src/modules/task/admin/test/jest.config.ts --coverage

# Sử dụng script run-tests.js
node src/modules/task/admin/test/run-tests.js                  # Chạy tất cả các test
node src/modules/task/admin/test/run-tests.js --coverage       # Chạy tất cả các test với coverage
node src/modules/task/admin/test/run-tests.js --dir controllers # Chạy tất cả các test trong thư mục controllers
node src/modules/task/admin/test/run-tests.js --file controllers/admin-task.controller.spec.ts # Chạy một file test cụ thể
```

## Quy ước viết test

1. Mỗi controller và service cần có ít nhất một file test tương ứng
2. Sử dụng mock cho các dependencies để đảm bảo unit test độc lập
3. Đảm bảo test coverage cho các trường hợp thành công và thất bại
4. Sử dụng các mocks trong thư mục `__mocks__` để đảm bảo tính nhất quán

## Danh sách các test

### Controllers
- **AdminTaskController**: Test các API liên quan đến task
  - `findAll`: Lấy danh sách task
  - `findOne`: Lấy thông tin chi tiết task
  - `create`: Tạo mới task
  - `update`: Cập nhật task
  - `remove`: Xóa task

- **AdminStepController**: Test các API liên quan đến step
  - `findAll`: Lấy danh sách step của task
  - `findOne`: Lấy thông tin chi tiết step
  - `create`: Tạo mới step
  - `update`: Cập nhật step
  - `remove`: Xóa step
  - `reorder`: Sắp xếp lại thứ tự các step

- **AdminConnectionController**: Test các API liên quan đến connection
  - `findAll`: Lấy danh sách connection của task
  - `findOne`: Lấy thông tin chi tiết connection
  - `create`: Tạo mới connection
  - `update`: Cập nhật connection
  - `remove`: Xóa connection

- **AdminExecutionController**: Test các API liên quan đến execution
  - `findAll`: Lấy danh sách execution của task
  - `findOne`: Lấy thông tin chi tiết execution
  - `create`: Tạo mới execution
  - `update`: Cập nhật execution

### Services
- **AdminTaskService**: Test các phương thức xử lý logic task
  - `findAll`: Lấy danh sách task
  - `findOne`: Lấy thông tin chi tiết task
  - `create`: Tạo mới task
  - `update`: Cập nhật task
  - `remove`: Xóa task

- **AdminStepService**: Test các phương thức xử lý logic step
  - `findAllByTaskId`: Lấy danh sách step của task
  - `findOne`: Lấy thông tin chi tiết step
  - `create`: Tạo mới step
  - `update`: Cập nhật step
  - `remove`: Xóa step
  - `reorder`: Sắp xếp lại thứ tự các step

- **AdminConnectionService**: Test các phương thức xử lý logic connection
  - `findAllByTaskId`: Lấy danh sách connection của task
  - `findOne`: Lấy thông tin chi tiết connection
  - `create`: Tạo mới connection
  - `update`: Cập nhật connection
  - `remove`: Xóa connection

- **AdminExecutionService**: Test các phương thức xử lý logic execution
  - `findAllByTaskId`: Lấy danh sách execution của task
  - `findOne`: Lấy thông tin chi tiết execution
  - `create`: Tạo mới execution
  - `update`: Cập nhật execution

### DTOs
- **Task DTOs**: Test các DTO liên quan đến task
  - `CreateTaskDto`: DTO cho việc tạo mới task
  - `UpdateTaskDto`: DTO cho việc cập nhật task
  - `QueryTaskDto`: DTO cho việc truy vấn danh sách task
  - `TaskResponseDto`: DTO cho việc trả về thông tin task

- **Step DTOs**: Test các DTO liên quan đến step
  - `CreateStepDto`: DTO cho việc tạo mới step
  - `UpdateStepDto`: DTO cho việc cập nhật step
  - `ReorderStepsDto`: DTO cho việc sắp xếp lại thứ tự các step
  - `StepResponseDto`: DTO cho việc trả về thông tin step

- **Connection DTOs**: Test các DTO liên quan đến connection
  - `CreateConnectionDto`: DTO cho việc tạo mới connection
  - `UpdateConnectionDto`: DTO cho việc cập nhật connection
  - `ConnectionResponseDto`: DTO cho việc trả về thông tin connection

- **Execution DTOs**: Test các DTO liên quan đến execution
  - `CreateExecutionDto`: DTO cho việc tạo mới execution
  - `UpdateExecutionDto`: DTO cho việc cập nhật execution
  - `ExecutionResponseDto`: DTO cho việc trả về thông tin execution
