module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../../../..',
  testRegex: 'src/modules/task/admin/test/.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['src/modules/task/admin/**/*.(t|j)s'],
  coverageDirectory: './coverage/task-admin',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@modules/(.*)$': '<rootDir>/src/modules/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
    '^@common/(.*)$': '<rootDir>/src/common/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@database/(.*)$': '<rootDir>/src/database/$1',
    '^@config$': '<rootDir>/src/config',
    '^@dto/(.*)$': '<rootDir>/src/common/dto/$1',
    '^typeorm-transactional$': '<rootDir>/src/modules/task/admin/test/__mocks__/typeorm-transactional.ts',
    '^@modules/task/exceptions$': '<rootDir>/src/modules/task/admin/test/__mocks__/@modules/task/exceptions/index.ts',
    '^@common/exceptions$': '<rootDir>/src/modules/task/admin/test/__mocks__/@common/exceptions/index.ts',
    '^@common/error/api-error-response-with-examples.decorator$': '<rootDir>/src/modules/task/admin/test/__mocks__/@common/error/api-error-response-with-examples.decorator.ts',
    '^@common/response/api-error-response.dto$': '<rootDir>/src/modules/task/admin/test/__mocks__/@common/response/api-error-response.dto.ts',
    '^@common/swagger$': '<rootDir>/src/modules/task/admin/test/__mocks__/@common/swagger/index.ts',
    '^@common/response/api-response-dto$': '<rootDir>/src/modules/task/admin/test/__mocks__/@common/response/api-response-dto.ts',
    '^@/modules/auth/guards$': '<rootDir>/src/modules/task/admin/test/__mocks__/@modules/auth/guards/index.ts',
    '^@modules/auth/guards/permissions.guard$': '<rootDir>/src/modules/task/admin/test/__mocks__/@modules/auth/guards/permissions.guard.ts',
    '^@/modules/auth/decorators/current-employee.decorator$': '<rootDir>/src/modules/task/admin/test/__mocks__/@modules/auth/decorators/current-employee.decorator.ts',
    '^@modules/auth/decorators/roles.decorator$': '<rootDir>/src/modules/task/admin/test/__mocks__/@modules/auth/decorators/roles.decorator.ts',
    '^@modules/task/admin/dto$': '<rootDir>/src/modules/task/admin/test/__mocks__/@modules/task/admin/dto/index.ts',
    '^@modules/task/admin/controllers$': '<rootDir>/src/modules/task/admin/test/__mocks__/@modules/task/admin/controllers/index.ts',
    '^@modules/task/interfaces/step-config.interface$': '<rootDir>/src/modules/task/admin/test/__mocks__/@modules/task/interfaces/step-config.interface.ts',
    '^@modules/task/interfaces/task-execution-status.enum$': '<rootDir>/src/modules/task/admin/test/__mocks__/@modules/task/interfaces/task-execution-status.enum.ts',
    '^@common/dto/query.dto$': '<rootDir>/src/modules/task/admin/test/__mocks__/@common/dto/query.dto.ts',
    '^@modules/employee/dto/employee-info.dto$': '<rootDir>/src/modules/task/admin/test/__mocks__/@modules/employee/dto/employee-info.dto.ts',
  },
  setupFilesAfterEnv: ['<rootDir>/src/modules/task/admin/test/jest.setup.ts'],
  testTimeout: 30000,
  maxWorkers: 1,
  globals: {
    'ts-jest': {
      isolatedModules: true,
    },
  },
};
