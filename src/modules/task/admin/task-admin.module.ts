import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AdminTask,
  AdminStep,
  AdminTaskExecution,
  AdminStepConnection,
} from '../entities';
import {
  AdminTaskRepository,
  AdminStepRepository,
  AdminTaskExecutionRepository,
  AdminStepConnectionRepository,
} from '../repositories';
import {
  AdminTaskController,
  AdminStepController,
  AdminConnectionController,
  AdminExecutionController
} from './controllers';
import {
  AdminTaskService,
  AdminStepService,
  AdminConnectionService,
  AdminExecutionService
} from './services';
import { ValidationHelper } from './helpers';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { Agent } from '@modules/agent/entities/agent.entity';
import { Employee } from '@modules/employee/entities/employee.entity';
import { EmployeeModule } from '@modules/employee/employee.module';
import { ServicesModule } from '@shared/services/services.module';

/**
 * Module quản lý task cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      AdminTask,
      AdminStep,
      AdminTaskExecution,
      AdminStepConnection,
      Agent,
      Employee
    ]),
    EmployeeModule,
    ServicesModule
  ],
  controllers: [
    AdminTaskController,
    AdminStepController,
    AdminConnectionController,
    AdminExecutionController
  ],
  providers: [
    // Repositories
    AdminTaskRepository,
    AdminStepRepository,
    AdminTaskExecutionRepository,
    AdminStepConnectionRepository,
    AgentRepository,

    // Helpers
    ValidationHelper,

    // Services
    AdminTaskService,
    AdminStepService,
    AdminConnectionService,
    AdminExecutionService
  ],
  exports: [
    AdminTaskService,
    AdminStepService,
    AdminConnectionService,
    AdminExecutionService
  ],
})
export class TaskAdminModule {}
