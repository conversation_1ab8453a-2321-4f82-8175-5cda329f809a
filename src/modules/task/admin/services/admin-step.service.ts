import { Injectable, Logger } from '@nestjs/common';
import { AdminStepRepository, AdminTaskRepository } from '../../repositories';
import { AppException } from '@common/exceptions/app.exception';
import { plainToInstance } from 'class-transformer';
import { Transactional } from 'typeorm-transactional';
import { ValidationHelper } from '@modules/task/admin/helpers';
import { ADMIN_STEP_ERROR_CODES } from '../exceptions';
import {
  CreateStepDto,
  UpdateStepDto,
  ReorderStepsDto,
  StepResponseDto
} from '../dto';

/**
 * Service xử lý logic liên quan đến các bước trong task của admin
 */
@Injectable()
export class AdminStepService {
  /**
   * Logger cho AdminStepService
   */
  private readonly logger = new Logger(AdminStepService.name);

  /**
   * Constructor
   * @param adminStepRepository Repository xử lý dữ liệu các bước trong task của admin
   * @param adminTaskRepository Repository xử lý dữ liệu task của admin
   * @param validationHelper Helper xử lý validation
   */
  constructor(
    private readonly adminStepRepository: AdminStepRepository,
    private readonly adminTaskRepository: AdminTaskRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Lấy danh sách các bước của một task
   * @param taskId ID của task
   * @returns Danh sách các bước
   */
  async findAllByTaskId(taskId: string): Promise<StepResponseDto[]> {
    try {
      this.logger.log(`Lấy danh sách các bước của task với ID: ${taskId}`);

      const task = await this.adminTaskRepository.findTaskById(taskId);
      this.validationHelper.validateTaskExists(task);

      const steps = await this.adminStepRepository.findStepsByTaskId(taskId);

      return plainToInstance(StepResponseDto, steps);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách các bước: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_FETCH_FAILED,
        'Lỗi khi lấy danh sách các bước'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của một bước
   * @param stepId ID của bước
   * @returns Thông tin chi tiết của bước
   */
  async findOne(stepId: string): Promise<StepResponseDto> {
    try {
      this.logger.log(`Lấy thông tin chi tiết bước với ID: ${stepId}`);

      const step = await this.adminStepRepository.findStepById(stepId);
      this.validationHelper.validateStepExists(step);

      return plainToInstance(StepResponseDto, step);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết bước: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_FETCH_FAILED,
        'Lỗi khi lấy thông tin chi tiết bước'
      );
    }
  }

  /**
   * Tạo mới bước cho task
   * @param taskId ID của task
   * @param createStepDto Dữ liệu tạo bước
   * @returns Thông tin bước đã tạo
   */
  @Transactional()
  async create(taskId: string, createStepDto: CreateStepDto): Promise<StepResponseDto> {
    try {
      this.logger.log(`Tạo mới bước cho task với ID: ${taskId}, dữ liệu: ${JSON.stringify(createStepDto)}`);

      const task = await this.adminTaskRepository.findTaskById(taskId);
      this.validationHelper.validateTaskExists(task);

      const { stepName, stepDescription, stepType, stepConfig } = createStepDto;

      await this.validationHelper.validateStepCreation(stepName, stepType, taskId, stepConfig);

      // Xác định orderIndex mới (lớn nhất hiện tại + 1)
      const maxOrderIndex = await this.adminStepRepository.getMaxOrderIndex(taskId);
      const orderIndex = maxOrderIndex + 1;

      const newStep = await this.adminStepRepository.createStep({
        taskId,
        stepName,
        stepDescription,
        stepType,
        stepConfig,
        orderIndex
      });

      return plainToInstance(StepResponseDto, newStep);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo mới bước: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_CREATION_FAILED,
        'Lỗi khi tạo mới bước'
      );
    }
  }

  /**
   * Cập nhật thông tin bước
   * @param taskId ID của task
   * @param stepId ID của bước
   * @param updateStepDto Dữ liệu cập nhật
   * @returns Thông tin bước đã cập nhật
   */
  @Transactional()
  async update(taskId: string, stepId: string, updateStepDto: UpdateStepDto): Promise<StepResponseDto> {
    try {
      this.logger.log(`Cập nhật bước với ID: ${stepId}, dữ liệu: ${JSON.stringify(updateStepDto)}`);

      const task = await this.adminTaskRepository.findTaskById(taskId);
      this.validationHelper.validateTaskExists(task);

      const step = await this.adminStepRepository.findStepById(stepId);
      this.validationHelper.validateStepExists(step);

      // Kiểm tra bước có thuộc task không
      // Ensure step is not null before accessing its properties
      if (!step) {
        throw new AppException(
          ADMIN_STEP_ERROR_CODES.ADMIN_STEP_NOT_FOUND,
          'Bước không tồn tại'
        );
      }

      if (step.taskId !== taskId) {
        throw new AppException(
          ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
          'Bước không thuộc task này'
        );
      }

      // Kiểm tra dữ liệu cập nhật
      if (updateStepDto.stepName && updateStepDto.stepType) {
        await this.validationHelper.validateStepCreation(updateStepDto.stepName, updateStepDto.stepType, taskId, updateStepDto.stepConfig);
      } else if (updateStepDto.stepName && step && step.stepType) {
        await this.validationHelper.validateStepCreation(updateStepDto.stepName, step.stepType, taskId);
      } else if (step && step.stepName && updateStepDto.stepType) {
        await this.validationHelper.validateStepCreation(step.stepName, updateStepDto.stepType, taskId, updateStepDto.stepConfig);
      }

      // Kiểm tra cấu hình bước và tính tương thích với stepType
      if (updateStepDto.stepConfig) {
        const stepType = updateStepDto.stepType || (step ? step.stepType : undefined);
        if (!stepType) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Loại bước không hợp lệ'
          );
        }

        // Kiểm tra tính tương thích giữa stepType và stepConfig
        await this.validationHelper.validateStepTypeAndConfigCompatibility(stepType, updateStepDto.stepConfig);
      }

      const updatedStep = await this.adminStepRepository.updateStep(stepId, {
        ...updateStepDto,
        updatedAt: Date.now()
      });

      return plainToInstance(StepResponseDto, updatedStep);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật bước: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_UPDATE_FAILED,
        'Lỗi khi cập nhật bước'
      );
    }
  }

  /**
   * Sắp xếp lại thứ tự các bước
   * @param taskId ID của task
   * @param reorderStepsDto Dữ liệu sắp xếp lại
   * @returns Danh sách các bước đã sắp xếp lại
   */
  @Transactional()
  async reorder(taskId: string, reorderStepsDto: ReorderStepsDto): Promise<StepResponseDto[]> {
    try {
      this.logger.log(`Sắp xếp lại thứ tự các bước của task với ID: ${taskId}, dữ liệu: ${JSON.stringify(reorderStepsDto)}`);

      const task = await this.adminTaskRepository.findTaskById(taskId);
      this.validationHelper.validateTaskExists(task);

      const { stepIds } = reorderStepsDto;

      // Kiểm tra tất cả các bước có tồn tại và thuộc task không
      const steps = await this.adminStepRepository.findStepsByIds(stepIds);

      if (steps.length !== stepIds.length) {
        throw new AppException(
          ADMIN_STEP_ERROR_CODES.ADMIN_STEP_NOT_FOUND,
          'Một hoặc nhiều bước không tồn tại'
        );
      }

      for (const step of steps) {
        if (step.taskId !== taskId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Một hoặc nhiều bước không thuộc task này'
          );
        }
      }

      // Cập nhật thứ tự các bước
      const updatedSteps = await this.adminStepRepository.updateStepsOrder(stepIds);

      return plainToInstance(StepResponseDto, updatedSteps);
    } catch (error) {
      this.logger.error(`Lỗi khi sắp xếp lại thứ tự các bước: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }

      // Cung cấp thông báo lỗi chi tiết hơn
      let errorMessage = 'Lỗi khi sắp xếp lại thứ tự các bước';

      if (error.message) {
        // Nếu là lỗi unique constraint, đưa ra thông báo dễ hiểu hơn
        if (error.message.includes('unique_admin_task_order')) {
          errorMessage = 'Lỗi khi sắp xếp lại thứ tự các bước: Xung đột thứ tự các bước. Vui lòng thử lại sau.';
        } else {
          errorMessage = `Lỗi khi sắp xếp lại thứ tự các bước: ${error.message}`;
        }
      }

      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_REORDER_FAILED,
        errorMessage
      );
    }
  }

  /**
   * Xóa bước
   * @param taskId ID của task
   * @param stepId ID của bước
   */
  @Transactional()
  async remove(taskId: string, stepId: string): Promise<void> {
    try {
      this.logger.log(`Xóa bước với ID: ${stepId} của task với ID: ${taskId}`);

      const task = await this.adminTaskRepository.findTaskById(taskId);
      this.validationHelper.validateTaskExists(task);

      const step = await this.adminStepRepository.findStepById(stepId);
      this.validationHelper.validateStepExists(step);

      // Kiểm tra bước có thuộc task không
      // Ensure step is not null before accessing its properties
      if (!step) {
        throw new AppException(
          ADMIN_STEP_ERROR_CODES.ADMIN_STEP_NOT_FOUND,
          'Bước không tồn tại'
        );
      }

      if (step.taskId !== taskId) {
        throw new AppException(
          ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
          'Bước không thuộc task này'
        );
      }

      // Xóa bước
      await this.adminStepRepository.deleteStep(stepId);

      // Cập nhật lại thứ tự các bước còn lại
      if (step) {
        await this.adminStepRepository.reorderStepsAfterDelete(taskId, step.orderIndex);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa bước: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_DELETE_FAILED,
        'Lỗi khi xóa bước'
      );
    }
  }
}
