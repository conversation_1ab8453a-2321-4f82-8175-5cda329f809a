import { Injectable, Logger } from '@nestjs/common';
import { AdminStepConnectionRepository, AdminTaskRepository, AdminStepRepository } from '../../repositories';
import { AppException } from '@common/exceptions/app.exception';
import { plainToInstance } from 'class-transformer';
import { Transactional } from 'typeorm-transactional';
import { ValidationHelper } from '@modules/task/admin/helpers';
import { ADMIN_CONNECTION_ERROR_CODES } from '../exceptions';
import {
  CreateConnectionDto,
  UpdateConnectionDto,
  ConnectionResponseDto
} from '../dto';

/**
 * Service xử lý logic liên quan đến kết nối giữa các bước trong task của admin
 */
@Injectable()
export class AdminConnectionService {
  /**
   * Logger cho AdminConnectionService
   */
  private readonly logger = new Logger(AdminConnectionService.name);

  /**
   * Constructor
   * @param adminStepConnectionRepository Repository xử lý dữ liệu kết nối giữa các bước trong task của admin
   * @param adminTaskRepository Repository xử lý dữ liệu task của admin
   * @param adminStepRepository Repository xử lý dữ liệu các bước trong task của admin
   * @param validationHelper Helper xử lý validation
   */
  constructor(
    private readonly adminStepConnectionRepository: AdminStepConnectionRepository,
    private readonly adminTaskRepository: AdminTaskRepository,
    private readonly adminStepRepository: AdminStepRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Lấy danh sách kết nối của một task
   * @param taskId ID của task
   * @returns Danh sách kết nối
   */
  async findAllByTaskId(taskId: string): Promise<ConnectionResponseDto[]> {
    this.logger.log(`Lấy danh sách kết nối của task với ID: ${taskId}`);

    // Business validation
    const task = await this.adminTaskRepository.findTaskById(taskId);
    this.validationHelper.validateTaskExists(task);

    try {
      // Main logic
      const connections = await this.adminStepConnectionRepository.findConnectionsByTaskId(taskId);
      return plainToInstance(ConnectionResponseDto, connections);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách kết nối: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_FETCH_FAILED,
        'Lỗi khi lấy danh sách kết nối'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết kết nối
   * @param connectionId ID của kết nối
   * @returns Thông tin chi tiết kết nối
   */
  async findOne(connectionId: string): Promise<ConnectionResponseDto> {
    this.logger.log(`Lấy thông tin chi tiết kết nối với ID: ${connectionId}`);

    // Business validation
    const connection = await this.adminStepConnectionRepository.findConnectionById(connectionId);
    this.validationHelper.validateConnectionExists(connection);

    try {
      // Main logic
      return plainToInstance(ConnectionResponseDto, connection);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết kết nối: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_FETCH_FAILED,
        'Lỗi khi lấy thông tin chi tiết kết nối'
      );
    }
  }

  /**
   * Tạo mới kết nối giữa các bước
   * @param taskId ID của task
   * @param createConnectionDto Dữ liệu tạo kết nối
   * @returns Thông tin kết nối đã tạo
   */
  @Transactional()
  async create(taskId: string, createConnectionDto: CreateConnectionDto): Promise<ConnectionResponseDto> {
    this.logger.log(`Tạo mới kết nối cho task với ID: ${taskId}, dữ liệu: ${JSON.stringify(createConnectionDto)}`);

    // Business validation
    const task = await this.adminTaskRepository.findTaskById(taskId);
    this.validationHelper.validateTaskExists(task);

    const { fromStepId, toStepId, outputField, inputField } = createConnectionDto;

    // Kiểm tra dữ liệu đầu vào
    this.validationHelper.validateConnectionCreation(fromStepId, toStepId, outputField, inputField);

    // Kiểm tra các bước có tồn tại và thuộc task không
    const fromStep = await this.adminStepRepository.findStepById(fromStepId);
    const toStep = await this.adminStepRepository.findStepById(toStepId);

    if (!fromStep) {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'Bước nguồn không tồn tại'
      );
    }

    if (!toStep) {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'Bước đích không tồn tại'
      );
    }

    // Kiểm tra các bước có thuộc task không
    this.validationHelper.validateStepBelongsToTask(fromStep, taskId, 'nguồn');
    this.validationHelper.validateStepBelongsToTask(toStep, taskId, 'đích');

    // Kiểm tra kết nối đã tồn tại chưa
    const existingConnection = await this.adminStepConnectionRepository.findDuplicateConnection(
      taskId,
      fromStepId,
      toStepId,
      outputField,
      inputField
    );

    this.validationHelper.validateConnectionNotDuplicate(existingConnection);

    try {
      // Main logic
      const newConnection = await this.adminStepConnectionRepository.createConnection({
        taskId,
        fromStepId,
        toStepId,
        outputField,
        inputField
      });

      return plainToInstance(ConnectionResponseDto, newConnection);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo mới kết nối: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_CREATION_FAILED,
        'Lỗi khi tạo mới kết nối'
      );
    }
  }

  /**
   * Cập nhật thông tin kết nối
   * @param taskId ID của task
   * @param connectionId ID của kết nối
   * @param updateConnectionDto Dữ liệu cập nhật
   * @returns Thông tin kết nối đã cập nhật
   */
  @Transactional()
  async update(
    taskId: string,
    connectionId: string,
    updateConnectionDto: UpdateConnectionDto
  ): Promise<ConnectionResponseDto> {
    this.logger.log(`Cập nhật kết nối với ID: ${connectionId}, dữ liệu: ${JSON.stringify(updateConnectionDto)}`);

    // Business validation
    const task = await this.adminTaskRepository.findTaskById(taskId);
    this.validationHelper.validateTaskExists(task);

    const connection = await this.adminStepConnectionRepository.findConnectionById(connectionId);
    this.validationHelper.validateConnectionExists(connection);

    // Kiểm tra kết nối có thuộc task không
    // connection is guaranteed to be non-null after validateConnectionExists
    this.validationHelper.validateConnectionBelongsToTask(connection!, taskId);

    // Kiểm tra các bước mới (nếu có) có tồn tại và thuộc task không
    if (updateConnectionDto.fromStepId) {
      const fromStep = await this.adminStepRepository.findStepById(updateConnectionDto.fromStepId);
      if (!fromStep) {
        throw new AppException(
          ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
          'Bước nguồn không tồn tại'
        );
      }
      this.validationHelper.validateStepBelongsToTask(fromStep, taskId, 'nguồn');
    }

    if (updateConnectionDto.toStepId) {
      const toStep = await this.adminStepRepository.findStepById(updateConnectionDto.toStepId);
      if (!toStep) {
        throw new AppException(
          ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
          'Bước đích không tồn tại'
        );
      }
      this.validationHelper.validateStepBelongsToTask(toStep, taskId, 'đích');
    }

    // connection is guaranteed to be non-null after validateConnectionExists
    const fromStepId = updateConnectionDto.fromStepId || connection!.fromStepId;
    const toStepId = updateConnectionDto.toStepId || connection!.toStepId;
    const outputField = updateConnectionDto.outputField || connection!.outputField;
    const inputField = updateConnectionDto.inputField || connection!.inputField;

    if (fromStepId === toStepId) {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'Không thể kết nối một bước với chính nó'
      );
    }

    const existingConnection = await this.adminStepConnectionRepository.findDuplicateConnection(
      taskId,
      fromStepId,
      toStepId,
      outputField,
      inputField,
      connectionId // Loại trừ kết nối hiện tại
    );

    this.validationHelper.validateConnectionNotDuplicate(existingConnection);

    try {
      // Main logic
      const updatedConnection = await this.adminStepConnectionRepository.updateConnection(
        connectionId,
        {
          fromStepId,
          toStepId,
          outputField,
          inputField,
          updatedAt: Date.now()
        }
      );

      return plainToInstance(ConnectionResponseDto, updatedConnection);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật kết nối: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_UPDATE_FAILED,
        'Lỗi khi cập nhật kết nối'
      );
    }
  }

  /**
   * Xóa kết nối
   * @param taskId ID của task
   * @param connectionId ID của kết nối
   * @returns Void
   */
  @Transactional()
  async remove(taskId: string, connectionId: string): Promise<void> {
    this.logger.log(`Xóa kết nối với ID: ${connectionId} của task với ID: ${taskId}`);

    // Business validation
    const task = await this.adminTaskRepository.findTaskById(taskId);
    this.validationHelper.validateTaskExists(task);

    const connection = await this.adminStepConnectionRepository.findConnectionById(connectionId);
    this.validationHelper.validateConnectionExists(connection);

    // Kiểm tra kết nối có thuộc task không
    // connection is guaranteed to be non-null after validateConnectionExists
    this.validationHelper.validateConnectionBelongsToTask(connection!, taskId);

    try {
      // Main logic
      await this.adminStepConnectionRepository.deleteConnection(connectionId);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa kết nối: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_DELETE_FAILED,
        'Lỗi khi xóa kết nối'
      );
    }
  }
}
