import { Injectable, Logger } from '@nestjs/common';
import { AdminTaskRepository } from '../../repositories';
import { AppException } from '@common/exceptions/app.exception';
import { PaginatedResult } from '@common/response/api-response-dto';
import { plainToInstance } from 'class-transformer';
import { Transactional } from 'typeorm-transactional';
import { ValidationHelper } from '../helpers/validation.helper';
import { ADMIN_TASK_ERROR_CODES } from '../exceptions';
import {
  CreateTaskDto,
  UpdateTaskDto,
  QueryTaskDto,
  TaskResponseDto
} from '../dto';

/**
 * Service xử lý logic liên quan đến task của admin
 */
@Injectable()
export class AdminTaskService {
  /**
   * Logger cho AdminTaskService
   */
  private readonly logger = new Logger(AdminTaskService.name);

  /**
   * Constructor
   * @param adminTaskRepository Repository xử lý dữ liệu task của admin
   * @param validationHelper Helper xử lý validation
   */
  constructor(
    private readonly adminTaskRepository: AdminTaskRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Lấy danh sách task với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách task với phân trang
   */
  async findAll(queryDto: QueryTaskDto): Promise<PaginatedResult<TaskResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách task với query: ${JSON.stringify(queryDto)}`);

      const { page, limit, sortBy, sortDirection, search, agentId, active } = queryDto;
      const sortOrder = sortDirection || 'DESC';

      const [tasks, total] = await this.adminTaskRepository.findTasksWithPagination(
        page,
        limit,
        sortBy || 'createdAt',
        sortOrder || 'DESC',
        search,
        agentId,
        active
      );

      const taskResponses = plainToInstance(TaskResponseDto, tasks);

      return {
        items: taskResponses,
        meta: {
          totalItems: total,
          itemCount: taskResponses.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách task: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_FETCH_FAILED,
        'Lỗi khi lấy danh sách task'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết task theo ID
   * @param taskId ID của task
   * @returns Thông tin chi tiết task
   */
  async findOne(taskId: string): Promise<TaskResponseDto> {
    try {
      this.logger.log(`Lấy thông tin chi tiết task với ID: ${taskId}`);

      // Sử dụng phương thức mới để lấy thông tin task với thông tin nhân viên
      const task = await this.adminTaskRepository.findTaskByIdWithEmployeeInfo(taskId);

      this.validationHelper.validateTaskExists(task);

      // Chuyển đổi sang DTO với thông tin nhân viên
      return plainToInstance(TaskResponseDto, task);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết task: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_FETCH_FAILED,
        'Lỗi khi lấy thông tin chi tiết task'
      );
    }
  }

  /**
   * Tạo mới task
   * @param createTaskDto Dữ liệu tạo task
   * @param employeeId ID của nhân viên tạo task
   * @returns Thông tin task đã tạo
   */
  @Transactional()
  async create(createTaskDto: CreateTaskDto, employeeId: number): Promise<TaskResponseDto> {
    try {
      this.logger.log(`Tạo mới task với dữ liệu: ${JSON.stringify(createTaskDto)}`);

      const { taskName, taskDescription, agentId, active } = createTaskDto;

      this.validationHelper.validateTaskCreation(taskName, agentId);

      // Kiểm tra agent có tồn tại không
      await this.validationHelper.validateAgentExists(agentId);

      const newTask = await this.adminTaskRepository.createTask({
        taskName,
        taskDescription,
        agentId,
        active: active !== undefined ? active : true,
        createdBy: employeeId,
        updatedBy: employeeId
      });

      return plainToInstance(TaskResponseDto, newTask);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo mới task: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_CREATION_FAILED,
        'Lỗi khi tạo mới task'
      );
    }
  }

  /**
   * Cập nhật thông tin task
   * @param taskId ID của task
   * @param updateTaskDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns Thông tin task đã cập nhật
   */
  @Transactional()
  async update(taskId: string, updateTaskDto: UpdateTaskDto, employeeId: number): Promise<TaskResponseDto> {
    try {
      this.logger.log(`Cập nhật task với ID: ${taskId}, dữ liệu: ${JSON.stringify(updateTaskDto)}`);

      const task = await this.adminTaskRepository.findTaskById(taskId);

      this.validationHelper.validateTaskExists(task);

      if (updateTaskDto.taskName && updateTaskDto.agentId) {
        this.validationHelper.validateTaskCreation(updateTaskDto.taskName, updateTaskDto.agentId);
        // Kiểm tra agent có tồn tại không
        await this.validationHelper.validateAgentExists(updateTaskDto.agentId);
      } else if (updateTaskDto.taskName && task && task.agentId) {
        this.validationHelper.validateTaskCreation(updateTaskDto.taskName, task.agentId);
      } else if (task && task.taskName && updateTaskDto.agentId) {
        this.validationHelper.validateTaskCreation(task.taskName, updateTaskDto.agentId);
        // Kiểm tra agent có tồn tại không
        await this.validationHelper.validateAgentExists(updateTaskDto.agentId);
      }

      const updatedTask = await this.adminTaskRepository.updateTask(taskId, {
        ...updateTaskDto,
        updatedBy: employeeId,
        updatedAt: Date.now()
      });

      return plainToInstance(TaskResponseDto, updatedTask);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật task: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_UPDATE_FAILED,
        'Lỗi khi cập nhật task'
      );
    }
  }

  /**
   * Xóa task (soft delete)
   * @param taskId ID của task
   * @param employeeId ID của nhân viên xóa
   */
  @Transactional()
  async remove(taskId: string, employeeId: number): Promise<void> {
    try {
      this.logger.log(`Xóa task với ID: ${taskId}`);

      const task = await this.adminTaskRepository.findTaskById(taskId);

      this.validationHelper.validateTaskExists(task);

      await this.adminTaskRepository.softDeleteTask(taskId, employeeId);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa task: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_DELETE_FAILED,
        'Lỗi khi xóa task'
      );
    }
  }
}
