import { Injectable, Logger } from '@nestjs/common';
import { AdminTaskExecutionRepository, AdminTaskRepository } from '../../repositories';
import { AppException } from '@common/exceptions/app.exception';
import { PaginatedResult } from '@common/response/api-response-dto';
import { plainToInstance } from 'class-transformer';
import { ValidationHelper } from '@modules/task/admin/helpers';
import { ADMIN_EXECUTION_ERROR_CODES } from '../exceptions';
import {
  QueryExecutionDto,
  ExecutionResponseDto,
  ExecutionDetailResponseDto
} from '../dto';

/**
 * Service xử lý logic liên quan đến lịch sử thực thi task của admin
 * Chịu trách nhiệm quản lý các thao tác truy vấn và xử lý dữ liệu lịch sử thực thi task
 */
@Injectable()
export class AdminExecutionService {
  /**
   * Logger cho AdminExecutionService
   * @private
   */
  private readonly logger = new Logger(AdminExecutionService.name);

  /**
   * Constructor
   * @param adminTaskExecutionRepository Repository xử lý dữ liệu thực thi task của admin
   * @param adminTaskRepository Repository xử lý dữ liệu task của admin
   * @param validationHelper Helper xử lý validation
   */
  constructor(
    private readonly adminTaskExecutionRepository: AdminTaskExecutionRepository,
    private readonly adminTaskRepository: AdminTaskRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Lấy danh sách lịch sử thực thi của một task với phân trang
   * @param taskId ID của task
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử thực thi với phân trang
   */
  async findAllByTaskId(
    taskId: string,
    queryDto: QueryExecutionDto
  ): Promise<PaginatedResult<ExecutionResponseDto>> {
    this.logger.log(`Lấy danh sách lịch sử thực thi của task với ID: ${taskId}, query: ${JSON.stringify(queryDto)}`);

    // Business validation
    const task = await this.adminTaskRepository.findTaskById(taskId);
    this.validationHelper.validateTaskExists(task);

    try {
      // Main logic
      const { page, limit, sortBy, sortDirection, status, startTimeFrom, startTimeTo } = queryDto;
      const sortOrder = sortDirection || 'DESC';

      const [executions, total] = await this.adminTaskExecutionRepository.findExecutionsWithPagination(
        taskId,
        page,
        limit,
        sortBy || 'startTime',
        sortOrder,
        status,
        startTimeFrom,
        startTimeTo
      );

      const executionResponses = plainToInstance(ExecutionResponseDto, executions);

      return {
        items: executionResponses,
        meta: {
          totalItems: total,
          itemCount: executionResponses.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách lịch sử thực thi: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_FETCH_FAILED,
        `Lỗi khi lấy danh sách lịch sử thực thi: ${error.message}`
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của một lần thực thi
   * @param taskId ID của task
   * @param executionId ID của lần thực thi
   * @returns Thông tin chi tiết của lần thực thi
   */
  async findOne(taskId: string, executionId: string): Promise<ExecutionDetailResponseDto> {
    this.logger.log(`Lấy thông tin chi tiết lần thực thi với ID: ${executionId} của task với ID: ${taskId}`);

    // Business validation
    const task = await this.adminTaskRepository.findTaskById(taskId);
    this.validationHelper.validateTaskExists(task);

    const execution = await this.adminTaskExecutionRepository.findExecutionById(executionId);
    this.validationHelper.validateExecutionExists(execution);

    // Kiểm tra execution có thuộc task không
    this.validationHelper.validateExecutionBelongsToTask(execution, taskId);

    try {
      // Main logic
      return plainToInstance(ExecutionDetailResponseDto, execution);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết lần thực thi: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_FETCH_FAILED,
        `Lỗi khi lấy thông tin chi tiết lần thực thi: ${error.message}`
      );
    }
  }
}
