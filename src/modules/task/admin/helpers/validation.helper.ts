import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';
import { AdminTask, AdminStep, AdminStepConnection, AdminTaskExecution } from '../../entities';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';

import {
  ADMIN_TASK_ERROR_CODES,
  ADMIN_STEP_ERROR_CODES,
  ADMIN_CONNECTION_ERROR_CODES,
  ADMIN_EXECUTION_ERROR_CODES
} from '../exceptions';
import { StepType } from '@modules/task/interfaces/step-config.interface';

/**
 * Helper class để xác thực dữ liệu trong module task admin
 */
@Injectable()
export class ValidationHelper {
  /**
   * Constructor
   * @param agentRepository Repository xử lý dữ liệu agent
   */
  constructor(
    private readonly agentRepository: AgentRepository,
  ) {}
  /**
   * <PERSON><PERSON><PERSON> thực task tồn tại và chưa bị xóa
   * @param task Task cần xác thực
   * @throws AppException nếu task không tồn tại hoặc đã bị xóa
   * @returns void - Nếu task tồn tại và chưa bị xóa, hàm này sẽ không trả về gì
   */
  validateTaskExists(task: AdminTask | null): asserts task is AdminTask {
    if (!task) {
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND,
        'Không tìm thấy nhiệm vụ'
      );
    }

    if (task.deletedAt) {
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND,
        'Nhiệm vụ đã bị xóa'
      );
    }
  }

  /**
   * Xác thực dữ liệu tạo task
   * @param taskName Tên task
   * @param agentId ID của agent
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  validateTaskCreation(taskName: string, agentId: string): void {
    if (!taskName || taskName.trim() === '') {
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_INVALID_DATA,
        'Tên nhiệm vụ là bắt buộc'
      );
    }

    if (taskName.length > 255) {
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_INVALID_DATA,
        'Tên nhiệm vụ không được vượt quá 255 ký tự'
      );
    }

    if (!agentId || agentId.trim() === '') {
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_MISSING_AGENT,
        'ID của agent là bắt buộc'
      );
    }
  }

  /**
   * Xác thực agent tồn tại
   * @param agentId ID của agent
   * @throws AppException nếu agent không tồn tại
   */
  async validateAgentExists(agentId: string): Promise<void> {
    if (!agentId || agentId.trim() === '') {
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_MISSING_AGENT,
        'ID của agent là bắt buộc'
      );
    }

    const agent = await this.agentRepository.findById(agentId);

    if (!agent) {
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_AGENT_NOT_FOUND,
        'Không tìm thấy agent với ID đã cung cấp'
      );
    }

    // Kiểm tra thêm điều kiện is_for_sale phải là false
    if (agent.isForSale) {
      throw new AppException(
        ADMIN_TASK_ERROR_CODES.ADMIN_TASK_AGENT_NOT_AVAILABLE,
        'Agent này đang được bán, không thể sử dụng trong task'
      );
    }
  }

  /**
   * Xác thực step tồn tại
   * @param step Step cần xác thực
   * @throws AppException nếu step không tồn tại
   * @returns void - Nếu step tồn tại, hàm này sẽ không trả về gì
   */
  validateStepExists(step: AdminStep | null): asserts step is AdminStep {
    if (!step) {
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_NOT_FOUND,
        'Không tìm thấy bước'
      );
    }
  }

  /**
   * Xác thực step thuộc về task
   * @param step Step cần xác thực
   * @param taskId ID của task
   * @param stepType Loại bước (nguồn hoặc đích)
   * @throws AppException nếu step không thuộc task
   */
  validateStepBelongsToTask(step: AdminStep, taskId: string, stepType: 'nguồn' | 'đích'): void {
    if (step.taskId !== taskId) {
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
        `Bước ${stepType} không thuộc task này`
      );
    }
  }

  /**
   * Xác thực dữ liệu tạo step
   * @param stepName Tên step
   * @param stepType Loại step
   * @param taskId ID của task
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateStepCreation(stepName: string, stepType: string, taskId: string, stepConfig?: Record<string, any> | null): Promise<void> {
    if (!stepName || stepName.trim() === '') {
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
        'Tên bước là bắt buộc'
      );
    }

    if (stepName.length > 255) {
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
        'Tên bước không được vượt quá 255 ký tự'
      );
    }

    const validStepTypes = ['prompt', 'google_sheet', 'google_doc', 'google_calendar', 'email', 'facebook_page', 'gen_image', 'gen_video', 'agent'];
    if (!validStepTypes.includes(stepType)) {
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
        'Loại bước không hợp lệ. Phải là một trong các giá trị: ' + validStepTypes.join(', ')
      );
    }

    if (!taskId || taskId.trim() === '') {
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
        'ID của nhiệm vụ là bắt buộc'
      );
    }

    // Kiểm tra tính tương thích giữa stepType và stepConfig nếu có
    if (stepConfig) {
      await this.validateStepTypeAndConfigCompatibility(stepType, stepConfig);
    }
  }

  /**
   * Kiểm tra tính tương thích giữa stepType và stepConfig
   * @param stepType Loại step
   * @param stepConfig Cấu hình step
   * @throws AppException nếu stepType và stepConfig không tương thích
   */
  async validateStepTypeAndConfigCompatibility(stepType: string, stepConfig: Record<string, any>): Promise<void> {
    // Kiểm tra xem stepConfig có trường stepType không
    if (stepConfig.stepType && stepConfig.stepType !== stepType) {
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
        `Loại bước trong cấu hình (${stepConfig.stepType}) không khớp với loại bước được chỉ định (${stepType})`
      );
    }

    // Kiểm tra các trường bắt buộc theo từng loại step
    switch (stepType) {
      case 'prompt':
        if (!this.hasPromptConfigFields(stepConfig)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Cấu hình cho bước loại prompt phải có các trường: promptText, inputType, required'
          );
        }
        break;
      case 'google_sheet':
        if (!this.hasGoogleSheetConfigFields(stepConfig)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Cấu hình cho bước loại google_sheet phải có các trường: sheetId, range, action, credentialId'
          );
        }
        break;
      case 'google_doc':
        if (!this.hasGoogleDocConfigFields(stepConfig)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Cấu hình cho bước loại google_doc phải có các trường: action, content, credentialId'
          );
        }
        break;
      case 'google_calendar':
        if (!this.hasGoogleCalendarConfigFields(stepConfig)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Cấu hình cho bước loại google_calendar phải có các trường: calendarId, action, eventDetails, credentialId'
          );
        }
        break;
      case 'email':
        if (!this.hasEmailConfigFields(stepConfig)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Cấu hình cho bước loại email phải có các trường: to, subject, body'
          );
        }
        break;
      case 'facebook_page':
        if (!this.hasFacebookPageConfigFields(stepConfig)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Cấu hình cho bước loại facebook_page phải có các trường: pageId, action, content, credentialId'
          );
        }
        break;
      case 'gen_image':
        if (!this.hasGenImageConfigFields(stepConfig)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Cấu hình cho bước loại gen_image phải có các trường: prompt, resolution'
          );
        }
        break;
      case 'gen_video':
        if (!this.hasGenVideoConfigFields(stepConfig)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Cấu hình cho bước loại gen_video phải có các trường: prompt, duration, resolution'
          );
        }
        break;
      case 'agent':
        if (!this.hasAgentConfigFields(stepConfig)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Cấu hình cho bước loại agent phải có các trường: agentId, parameters'
          );
        }
        // Kiểm tra agentId có tồn tại trong database không
        await this.validateAgentExists(stepConfig.agentId);
        break;
    }
  }

  /**
   * Kiểm tra xem cấu hình có các trường cần thiết cho bước loại prompt không
   */
  private hasPromptConfigFields(config: Record<string, any>): boolean {
    return !!config.promptText && !!config.inputType && config.required !== undefined;
  }

  /**
   * Kiểm tra xem cấu hình có các trường cần thiết cho bước loại google_sheet không
   */
  private hasGoogleSheetConfigFields(config: Record<string, any>): boolean {
    return !!config.sheetId && !!config.range && !!config.action && !!config.credentialId;
  }

  /**
   * Kiểm tra xem cấu hình có các trường cần thiết cho bước loại google_doc không
   */
  private hasGoogleDocConfigFields(config: Record<string, any>): boolean {
    // docId chỉ bắt buộc khi action là append hoặc replace
    if (config.action === 'create') {
      return !!config.action && !!config.content && !!config.credentialId;
    }
    return !!config.docId && !!config.action && !!config.content && !!config.credentialId;
  }

  /**
   * Kiểm tra xem cấu hình có các trường cần thiết cho bước loại google_calendar không
   */
  private hasGoogleCalendarConfigFields(config: Record<string, any>): boolean {
    if (!config.calendarId || !config.action || !config.eventDetails || !config.credentialId) {
      return false;
    }

    // Kiểm tra các trường trong eventDetails tùy theo action
    if (config.action === 'delete_event') {
      return !!config.eventDetails.eventId;
    } else if (['create_event', 'update_event'].includes(config.action)) {
      if (config.action === 'update_event' && !config.eventDetails.eventId) {
        return false;
      }
      return !!config.eventDetails.summary &&
             !!config.eventDetails.start && !!config.eventDetails.start.dateTime &&
             !!config.eventDetails.end && !!config.eventDetails.end.dateTime;
    }

    return false;
  }

  /**
   * Kiểm tra xem cấu hình có các trường cần thiết cho bước loại email không
   */
  private hasEmailConfigFields(config: Record<string, any>): boolean {
    return !!config.to && Array.isArray(config.to) && config.to.length > 0 &&
           !!config.subject && !!config.body;
  }

  /**
   * Kiểm tra xem cấu hình có các trường cần thiết cho bước loại facebook_page không
   */
  private hasFacebookPageConfigFields(config: Record<string, any>): boolean {
    if (!config.pageId || !config.action || !config.content || !config.credentialId) {
      return false;
    }

    // Kiểm tra các trường trong content tùy theo action
    if (config.action === 'post') {
      return !!config.content.message;
    } else if (config.action === 'comment') {
      return !!config.content.message && !!config.content.commentId;
    } else if (config.action === 'get_insights') {
      return !!config.content.insightType;
    }

    return false;
  }

  /**
   * Kiểm tra xem cấu hình có các trường cần thiết cho bước loại gen_image không
   */
  private hasGenImageConfigFields(config: Record<string, any>): boolean {
    return !!config.prompt && !!config.resolution;
  }

  /**
   * Kiểm tra xem cấu hình có các trường cần thiết cho bước loại gen_video không
   */
  private hasGenVideoConfigFields(config: Record<string, any>): boolean {
    return !!config.prompt && config.duration !== undefined &&
           typeof config.duration === 'number' && config.duration > 0 &&
           !!config.resolution;
  }

  /**
   * Kiểm tra xem cấu hình có các trường cần thiết cho bước loại agent không
   */
  private hasAgentConfigFields(config: Record<string, any>): boolean {
    return !!config.agentId && config.agentId.trim() !== '' && !!config.parameters && typeof config.parameters === 'object';
  }

  /**
   * Xác thực dữ liệu cấu hình step
   * @param stepType Loại step
   * @param stepConfig Cấu hình step
   * @throws AppException nếu cấu hình không hợp lệ
   */
  async validateStepConfig(stepType: string, stepConfig: Record<string, any> | null): Promise<void> {
    if (!stepConfig) {
      throw new AppException(
        ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
        'Cấu hình bước là bắt buộc'
      );
    }

    // Kiểm tra tính tương thích giữa stepType và stepConfig
    await this.validateStepTypeAndConfigCompatibility(stepType, stepConfig);

    switch (stepType) {
      case 'prompt':
        // Validate PromptStepConfig
        if (!stepConfig.promptText) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Văn bản hướng dẫn (promptText) là bắt buộc'
          );
        }
        if (!stepConfig.inputType) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Kiểu dữ liệu đầu vào (inputType) là bắt buộc'
          );
        }
        const validInputTypes = ['text', 'number', 'date', 'boolean'];
        if (!validInputTypes.includes(stepConfig.inputType)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Kiểu dữ liệu đầu vào (inputType) không hợp lệ. Phải là một trong các giá trị: ' + validInputTypes.join(', ')
          );
        }
        if (stepConfig.required === undefined || stepConfig.required === null) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Trường required là bắt buộc'
          );
        }
        if (typeof stepConfig.required !== 'boolean') {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Trường required phải là kiểu boolean'
          );
        }
        break;

      case 'google_sheet':
        // Validate GoogleSheetStepConfig
        if (!stepConfig.sheetId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID của Google Sheet (sheetId) là bắt buộc'
          );
        }
        if (!stepConfig.range) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Vùng dữ liệu (range) là bắt buộc'
          );
        }
        if (!stepConfig.action) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Hành động (action) là bắt buộc'
          );
        }
        const validGSheetActions = ['read', 'write'];
        if (!validGSheetActions.includes(stepConfig.action)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Hành động (action) không hợp lệ. Phải là một trong các giá trị: ' + validGSheetActions.join(', ')
          );
        }
        if (!stepConfig.credentialId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID xác thực (credentialId) là bắt buộc'
          );
        }
        break;

      case 'google_doc':
        // Validate GoogleDocStepConfig
        if (!stepConfig.action) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Hành động (action) là bắt buộc'
          );
        }
        const validGDocActions = ['create', 'append', 'replace'];
        if (!validGDocActions.includes(stepConfig.action)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Hành động (action) không hợp lệ. Phải là một trong các giá trị: ' + validGDocActions.join(', ')
          );
        }
        if (stepConfig.action !== 'create' && !stepConfig.docId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID của Google Doc (docId) là bắt buộc khi action là append hoặc replace'
          );
        }
        if (!stepConfig.content) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Nội dung (content) là bắt buộc'
          );
        }
        if (!stepConfig.credentialId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID xác thực (credentialId) là bắt buộc'
          );
        }
        break;

      case 'google_calendar':
        // Validate GoogleCalendarStepConfig
        if (!stepConfig.calendarId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID của lịch (calendarId) là bắt buộc'
          );
        }
        if (!stepConfig.action) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Hành động (action) là bắt buộc'
          );
        }
        const validCalendarActions = ['create_event', 'update_event', 'delete_event'];
        if (!validCalendarActions.includes(stepConfig.action)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Hành động (action) không hợp lệ. Phải là một trong các giá trị: ' + validCalendarActions.join(', ')
          );
        }
        if (!stepConfig.eventDetails) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Chi tiết sự kiện (eventDetails) là bắt buộc'
          );
        }
        if (['update_event', 'delete_event'].includes(stepConfig.action) && !stepConfig.eventDetails.eventId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID sự kiện (eventId) là bắt buộc khi action là update_event hoặc delete_event'
          );
        }
        if (stepConfig.action !== 'delete_event') {
          if (!stepConfig.eventDetails.summary) {
            throw new AppException(
              ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
              'Tiêu đề sự kiện (summary) là bắt buộc'
            );
          }
          if (!stepConfig.eventDetails.start || !stepConfig.eventDetails.start.dateTime) {
            throw new AppException(
              ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
              'Thời gian bắt đầu (start.dateTime) là bắt buộc'
            );
          }
          if (!stepConfig.eventDetails.end || !stepConfig.eventDetails.end.dateTime) {
            throw new AppException(
              ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
              'Thời gian kết thúc (end.dateTime) là bắt buộc'
            );
          }
        }
        if (!stepConfig.credentialId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID xác thực (credentialId) là bắt buộc'
          );
        }
        break;

      case 'email':
        // Validate EmailStepConfig
        if (!stepConfig.to || !Array.isArray(stepConfig.to) || stepConfig.to.length === 0) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Danh sách email người nhận (to) là bắt buộc và phải là một mảng không rỗng'
          );
        }
        if (!stepConfig.subject) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Chủ đề email (subject) là bắt buộc'
          );
        }
        if (!stepConfig.body) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Nội dung email (body) là bắt buộc'
          );
        }
        break;

      case 'facebook_page':
        // Validate FacebookPageStepConfig
        if (!stepConfig.pageId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID của Fanpage (pageId) là bắt buộc'
          );
        }
        if (!stepConfig.action) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Hành động (action) là bắt buộc'
          );
        }
        const validFBActions = ['post', 'comment', 'get_insights'];
        if (!validFBActions.includes(stepConfig.action)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Hành động (action) không hợp lệ. Phải là một trong các giá trị: ' + validFBActions.join(', ')
          );
        }
        if (!stepConfig.content) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Nội dung (content) là bắt buộc'
          );
        }
        if (stepConfig.action === 'post' && !stepConfig.content.message) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Nội dung bài đăng (message) là bắt buộc khi action là post'
          );
        }
        if (stepConfig.action === 'comment' && (!stepConfig.content.message || !stepConfig.content.commentId)) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Nội dung bình luận (message) và ID bình luận (commentId) là bắt buộc khi action là comment'
          );
        }
        if (stepConfig.action === 'get_insights' && !stepConfig.content.insightType) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Loại insight (insightType) là bắt buộc khi action là get_insights'
          );
        }
        if (!stepConfig.credentialId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID xác thực (credentialId) là bắt buộc'
          );
        }
        break;

      case 'gen_image':
        // Validate GenImageStepConfig
        if (!stepConfig.prompt) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Mô tả văn bản (prompt) là bắt buộc'
          );
        }
        if (!stepConfig.resolution) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Độ phân giải (resolution) là bắt buộc'
          );
        }
        break;

      case 'gen_video':
        // Validate GenVideoStepConfig
        if (!stepConfig.prompt) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Mô tả văn bản (prompt) là bắt buộc'
          );
        }
        if (stepConfig.duration === undefined || stepConfig.duration === null) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Độ dài video (duration) là bắt buộc'
          );
        }
        if (typeof stepConfig.duration !== 'number' || stepConfig.duration <= 0) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Độ dài video (duration) phải là số dương'
          );
        }
        if (!stepConfig.resolution) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Độ phân giải (resolution) là bắt buộc'
          );
        }
        break;

      case 'agent':
        // Validate AgentStepConfig
        if (!stepConfig.agentId) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID của agent (agentId) là bắt buộc'
          );
        }
        if (stepConfig.agentId.trim() === '') {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'ID của agent (agentId) không được để trống'
          );
        }
        // Kiểm tra agentId có tồn tại trong database không
        await this.validateAgentExists(stepConfig.agentId);

        if (!stepConfig.parameters) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Tham số (parameters) là bắt buộc'
          );
        }
        if (typeof stepConfig.parameters !== 'object') {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Tham số (parameters) phải là một đối tượng'
          );
        }
        break;

      case 'trigger':
        if (!stepConfig.triggerType) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Loại trigger là bắt buộc'
          );
        }
        break;

      case 'action':
        if (!stepConfig.actionType) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Loại action là bắt buộc'
          );
        }
        break;

      case 'media':
        if (!stepConfig.mediaType) {
          throw new AppException(
            ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
            'Loại media là bắt buộc'
          );
        }
        break;
    }
  }

  /**
   * Xác thực connection tồn tại
   * @param connection Connection cần xác thực
   * @throws AppException nếu connection không tồn tại
   * @returns void - Nếu connection tồn tại, hàm này sẽ không trả về gì
   */
  validateConnectionExists(connection: AdminStepConnection | null): asserts connection is AdminStepConnection {
    if (!connection) {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_NOT_FOUND,
        'Không tìm thấy kết nối'
      );
    }
  }

  /**
   * Xác thực kết nối thuộc về task
   * @param connection Kết nối cần xác thực
   * @param taskId ID của task
   * @throws AppException nếu kết nối không thuộc task
   */
  validateConnectionBelongsToTask(connection: AdminStepConnection, taskId: string): void {
    if (connection.taskId !== taskId) {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'Kết nối không thuộc task này'
      );
    }
  }

  /**
   * Xác thực dữ liệu tạo connection
   * @param fromStepId ID của step nguồn
   * @param toStepId ID của step đích
   * @param outputField Tên trường output
   * @param inputField Tên trường input
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  validateConnectionCreation(
    fromStepId: string,
    toStepId: string,
    outputField: string,
    inputField: string
  ): void {
    if (!fromStepId || fromStepId.trim() === '') {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'ID của step nguồn là bắt buộc'
      );
    }

    if (!toStepId || toStepId.trim() === '') {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'ID của step đích là bắt buộc'
      );
    }

    if (fromStepId === toStepId) {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'Step nguồn và step đích không được trùng nhau'
      );
    }

    if (!outputField || outputField.trim() === '') {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'Tên trường output là bắt buộc'
      );
    }

    if (!inputField || inputField.trim() === '') {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'Tên trường input là bắt buộc'
      );
    }

    if (outputField.length > 255) {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'Tên trường output không được vượt quá 255 ký tự'
      );
    }

    if (inputField.length > 255) {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
        'Tên trường input không được vượt quá 255 ký tự'
      );
    }
  }

  /**
   * Xác thực kết nối không bị trùng lặp
   * @param existingConnection Kết nối đã tồn tại (nếu có)
   * @throws AppException nếu kết nối đã tồn tại
   */
  validateConnectionNotDuplicate(existingConnection: AdminStepConnection | null): void {
    if (existingConnection) {
      throw new AppException(
        ADMIN_CONNECTION_ERROR_CODES.CONNECTION_DUPLICATE,
        'Kết nối này đã tồn tại'
      );
    }
  }

  /**
   * Xác thực execution tồn tại
   * @param execution Execution cần xác thực
   * @throws AppException nếu execution không tồn tại
   * @returns void - Nếu execution tồn tại, hàm này sẽ không trả về gì
   */
  validateExecutionExists(execution: AdminTaskExecution | null): asserts execution is AdminTaskExecution {
    if (!execution) {
      throw new AppException(
        ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_NOT_FOUND,
        'Không tìm thấy phiên thực thi'
      );
    }
  }

  /**
   * Xác thực execution thuộc về task
   * @param execution Execution cần xác thực
   * @param taskId ID của task
   * @throws AppException nếu execution không thuộc task
   */
  validateExecutionBelongsToTask(execution: AdminTaskExecution, taskId: string): void {
    if (execution.taskId !== taskId) {
      throw new AppException(
        ADMIN_EXECUTION_ERROR_CODES.EXECUTION_INVALID_DATA,
        'Lần thực thi không thuộc task này'
      );
    }
  }
}
