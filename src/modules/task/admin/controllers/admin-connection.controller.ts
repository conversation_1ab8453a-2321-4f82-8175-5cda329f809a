import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminConnectionService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import {
  CreateConnectionDto,
  UpdateConnectionDto,
  ConnectionResponseDto
} from '../dto';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { CurrentEmployee } from '@/modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@/modules/auth/interfaces';
import { ApiErrorResponseWithExamples } from '@/common/error/api-error-response-with-examples.decorator';
import { ApiErrorResponseDto } from '@/common/error/api-error-response.dto';
import { ADMIN_CONNECTION_ERROR_CODES } from '@modules/task/admin/exceptions';

/**
 * Controller xử lý các endpoint liên quan đến kết nối giữa các bước trong task của admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TASK)
@ApiExtraModels(ApiResponseDto, ConnectionResponseDto, ApiErrorResponseDto)
@Controller('admin/tasks/:taskId/connections')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@ApiBearerAuth('JWT-auth')
export class AdminConnectionController {
  /**
   * Constructor
   * @param adminConnectionService Service xử lý logic liên quan đến kết nối giữa các bước trong task của admin
   */
  constructor(private readonly adminConnectionService: AdminConnectionService) {}

  /**
   * Lấy danh sách kết nối của một task
   * @param taskId ID của task
   * @param employee
   * @returns Danh sách kết nối
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách kết nối của một task' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kết nối',
    schema: ApiResponseDto.getArraySchema(ConnectionResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_FETCH_FAILED,
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_UNAUTHORIZED
  )
  async findAll(
    @Param('taskId') taskId: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<ConnectionResponseDto[]>> {
    const result = await this.adminConnectionService.findAllByTaskId(taskId);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy thông tin chi tiết của một kết nối
   * @param taskId ID của task
   * @param connectionId ID của kết nối
   * @param employee
   * @returns Thông tin chi tiết của kết nối
   */
  @Get(':connectionId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết của một kết nối' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiParam({ name: 'connectionId', description: 'ID của kết nối' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết của kết nối',
    schema: ApiResponseDto.getSchema(ConnectionResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_NOT_FOUND,
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_FETCH_FAILED,
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_UNAUTHORIZED
  )
  async findOne(
    @Param('taskId') taskId: string,
    @Param('connectionId') connectionId: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<ConnectionResponseDto>> {
    const result = await this.adminConnectionService.findOne(connectionId);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo mới kết nối giữa các bước
   * @param taskId ID của task
   * @param createConnectionDto Dữ liệu tạo kết nối
   * @param employee
   * @returns Thông tin kết nối đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới kết nối giữa các bước' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiResponse({
    status: 201,
    description: 'Thông tin kết nối đã tạo',
    schema: ApiResponseDto.getSchema(ConnectionResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_CREATION_FAILED,
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
    ADMIN_CONNECTION_ERROR_CODES.CONNECTION_SAME_STEP,
    ADMIN_CONNECTION_ERROR_CODES.CONNECTION_STEP_NOT_FOUND,
    ADMIN_CONNECTION_ERROR_CODES.CONNECTION_STEPS_DIFFERENT_TASKS,
    ADMIN_CONNECTION_ERROR_CODES.CONNECTION_DUPLICATE
  )
  async create(
    @Param('taskId') taskId: string,
    @Body() createConnectionDto: CreateConnectionDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<ConnectionResponseDto>> {
    const result = await this.adminConnectionService.create(taskId, createConnectionDto);
    return ApiResponseDto.created(result);
  }

  /**
   * Cập nhật thông tin kết nối
   * @param taskId ID của task
   * @param connectionId ID của kết nối
   * @param updateConnectionDto Dữ liệu cập nhật
   * @param employee
   * @returns Thông tin kết nối đã cập nhật
   */
  @Patch(':connectionId')
  @ApiOperation({ summary: 'Cập nhật thông tin kết nối' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiParam({ name: 'connectionId', description: 'ID của kết nối' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin kết nối đã cập nhật',
    schema: ApiResponseDto.getSchema(ConnectionResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_NOT_FOUND,
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_UPDATE_FAILED,
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_INVALID_DATA,
    ADMIN_CONNECTION_ERROR_CODES.CONNECTION_SAME_STEP,
    ADMIN_CONNECTION_ERROR_CODES.CONNECTION_STEP_NOT_FOUND
  )
  async update(
    @Param('taskId') taskId: string,
    @Param('connectionId') connectionId: string,
    @Body() updateConnectionDto: UpdateConnectionDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<ConnectionResponseDto>> {
    const result = await this.adminConnectionService.update(taskId, connectionId, updateConnectionDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa kết nối
   * @param taskId ID của task
   * @param connectionId ID của kết nối
   * @param employee
   * @returns Thông báo xóa thành công
   */
  @Delete(':connectionId')
  @ApiOperation({ summary: 'Xóa kết nối' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiParam({ name: 'connectionId', description: 'ID của kết nối' })
  @ApiResponse({
    status: 200,
    description: 'Thông báo xóa thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_NOT_FOUND,
    ADMIN_CONNECTION_ERROR_CODES.ADMIN_CONNECTION_DELETE_FAILED
  )
  async remove(
    @Param('taskId') taskId: string,
    @Param('connectionId') connectionId: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<null>> {
    await this.adminConnectionService.remove(taskId, connectionId);
    return ApiResponseDto.success(null, 'Xóa kết nối thành công');
  }
}
