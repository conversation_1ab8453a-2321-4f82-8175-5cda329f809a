import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminExecutionService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import {
  QueryExecutionDto,
  ExecutionResponseDto,
  ExecutionDetailResponseDto
} from '../dto';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { CurrentEmployee } from '@/modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@/modules/auth/interfaces';
import { ApiErrorResponseWithExamples } from '@/common/error/api-error-response-with-examples.decorator';
import { ApiErrorResponseDto } from '@/common/error/api-error-response.dto';
import { ADMIN_EXECUTION_ERROR_CODES } from '@modules/task/admin/exceptions';
import { TaskExecutionStatus } from '@/modules/task/enums/task-execution-status.enum';

/**
 * Controller xử lý các endpoint liên quan đến lịch sử thực thi task của admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TASK)
@ApiExtraModels(ApiResponseDto, ExecutionResponseDto, ExecutionDetailResponseDto, PaginatedResult, ApiErrorResponseDto)
@Controller('admin/tasks/:taskId/executions')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@ApiBearerAuth('JWT-auth')
export class AdminExecutionController {
  /**
   * Constructor
   * @param adminExecutionService Service xử lý logic liên quan đến lịch sử thực thi task của admin
   */
  constructor(private readonly adminExecutionService: AdminExecutionService) {}

  /**
   * Lấy danh sách lịch sử thực thi của một task với phân trang
   * @param taskId ID của task
   * @param queryDto Tham số truy vấn
   * @param employee
   * @returns Danh sách lịch sử thực thi với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách lịch sử thực thi của một task với phân trang' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng bản ghi trên mỗi trang', type: Number })
  @ApiQuery({ name: 'status', required: false, description: 'Lọc theo trạng thái', enum: TaskExecutionStatus })
  @ApiQuery({ name: 'startTimeFrom', required: false, description: 'Lọc theo thời gian bắt đầu từ (Unix epoch)', type: Number })
  @ApiQuery({ name: 'startTimeTo', required: false, description: 'Lọc theo thời gian bắt đầu đến (Unix epoch)', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Danh sách lịch sử thực thi với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(ExecutionResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_FETCH_FAILED,
    ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_UNAUTHORIZED
  )
  async findAll(
    @Param('taskId') taskId: string,
    @Query() queryDto: QueryExecutionDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<PaginatedResult<ExecutionResponseDto>>> {
    const result = await this.adminExecutionService.findAllByTaskId(taskId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy thông tin chi tiết của một lần thực thi
   * @param taskId ID của task
   * @param executionId ID của lần thực thi
   * @param employee
   * @returns Thông tin chi tiết của lần thực thi
   */
  @Get(':executionId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết của một lần thực thi' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiParam({ name: 'executionId', description: 'ID của lần thực thi' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết của lần thực thi',
    schema: ApiResponseDto.getSchema(ExecutionDetailResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_NOT_FOUND,
    ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_FETCH_FAILED,
    ADMIN_EXECUTION_ERROR_CODES.ADMIN_EXECUTION_UNAUTHORIZED
  )
  async findOne(
    @Param('taskId') taskId: string,
    @Param('executionId') executionId: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<ExecutionDetailResponseDto>> {
    const result = await this.adminExecutionService.findOne(taskId, executionId);
    return ApiResponseDto.success(result);
  }
}
