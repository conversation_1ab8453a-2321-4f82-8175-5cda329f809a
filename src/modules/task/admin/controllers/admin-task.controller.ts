import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { Body, Controller, Delete, Get, Param, Post, Patch, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminTaskService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { CurrentEmployee } from '@/modules/auth/decorators/current-employee.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import {
  CreateTaskDto,
  UpdateTaskDto,
  QueryTaskDto,
  TaskResponseDto
} from '../dto';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { JWTPayload } from '@/modules/auth/interfaces';
import { ApiErrorResponseWithExamples } from '@/common/error/api-error-response-with-examples.decorator';
import { ApiErrorResponseDto } from '@/common/error/api-error-response.dto';
import { ADMIN_TASK_ERROR_CODES } from '@modules/task/admin/exceptions';

/**
 * Controller xử lý các endpoint liên quan đến task của admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TASK)
@ApiExtraModels(ApiResponseDto, TaskResponseDto, PaginatedResult, ApiErrorResponseDto)
@Controller('admin/tasks')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@ApiBearerAuth('JWT-auth')
export class AdminTaskController {
  /**
   * Constructor
   * @param adminTaskService Service xử lý logic liên quan đến task của admin
   */
  constructor(private readonly adminTaskService: AdminTaskService) {}

  /**
   * Lấy danh sách task với phân trang
   * @param queryDto Tham số truy vấn
   * @param employee
   * @returns Danh sách task với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách task với phân trang' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng bản ghi trên mỗi trang', type: Number })
  @ApiQuery({ name: 'search', required: false, description: 'Tìm kiếm theo tên task', type: String })
  @ApiQuery({ name: 'agentId', required: false, description: 'Lọc theo ID của agent', type: String })
  @ApiQuery({ name: 'active', required: false, description: 'Lọc theo trạng thái hoạt động', type: Boolean })
  @ApiResponse({
    status: 200,
    description: 'Danh sách task với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(TaskResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_FETCH_FAILED,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_UNAUTHORIZED
  )
  async findAll(
    @Query() queryDto: QueryTaskDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<PaginatedResult<TaskResponseDto>>> {
    const result = await this.adminTaskService.findAll(queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy thông tin chi tiết task theo ID
   * @param taskId ID của task
   * @param employee
   * @returns Thông tin chi tiết task
   */
  @Get(':taskId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết task theo ID' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết task',
    schema: ApiResponseDto.getSchema(TaskResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_FETCH_FAILED,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_UNAUTHORIZED
  )
  async findOne(
    @Param('taskId') taskId: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<TaskResponseDto>> {
    const result = await this.adminTaskService.findOne(taskId);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo mới task
   * @param createTaskDto Dữ liệu tạo task
   * @param employee Thông tin nhân viên tạo task
   * @returns Thông tin task đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới task' })
  @ApiResponse({
    status: 201,
    description: 'Thông tin task đã tạo',
    schema: ApiResponseDto.getSchema(TaskResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_CREATION_FAILED,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_INVALID_DATA,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_MISSING_AGENT,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_AGENT_NOT_FOUND,
    ADMIN_TASK_ERROR_CODES.TASK_NAME_REQUIRED,
    ADMIN_TASK_ERROR_CODES.TASK_NAME_TOO_LONG
  )
  async create(
    @Body() createTaskDto: CreateTaskDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<TaskResponseDto>> {
    const result = await this.adminTaskService.create(createTaskDto, Number(employee.id));
    return ApiResponseDto.created(result);
  }

  /**
   * Cập nhật thông tin task
   * @param taskId ID của task
   * @param updateTaskDto Dữ liệu cập nhật
   * @param employee Thông tin nhân viên cập nhật
   * @returns Thông tin task đã cập nhật
   */
  @Patch(':taskId')
  @ApiOperation({ summary: 'Cập nhật thông tin task' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin task đã cập nhật',
    schema: ApiResponseDto.getSchema(TaskResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_UPDATE_FAILED,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_INVALID_DATA,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_AGENT_NOT_FOUND,
    ADMIN_TASK_ERROR_CODES.TASK_NAME_TOO_LONG
  )
  async update(
    @Param('taskId') taskId: string,
    @Body() updateTaskDto: UpdateTaskDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<TaskResponseDto>> {
    const result = await this.adminTaskService.update(taskId, updateTaskDto, Number(employee.id));
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa task (soft delete)
   * @param taskId ID của task
   * @param employee Thông tin nhân viên xóa
   * @returns Thông báo xóa thành công
   */
  @Delete(':taskId')
  @ApiOperation({ summary: 'Xóa task (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'Thông báo xóa thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_NOT_FOUND,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_DELETE_FAILED,
    ADMIN_TASK_ERROR_CODES.ADMIN_TASK_UNAUTHORIZED
  )
  async remove(
    @Param('taskId') taskId: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<null>> {
    await this.adminTaskService.remove(taskId, Number(employee.id));
    return ApiResponseDto.success(null, 'Xóa task thành công');
  }
}
