import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { Body, Controller, Delete, Get, Param, Post, Patch, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminStepService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import {
  CreateStepDto,
  UpdateStepDto,
  ReorderStepsDto,
  StepResponseDto,
} from '../dto';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { CurrentEmployee } from '@/modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@/modules/auth/interfaces';
import { ApiErrorResponseWithExamples } from '@/common/error/api-error-response-with-examples.decorator';
import { ApiErrorResponseDto } from '@/common/error/api-error-response.dto';
import { ADMIN_STEP_ERROR_CODES } from '@modules/task/admin/exceptions';
import { ApiCreateStepBody } from '@modules/task/admin/dto/step/query-step.dto';

/**
 * Controller xử lý các endpoint liên quan đến các bước trong task của admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TASK)
@ApiExtraModels(ApiResponseDto, StepResponseDto, ApiErrorResponseDto)
@Controller('admin/tasks/:taskId/steps')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@ApiBearerAuth('JWT-auth')
export class AdminStepController {
  /**
   * Constructor
   * @param adminStepService Service xử lý logic liên quan đến các bước trong task của admin
   */
  constructor(private readonly adminStepService: AdminStepService) {}

  /**
   * Lấy danh sách các bước của một task
   * @param taskId ID của task
   * @param employee
   * @returns Danh sách các bước
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách các bước của một task' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách các bước',
    schema: ApiResponseDto.getArraySchema(StepResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_FETCH_FAILED,
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_UNAUTHORIZED
  )
  async findAll(
    @Param('taskId') taskId: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<StepResponseDto[]>> {
    const result = await this.adminStepService.findAllByTaskId(taskId);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy thông tin chi tiết của một bước
   * @param taskId ID của task
   * @param stepId ID của bước
   * @param employee
   * @returns Thông tin chi tiết của bước
   */
  @Get(':stepId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết của một bước' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiParam({ name: 'stepId', description: 'ID của bước' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết của bước',
    schema: ApiResponseDto.getSchema(StepResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_NOT_FOUND,
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_FETCH_FAILED,
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_UNAUTHORIZED
  )
  async findOne(
    @Param('taskId') taskId: string,
    @Param('stepId') stepId: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<StepResponseDto>> {
    const result = await this.adminStepService.findOne(stepId);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo mới bước cho task
   * @param taskId ID của task
   * @param createStepDto Dữ liệu tạo bước
   * @param employee
   * @returns Thông tin bước đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới bước cho task' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiCreateStepBody()
  @ApiResponse({
    status: 201,
    description: 'Thông tin bước đã tạo',
    schema: ApiResponseDto.getSchema(StepResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_CREATION_FAILED,
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
    ADMIN_STEP_ERROR_CODES.STEP_NAME_REQUIRED,
    ADMIN_STEP_ERROR_CODES.STEP_NAME_TOO_LONG,
    ADMIN_STEP_ERROR_CODES.STEP_INVALID_TYPE,
    ADMIN_STEP_ERROR_CODES.STEP_CONFIG_INVALID
  )
  async create(
    @Param('taskId') taskId: string,
    @Body() createStepDto: CreateStepDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<StepResponseDto>> {
    const result = await this.adminStepService.create(taskId, createStepDto);
    return ApiResponseDto.created(result);
  }

  /**
   * Cập nhật thông tin bước
   * @param taskId ID của task
   * @param stepId ID của bước
   * @param updateStepDto Dữ liệu cập nhật
   * @param employee
   * @returns Thông tin bước đã cập nhật
   */
  @Patch(':stepId')
  @ApiOperation({ summary: 'Cập nhật thông tin bước' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiParam({ name: 'stepId', description: 'ID của bước' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin bước đã cập nhật',
    schema: ApiResponseDto.getSchema(StepResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_NOT_FOUND,
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_UPDATE_FAILED,
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_INVALID_DATA,
    ADMIN_STEP_ERROR_CODES.STEP_CONFIG_INVALID,
    ADMIN_STEP_ERROR_CODES.STEP_NAME_TOO_LONG
  )
  async update(
    @Param('taskId') taskId: string,
    @Param('stepId') stepId: string,
    @Body() updateStepDto: UpdateStepDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<StepResponseDto>> {
    const result = await this.adminStepService.update(taskId, stepId, updateStepDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Sắp xếp lại thứ tự các bước
   * @param taskId ID của task
   * @param reorderStepsDto Dữ liệu sắp xếp lại
   * @param employee
   * @returns Danh sách các bước đã sắp xếp lại
   */
  @Put('reorder')
  @ApiOperation({ summary: 'Sắp xếp lại thứ tự các bước' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách các bước đã sắp xếp lại',
    schema: ApiResponseDto.getArraySchema(StepResponseDto)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_REORDER_FAILED,
    ADMIN_STEP_ERROR_CODES.STEP_NOT_FOUND,
    ADMIN_STEP_ERROR_CODES.STEP_ORDER_INVALID
  )
  async reorder(
    @Param('taskId') taskId: string,
    @Body() reorderStepsDto: ReorderStepsDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<StepResponseDto[]>> {
    const result = await this.adminStepService.reorder(taskId, reorderStepsDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa bước
   * @param taskId ID của task
   * @param stepId ID của bước
   * @param employee
   * @returns Thông báo xóa thành công
   */
  @Delete(':stepId')
  @ApiOperation({ summary: 'Xóa bước' })
  @ApiParam({ name: 'taskId', description: 'ID của task' })
  @ApiParam({ name: 'stepId', description: 'ID của bước' })
  @ApiResponse({
    status: 200,
    description: 'Thông báo xóa thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  @ApiErrorResponseWithExamples(
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_NOT_FOUND,
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_DELETE_FAILED,
    ADMIN_STEP_ERROR_CODES.ADMIN_STEP_UNAUTHORIZED
  )
  async remove(
    @Param('taskId') taskId: string,
    @Param('stepId') stepId: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<null>> {
    await this.adminStepService.remove(taskId, stepId);
    return ApiResponseDto.success(null, 'Xóa bước thành công');
  }
}
