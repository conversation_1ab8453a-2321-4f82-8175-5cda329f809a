import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho các thao tác liên quan đến connection
 * Phạm vi mã lỗi: 10200 - 10299
 */
export const CONNECTION_ERROR_CODES = {
  // Lỗi chung
  CONNECTION_NOT_FOUND: new ErrorCode(10200, 'Không tìm thấy kết nối', HttpStatus.NOT_FOUND),
  CONNECTION_CREATION_FAILED: new ErrorCode(10201, 'Tạo kết nối thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  CONNECTION_UPDATE_FAILED: new ErrorCode(10202, 'Cập nhật kết nối thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  CONNECTION_DELETE_FAILED: new ErrorCode(10203, '<PERSON><PERSON><PERSON> kết nối thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  CONNECTION_FETCH_FAILED: new ErrorCode(10204, 'Lấy thông tin kết nối thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi xác thực và phân quyền
  CONNECTION_UNAUTHORIZED: new ErrorCode(10210, 'Không có quyền truy cập kết nối này', HttpStatus.FORBIDDEN),
  
  // Lỗi dữ liệu
  CONNECTION_INVALID_DATA: new ErrorCode(10220, 'Dữ liệu kết nối không hợp lệ', HttpStatus.BAD_REQUEST),
  CONNECTION_SAME_STEP: new ErrorCode(10221, 'Không thể kết nối một bước với chính nó', HttpStatus.BAD_REQUEST),
  CONNECTION_FIELD_REQUIRED: new ErrorCode(10222, 'Tên trường kết nối là bắt buộc', HttpStatus.BAD_REQUEST),
  CONNECTION_FIELD_TOO_LONG: new ErrorCode(10223, 'Tên trường kết nối quá dài (tối đa 255 ký tự)', HttpStatus.BAD_REQUEST),
  CONNECTION_STEP_NOT_FOUND: new ErrorCode(10224, 'Không tìm thấy bước để kết nối', HttpStatus.NOT_FOUND),
  CONNECTION_STEPS_DIFFERENT_TASKS: new ErrorCode(10225, 'Các bước phải thuộc cùng một nhiệm vụ', HttpStatus.BAD_REQUEST),
  
  // Lỗi logic
  CONNECTION_CIRCULAR_REFERENCE: new ErrorCode(10230, 'Phát hiện tham chiếu vòng tròn trong kết nối', HttpStatus.BAD_REQUEST),
  CONNECTION_DUPLICATE: new ErrorCode(10231, 'Kết nối này đã tồn tại', HttpStatus.BAD_REQUEST),
  CONNECTION_INCOMPATIBLE_TYPES: new ErrorCode(10232, 'Kiểu dữ liệu của các trường kết nối không tương thích', HttpStatus.BAD_REQUEST),
  
  // Lỗi giới hạn
  CONNECTION_LIMIT_EXCEEDED: new ErrorCode(10240, 'Đã vượt quá giới hạn số lượng kết nối', HttpStatus.BAD_REQUEST),
};
