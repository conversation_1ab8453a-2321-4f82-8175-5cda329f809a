import { JwtUserGuard } from '@/modules/auth/guards';
import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags, getSchemaPath } from '@nestjs/swagger';
import { UserTaskService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CreateUserTaskDto, QueryUserTaskDto, UpdateUserTaskDto, UserTaskResponseDto } from '../dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response/api-response-dto';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { USER_TASK_ERROR_CODES } from '@modules/task/user/exceptions';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';

/**
 * Controller xử lý các endpoint liên quan đến task của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_TASK)
@ApiExtraModels(ApiResponseDto, UserTaskResponseDto, PaginatedResult, ApiErrorResponseDto)
@Controller('user/tasks')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserTaskController {
  /**
   * Constructor
   * @param userTaskService Service xử lý logic liên quan đến task của người dùng
   */
  constructor(private readonly userTaskService: UserTaskService) {}

  /**
   * Tạo mới nhiệm vụ
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới nhiệm vụ' })
  @ApiResponse({
    status: 201,
    description: 'Nhiệm vụ đã được tạo thành công',
    schema: ApiResponseDto.getSchema(UserTaskResponseDto),
  })
  @ApiErrorResponse(
    USER_TASK_ERROR_CODES.USER_TASK_CREATION_FAILED,
    USER_TASK_ERROR_CODES.USER_TASK_INVALID_DATA,
    USER_TASK_ERROR_CODES.USER_TASK_AGENT_NOT_FOUND
  )
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateUserTaskDto,
  ): Promise<ApiResponseDto<UserTaskResponseDto>> {
    const result = await this.userTaskService.create(user.id, createDto);
    return ApiResponseDto.created(result, 'Tạo mới nhiệm vụ thành công');
  }

  /**
   * Cập nhật nhiệm vụ
   */
  @Put(':taskId')
  @ApiOperation({ summary: 'Cập nhật nhiệm vụ' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Nhiệm vụ đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(UserTaskResponseDto),
  })
  @ApiErrorResponse(
    USER_TASK_ERROR_CODES.USER_TASK_NOT_FOUND,
    USER_TASK_ERROR_CODES.USER_TASK_UPDATE_FAILED,
    USER_TASK_ERROR_CODES.USER_TASK_INVALID_DATA,
    USER_TASK_ERROR_CODES.USER_TASK_INVALID_STATUS
  )
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Body() updateDto: UpdateUserTaskDto,
  ): Promise<ApiResponseDto<UserTaskResponseDto>> {
    const result = await this.userTaskService.update(taskId, user.id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật nhiệm vụ thành công');
  }

  /**
   * Lấy thông tin nhiệm vụ theo ID
   */
  @Get(':taskId')
  @ApiOperation({ summary: 'Lấy thông tin nhiệm vụ theo ID' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin nhiệm vụ',
    schema: ApiResponseDto.getSchema(UserTaskResponseDto),
  })
  @ApiErrorResponse(
    USER_TASK_ERROR_CODES.USER_TASK_NOT_FOUND,
    USER_TASK_ERROR_CODES.USER_TASK_FETCH_FAILED
  )
  async findById(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
  ): Promise<ApiResponseDto<UserTaskResponseDto>> {
    const result = await this.userTaskService.findById(taskId, user.id);
    return ApiResponseDto.success(result, 'Lấy thông tin nhiệm vụ thành công');
  }

  /**
   * Lấy danh sách nhiệm vụ của người dùng
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách nhiệm vụ của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách nhiệm vụ với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(UserTaskResponseDto),
  })
  @ApiErrorResponse(
    USER_TASK_ERROR_CODES.USER_TASK_FETCH_FAILED
  )
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: QueryUserTaskDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserTaskResponseDto>>> {
    const result = await this.userTaskService.findAll(user.id, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách nhiệm vụ thành công');
  }

  /**
   * Xóa nhiệm vụ
   */
  @Delete(':taskId')
  @ApiOperation({ summary: 'Xóa nhiệm vụ' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Nhiệm vụ đã được xóa thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'boolean',
              example: true
            }
          }
        }
      ]
    }
  })
  @ApiErrorResponse(
    USER_TASK_ERROR_CODES.USER_TASK_NOT_FOUND,
    USER_TASK_ERROR_CODES.USER_TASK_DELETE_FAILED
  )
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.userTaskService.delete(taskId, user.id);
    return ApiResponseDto.success(result, 'Xóa nhiệm vụ thành công');
  }

  /**
   * Gửi duyệt nhiệm vụ
   */
  @Patch(':taskId/accept')
  @ApiOperation({ summary: 'Gửi duyệt nhiệm vụ' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Nhiệm vụ được gửi duyệt thanh cong',
    schema: ApiResponseDto.getSchema(UserTaskResponseDto),
  })
  @ApiErrorResponse(
    USER_TASK_ERROR_CODES.USER_TASK_NOT_FOUND,
    USER_TASK_ERROR_CODES.USER_TASK_ACCEPT_FAILED
  )
  async accept(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
  ): Promise<ApiResponseDto<void>> {
    const result = await this.userTaskService.submitForApproval(taskId, user.id);
    return ApiResponseDto.success(result, 'Gửi duyệt nhiệm vụ thanh cong');
  }
}
