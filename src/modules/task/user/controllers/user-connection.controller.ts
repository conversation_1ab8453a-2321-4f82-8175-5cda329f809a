import { JwtUserGuard } from '@/modules/auth/guards';
import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserConnectionService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CreateUserStepConnectionDto, UpdateUserStepConnectionDto, QueryUserStepConnectionDto, UserStepConnectionResponseDto } from '../dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response/api-response-dto';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { USER_CONNECTION_ERROR_CODES } from '@modules/task/user/exceptions';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';

/**
 * Controller xử lý các endpoint liên quan đến kết nối giữa các bước trong task của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_TASK_STEP_CONNECTION)
@ApiExtraModels(ApiResponseDto, UserStepConnectionResponseDto, PaginatedResult, ApiErrorResponseDto)
@Controller('user/tasks/:taskId/step-connections')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserConnectionController {
  /**
   * Constructor
   * @param userConnectionService Service xử lý logic liên quan đến kết nối giữa các bước trong task của người dùng
   */
  constructor(private readonly userConnectionService: UserConnectionService) {}

  /**
   * Tạo mới kết nối giữa các bước
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo mới kết nối giữa các bước',
    requestBody: {
      content: {
        'application/json': {
          example: {
            fromStepId: '123e4567-e89b-12d3-a456-426614174000',
            toStepId: '123e4567-e89b-12d3-a456-426614174001',
            outputField: 'emailContent',
            inputField: 'content'
          }
        }
      }
    }
  })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiResponse({
    status: 201,
    description: 'Kết nối đã được tạo thành công',
    schema: ApiResponseDto.getSchema(UserStepConnectionResponseDto),
  })
  @ApiErrorResponse(
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_CREATION_FAILED,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_INVALID_DATA,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_SAME_STEP,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_STEP_NOT_FOUND,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_CIRCULAR
  )
  async create(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Body() createDto: CreateUserStepConnectionDto
  ): Promise<ApiResponseDto<UserStepConnectionResponseDto>> {
    // Đảm bảo taskId trong DTO khớp với taskId trong URL
    createDto.taskId = taskId;
    const result = await this.userConnectionService.create(user.id, createDto);
    return ApiResponseDto.created(result, 'Tạo mới kết nối thành công');
  }

  /**
   * Cập nhật kết nối giữa các bước
   */
  @Put(':connectionId')
  @ApiOperation({ summary: 'Cập nhật kết nối giữa các bước' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiParam({ name: 'connectionId', description: 'ID của kết nối', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Kết nối đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(UserStepConnectionResponseDto),
  })
  @ApiErrorResponse(
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_NOT_FOUND,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_UPDATE_FAILED,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_INVALID_DATA,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_SAME_STEP,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_STEP_NOT_FOUND,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_CIRCULAR
  )
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Param('connectionId') connectionId: string,
    @Body() updateDto: UpdateUserStepConnectionDto
  ): Promise<ApiResponseDto<UserStepConnectionResponseDto>> {
    const result = await this.userConnectionService.update(connectionId, taskId, user.id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật kết nối thành công');
  }

  /**
   * Lấy thông tin kết nối theo ID
   */
  @Get(':connectionId')
  @ApiOperation({ summary: 'Lấy thông tin kết nối theo ID' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiParam({ name: 'connectionId', description: 'ID của kết nối', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin kết nối',
    schema: ApiResponseDto.getSchema(UserStepConnectionResponseDto),
  })
  @ApiErrorResponse(
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_NOT_FOUND,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_FETCH_FAILED
  )
  async findById(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Param('connectionId') connectionId: string
  ): Promise<ApiResponseDto<UserStepConnectionResponseDto>> {
    const result = await this.userConnectionService.findById(connectionId, taskId, user.id);
    return ApiResponseDto.success(result, 'Lấy thông tin kết nối thành công');
  }

  /**
   * Lấy danh sách kết nối của nhiệm vụ
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách kết nối của nhiệm vụ' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kết nối với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(UserStepConnectionResponseDto),
  })
  @ApiErrorResponse(
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_FETCH_FAILED
  )
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Query() queryDto: QueryUserStepConnectionDto
  ): Promise<ApiResponseDto<PaginatedResult<UserStepConnectionResponseDto>>> {
    // Đảm bảo taskId trong DTO khớp với taskId trong URL
    queryDto.taskId = taskId;
    const result = await this.userConnectionService.findAll(taskId, user.id, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách kết nối thành công');
  }

  /**
   * Xóa kết nối
   */
  @Delete(':connectionId')
  @ApiOperation({ summary: 'Xóa kết nối' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiParam({ name: 'connectionId', description: 'ID của kết nối', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Kết nối đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Boolean)
  })
  @ApiErrorResponse(
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_NOT_FOUND,
    USER_CONNECTION_ERROR_CODES.USER_CONNECTION_DELETE_FAILED
  )
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Param('connectionId') connectionId: string
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.userConnectionService.delete(connectionId, taskId, user.id);
    return ApiResponseDto.success(result, 'Xóa kết nối thành công');
  }
}
