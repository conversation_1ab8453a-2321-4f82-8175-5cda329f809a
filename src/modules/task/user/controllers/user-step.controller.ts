import { JwtUserGuard } from '@/modules/auth/guards';
import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserStepService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CreateUserStepDto, QueryUserStepDto, UpdateUserStepDto, UserStepResponseDto } from '../dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response/api-response-dto';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { USER_STEP_ERROR_CODES } from '@modules/task/user/exceptions';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { ApiStepExamples } from '../helpers/swagger-examples';

/**
 * Controller xử lý các endpoint liên quan đến các bước trong task của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_TASK_STEP)
@ApiExtraModels(ApiResponseDto, UserStepResponseDto, PaginatedResult, ApiErrorResponseDto)
@Controller('user/tasks/:taskId/steps')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserStepController {
  /**
   * Constructor
   * @param userStepService Service xử lý logic liên quan đến các bước trong task của người dùng
   */
  constructor(private readonly userStepService: UserStepService) {}

  /**
   * Tạo mới bước trong nhiệm vụ
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới bước trong nhiệm vụ' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiResponse({
    status: 201,
    description: 'Bước đã được tạo thành công',
    schema: ApiResponseDto.getSchema(UserStepResponseDto),
  })
  @ApiErrorResponse(
    USER_STEP_ERROR_CODES.USER_STEP_CREATION_FAILED,
    USER_STEP_ERROR_CODES.USER_STEP_INVALID_DATA,
    USER_STEP_ERROR_CODES.USER_STEP_INVALID_TYPE,
    USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
    USER_STEP_ERROR_CODES.USER_STEP_ORDER_INVALID,
    USER_STEP_ERROR_CODES.USER_STEP_ORDER_DUPLICATE,
    USER_STEP_ERROR_CODES.USER_STEP_LIMIT_EXCEEDED
  )
  @ApiStepExamples()
  async create(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Body() createDto: CreateUserStepDto,
  ): Promise<ApiResponseDto<UserStepResponseDto>> {
    // Đảm bảo taskId trong DTO khớp với taskId trong URL
    createDto.taskId = taskId;
    const result = await this.userStepService.create(user.id, createDto);
    return ApiResponseDto.created(result, 'Tạo mới bước thành công');
  }

  /**
   * Cập nhật bước trong nhiệm vụ
   */
  @Put(':stepId')
  @ApiOperation({ summary: 'Cập nhật bước trong nhiệm vụ' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiParam({ name: 'stepId', description: 'ID của bước', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Bước đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(UserStepResponseDto),
  })
  @ApiErrorResponse(
    USER_STEP_ERROR_CODES.USER_STEP_NOT_FOUND,
    USER_STEP_ERROR_CODES.USER_STEP_UPDATE_FAILED,
    USER_STEP_ERROR_CODES.USER_STEP_INVALID_DATA,
    USER_STEP_ERROR_CODES.USER_STEP_INVALID_TYPE,
    USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
    USER_STEP_ERROR_CODES.USER_STEP_ORDER_INVALID,
    USER_STEP_ERROR_CODES.USER_STEP_ORDER_DUPLICATE
  )
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Param('stepId') stepId: string,
    @Body() updateDto: UpdateUserStepDto,
  ): Promise<ApiResponseDto<UserStepResponseDto>> {
    const result = await this.userStepService.update(stepId, taskId, user.id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật bước thành công');
  }

  /**
   * Lấy thông tin bước theo ID
   */
  @Get(':stepId')
  @ApiOperation({ summary: 'Lấy thông tin bước theo ID' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiParam({ name: 'stepId', description: 'ID của bước', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin bước',
    schema: ApiResponseDto.getSchema(UserStepResponseDto),
  })
  @ApiErrorResponse(
    USER_STEP_ERROR_CODES.USER_STEP_NOT_FOUND,
    USER_STEP_ERROR_CODES.USER_STEP_FETCH_FAILED
  )
  async findById(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Param('stepId') stepId: string,
  ): Promise<ApiResponseDto<UserStepResponseDto>> {
    const result = await this.userStepService.findById(stepId, taskId, user.id);
    return ApiResponseDto.success(result, 'Lấy thông tin bước thành công');
  }

  /**
   * Lấy danh sách bước của nhiệm vụ
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách bước của nhiệm vụ' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách bước với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(UserStepResponseDto),
  })
  @ApiErrorResponse(
    USER_STEP_ERROR_CODES.USER_STEP_FETCH_FAILED
  )
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Query() queryDto: QueryUserStepDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserStepResponseDto>>> {
    // Đảm bảo taskId trong DTO khớp với taskId trong URL
    queryDto.taskId = taskId;
    const result = await this.userStepService.findAll(taskId, user.id, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách bước thành công');
  }

  /**
   * Xóa bước
   */
  @Delete(':stepId')
  @ApiOperation({ summary: 'Xóa bước' })
  @ApiParam({ name: 'taskId', description: 'ID của nhiệm vụ', type: 'string' })
  @ApiParam({ name: 'stepId', description: 'ID của bước', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Bước đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Boolean)
  })
  @ApiErrorResponse(
    USER_STEP_ERROR_CODES.USER_STEP_NOT_FOUND,
    USER_STEP_ERROR_CODES.USER_STEP_DELETE_FAILED
  )
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('taskId') taskId: string,
    @Param('stepId') stepId: string,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.userStepService.delete(stepId, taskId, user.id);
    return ApiResponseDto.success(result, 'Xóa bước thành công');
  }
}