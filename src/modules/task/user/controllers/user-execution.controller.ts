import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { UserExecutionService } from '../services';
import { QueryUserTaskExecutionDto } from '@modules/task/user/dto';
import { UserTaskExecutionResponseDto } from '@modules/task/user/dto';
import { USER_EXECUTION_ERROR_CODES } from '@modules/task/user/exceptions';

/**
 * Controller xử lý các endpoint liên quan đến phiên thực thi nhiệm vụ của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_TASK_EXECUTION)
@ApiExtraModels(ApiResponseDto, UserTaskExecutionResponseDto, PaginatedResult)
@Controller('user/task-executions')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserExecutionController {
  /**
   * Constructor
   * @param userExecutionService Service xử lý logic liên quan đến phiên thực thi nhiệm vụ
   */
  constructor(private readonly userExecutionService: UserExecutionService) {}

  /**
   * Lấy danh sách phiên thực thi nhiệm vụ
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách phiên thực thi nhiệm vụ' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách phiên thực thi với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(UserTaskExecutionResponseDto),
  })
  @ApiErrorResponse(
    USER_EXECUTION_ERROR_CODES.USER_EXECUTION_FETCH_FAILED
  )
  async findAll(
    @Query() queryDto: QueryUserTaskExecutionDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserTaskExecutionResponseDto>>> {
    const result = await this.userExecutionService.findAll(queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách phiên thực thi thành công');
  }

  /**
   * Lấy thông tin chi tiết phiên thực thi
   */
  @Get(':executionId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết phiên thực thi' })
  @ApiParam({ name: 'executionId', description: 'ID của phiên thực thi', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết phiên thực thi',
    schema: ApiResponseDto.getSchema(UserTaskExecutionResponseDto),
  })
  @ApiErrorResponse(
    USER_EXECUTION_ERROR_CODES.USER_EXECUTION_NOT_FOUND,
    USER_EXECUTION_ERROR_CODES.USER_EXECUTION_FETCH_FAILED
  )
  async findById(
    @Param('executionId') executionId: string,
  ): Promise<ApiResponseDto<UserTaskExecutionResponseDto>> {
    const result = await this.userExecutionService.findById(executionId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết phiên thực thi thành công');
  }
}
