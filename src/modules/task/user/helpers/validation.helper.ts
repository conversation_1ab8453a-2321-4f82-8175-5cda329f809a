import { Injectable } from '@nestjs/common';
import { AppException } from '@/common/exceptions/app.exception';
import { USER_TASK_ERROR_CODES } from '@modules/task/user/exceptions';
import { USER_STEP_ERROR_CODES } from '@modules/task/user/exceptions';
import { UserTask, UserStep, UserStepConnection } from '@modules/task/entities';
import { TaskStatus } from '@modules/task/enums';
import { StepType } from '@modules/task/interfaces/step-config.interface';
import { TASK_CONSTANTS } from '@modules/task/constants';
import { DataSource } from 'typeorm';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { CONNECTION_ERROR_CODES } from '@modules/task/exceptions/connection.exceptions';

/**
 * Helper xử lý validation cho module User Task
 */
@Injectable()
export class ValidationHelper {
  constructor(private dataSource: DataSource) {}
  /**
   * <PERSON><PERSON><PERSON> tra nhiệm vụ có tồn tại không
   * @param task Nhiệm vụ cần kiểm tra
   * @throws AppException nếu nhiệm vụ không tồn tại
   */
  validateTaskExists(task: UserTask | null): asserts task is UserTask {
    if (!task) {
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_NOT_FOUND,
        'Không tìm thấy nhiệm vụ',
      );
    }
  }

  /**
   * Kiểm tra nhiệm vụ có bị xóa không
   * @param task Nhiệm vụ cần kiểm tra
   * @throws AppException nếu nhiệm vụ đã bị xóa
   */
  validateTaskNotDeleted(task: UserTask | null): void {
    // Đảm bảo task tồn tại
    this.validateTaskExists(task);
    if (task.deletedAt) {
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_NOT_FOUND,
        'Nhiệm vụ đã bị xóa',
      );
    }
  }

  /**
   * Kiểm tra người dùng có quyền truy cập nhiệm vụ không
   * @param task Nhiệm vụ cần kiểm tra
   * @param userId ID của người dùng
   * @throws AppException nếu người dùng không có quyền
   */
  validateTaskOwnership(task: UserTask | null, userId: number): void {
    // Đảm bảo task tồn tại
    this.validateTaskExists(task);
    if (task.userId !== userId) {
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_INVALID_OWNER,
        'Bạn không phải là chủ sở hữu của nhiệm vụ này',
      );
    }
  }

  /**
   * Kiểm tra trạng thái nhiệm vụ có cho phép cập nhật không
   * @param task Nhiệm vụ cần kiểm tra
   * @throws AppException nếu trạng thái không cho phép cập nhật
   */
  validateTaskStatusForUpdate(task: UserTask | null): void {
    // Đảm bảo task tồn tại
    this.validateTaskExists(task);
    if (task.status === TaskStatus.APPROVED) {
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_INVALID_STATUS,
        'Nhiệm vụ đã được phê duyệt, không thể thay đổi',
      );
    }

    if (task.status === TaskStatus.REJECTED) {
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_INVALID_STATUS,
        'Nhiệm vụ đã bị từ chối, không thể thay đổi',
      );
    }
  }

  /**
   * Kiểm tra tên nhiệm vụ có hợp lệ không
   * @param taskName Tên nhiệm vụ cần kiểm tra
   * @throws AppException nếu tên nhiệm vụ không hợp lệ
   */
  validateTaskName(taskName: string): void {
    if (!taskName || taskName.trim() === '') {
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_NAME_REQUIRED,
        'Tên nhiệm vụ là bắt buộc',
      );
    }

    if (taskName.length > 255) {
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_NAME_TOO_LONG,
        'Tên nhiệm vụ quá dài (tối đa 255 ký tự)',
      );
    }
  }

  /**
   * Kiểm tra nhiệm vụ có đang hoạt động không
   * @param task Nhiệm vụ cần kiểm tra
   * @throws AppException nếu nhiệm vụ không hoạt động
   */
  validateTaskActive(task: UserTask | null): void {
    // Đảm bảo task tồn tại
    this.validateTaskExists(task);
    if (!task.active) {
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_INVALID_STATUS,
        'Nhiệm vụ không hoạt động',
      );
    }
  }

  /**
   * Kiểm tra đầy đủ nhiệm vụ (tồn tại, không bị xóa, đang hoạt động)
   * @param task Nhiệm vụ cần kiểm tra
   * @param userId ID của người dùng (tùy chọn)
   * @throws AppException nếu nhiệm vụ không hợp lệ
   */
  validateTaskFullCheck(task: UserTask | null, userId?: number): void {
    this.validateTaskExists(task);
    this.validateTaskNotDeleted(task);
    this.validateTaskActive(task);

    if (userId !== undefined) {
      this.validateTaskOwnership(task, userId);
    }
  }

  // ===== STEP VALIDATION METHODS =====

  /**
   * Kiểm tra bước có tồn tại không
   * @param step Bước cần kiểm tra
   * @throws AppException nếu bước không tồn tại
   */
  validateStepExists(step: UserStep | null): asserts step is UserStep {
    if (!step) {
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_NOT_FOUND,
        'Không tìm thấy bước',
      );
    }
  }

  /**
   * Kiểm tra tên bước có hợp lệ không
   * @param stepName Tên bước cần kiểm tra
   * @throws AppException nếu tên bước không hợp lệ
   */
  validateStepName(stepName: string): void {
    if (!stepName || stepName.trim() === '') {
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_NAME_REQUIRED,
        'Tên bước là bắt buộc',
      );
    }

    if (stepName.length > 255) {
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_NAME_TOO_LONG,
        'Tên bước quá dài (tối đa 255 ký tự)',
      );
    }
  }

  /**
   * Kiểm tra loại bước có hợp lệ không
   * @param stepType Loại bước cần kiểm tra
   * @throws AppException nếu loại bước không hợp lệ
   */
  validateStepType(stepType: StepType): void {
    const validStepTypes: StepType[] = [
      StepType.PROMPT,
      StepType.GOOGLE_SHEET,
      StepType.GOOGLE_DOC,
      StepType.GOOGLE_CALENDAR,
      StepType.EMAIL,
      StepType.FACEBOOK_PAGE,
      StepType.GEN_IMAGE,
      StepType.GEN_VIDEO,
      StepType.AGENT,
    ];

    if (!validStepTypes.includes(stepType)) {
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_INVALID_TYPE,
        `Loại bước không hợp lệ: ${stepType}`,
      );
    }
  }

  /**
   * Kiểm tra cấu hình bước có hợp lệ không
   * @param stepType Loại bước
   * @param stepConfig Cấu hình bước
   * @throws AppException nếu cấu hình bước không hợp lệ
   */
  async validateStepConfig(
    stepType: StepType,
    stepConfig?: Record<string, any> | null,
  ): Promise<void> {
    if (!stepConfig) {
      return; // Cấu hình có thể null, không bắt buộc
    }

    // Kiểm tra stepType trong cấu hình phải khớp với stepType của bước
    if (stepConfig.stepType && stepConfig.stepType !== stepType) {
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
        `Loại bước trong cấu hình (${stepConfig.stepType}) không khớp với loại bước (${stepType})`,
      );
    }

    // Đảm bảo stepType được đặt trong cấu hình
    stepConfig.stepType = stepType;

    // Kiểm tra cấu hình theo từng loại bước
    switch (stepType) {
      case StepType.PROMPT:
        // Kiểm tra trường promptText
        if (!stepConfig.promptText) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước prompt phải có trường promptText',
          );
        }

        if (
          !stepConfig.inputType ||
          !['text', 'number', 'date', 'boolean'].includes(stepConfig.inputType)
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước prompt phải có trường inputType hợp lệ (text, number, date, boolean)',
          );
        }
        if (
          stepConfig.required === undefined ||
          typeof stepConfig.required !== 'boolean'
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước prompt phải có trường required kiểu boolean',
          );
        }
        break;

      case StepType.GOOGLE_SHEET:
        if (!stepConfig.sheetId) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_sheet phải có trường sheetId',
          );
        }
        if (!stepConfig.range) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_sheet phải có trường range',
          );
        }
        if (
          !stepConfig.action ||
          !['read', 'write'].includes(stepConfig.action)
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_sheet phải có trường action hợp lệ (read, write)',
          );
        }
        if (!stepConfig.credentialId) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_sheet phải có trường credentialId',
          );
        }
        break;

      case StepType.GOOGLE_DOC:
        if (
          !stepConfig.action ||
          !['create', 'append', 'replace'].includes(stepConfig.action)
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_doc phải có trường action hợp lệ (create, append, replace)',
          );
        }
        if (!stepConfig.content) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_doc phải có trường content',
          );
        }
        if (!stepConfig.credentialId) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_doc phải có trường credentialId',
          );
        }
        // docId chỉ bắt buộc khi action là append hoặc replace
        if (
          ['append', 'replace'].includes(stepConfig.action) &&
          !stepConfig.docId
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            `Cấu hình bước google_doc với action ${stepConfig.action} phải có trường docId`,
          );
        }
        break;

      case StepType.GOOGLE_CALENDAR:
        if (!stepConfig.calendarId) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_calendar phải có trường calendarId',
          );
        }
        if (
          !stepConfig.action ||
          !['create_event', 'update_event', 'delete_event'].includes(
            stepConfig.action,
          )
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_calendar phải có trường action hợp lệ (create_event, update_event, delete_event)',
          );
        }
        if (!stepConfig.eventDetails) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_calendar phải có trường eventDetails',
          );
        }
        // Kiểm tra eventDetails
        const { eventDetails } = stepConfig;
        if (
          ['update_event', 'delete_event'].includes(stepConfig.action) &&
          !eventDetails.eventId
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            `Cấu hình bước google_calendar với action ${stepConfig.action} phải có trường eventDetails.eventId`,
          );
        }
        if (
          stepConfig.action === 'create_event' ||
          stepConfig.action === 'update_event'
        ) {
          if (!eventDetails.summary) {
            throw new AppException(
              USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
              'Cấu hình bước google_calendar phải có trường eventDetails.summary',
            );
          }
          if (!eventDetails.start || !eventDetails.start.dateTime) {
            throw new AppException(
              USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
              'Cấu hình bước google_calendar phải có trường eventDetails.start.dateTime',
            );
          }
          if (!eventDetails.end || !eventDetails.end.dateTime) {
            throw new AppException(
              USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
              'Cấu hình bước google_calendar phải có trường eventDetails.end.dateTime',
            );
          }
        }
        if (!stepConfig.credentialId) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước google_calendar phải có trường credentialId',
          );
        }
        break;

      case StepType.EMAIL:
        if (
          !stepConfig.to ||
          !Array.isArray(stepConfig.to) ||
          stepConfig.to.length === 0
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước email phải có trường to là một mảng không rỗng',
          );
        }
        if (!stepConfig.subject) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước email phải có trường subject',
          );
        }
        if (!stepConfig.body) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước email phải có trường body',
          );
        }
        break;

      case StepType.FACEBOOK_PAGE:
        if (!stepConfig.pageId) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước facebook_page phải có trường pageId',
          );
        }
        if (
          !stepConfig.action ||
          !['post', 'comment', 'get_insights'].includes(stepConfig.action)
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước facebook_page phải có trường action hợp lệ (post, comment, get_insights)',
          );
        }
        if (!stepConfig.content) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước facebook_page phải có trường content',
          );
        }
        if (!stepConfig.credentialId) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước facebook_page phải có trường credentialId',
          );
        }
        break;

      case StepType.GEN_IMAGE:
        if (!stepConfig.prompt) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước gen_image phải có trường prompt',
          );
        }
        if (!stepConfig.resolution) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước gen_image phải có trường resolution',
          );
        }
        break;

      case StepType.GEN_VIDEO:
        if (!stepConfig.prompt) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước gen_video phải có trường prompt',
          );
        }
        if (
          !stepConfig.duration ||
          typeof stepConfig.duration !== 'number' ||
          stepConfig.duration <= 0
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước gen_video phải có trường duration là số dương',
          );
        }
        if (!stepConfig.resolution) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước gen_video phải có trường resolution',
          );
        }
        break;

      case StepType.AGENT:
        if (!stepConfig.agentId) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước agent phải có trường agentId',
          );
        }
        if (
          !stepConfig.parameters ||
          typeof stepConfig.parameters !== 'object'
        ) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_CONFIG_INVALID,
            'Cấu hình bước agent phải có trường parameters là một đối tượng',
          );
        }

        // Kiểm tra agent có tồn tại không
        await this.validateAgentExists(stepConfig.agentId);
        break;

      default:
        throw new AppException(
          USER_STEP_ERROR_CODES.USER_STEP_INVALID_TYPE,
          `Loại bước không được hỗ trợ: ${stepType}`,
        );
    }
  }

  /**
   * Kiểm tra thứ tự bước có hợp lệ không
   * @param orderIndex Thứ tự bước cần kiểm tra
   * @throws AppException nếu thứ tự bước không hợp lệ
   */
  validateStepOrderIndex(orderIndex: number): void {
    if (orderIndex < 0) {
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_ORDER_INVALID,
        'Thứ tự bước phải lớn hơn hoặc bằng 0',
      );
    }
  }

  /**
   * Kiểm tra số lượng bước trong nhiệm vụ có vượt quá giới hạn không
   * @param currentCount Số lượng bước hiện tại
   * @throws AppException nếu số lượng bước vượt quá giới hạn
   */
  validateStepLimit(currentCount: number): void {
    if (currentCount >= TASK_CONSTANTS.MAX_STEPS_PER_TASK) {
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_LIMIT_EXCEEDED,
        `Đã vượt quá giới hạn số lượng bước trong nhiệm vụ (tối đa ${TASK_CONSTANTS.MAX_STEPS_PER_TASK} bước)`,
      );
    }
  }

  /**
   * Kiểm tra bước có thuộc về nhiệm vụ không
   * @param step Bước cần kiểm tra
   * @param taskId ID của nhiệm vụ
   * @throws AppException nếu bước không thuộc về nhiệm vụ
   */
  validateStepBelongsToTask(step: UserStep | null, taskId: string): void {
    this.validateStepExists(step);
    if (step.taskId !== taskId) {
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_UNAUTHORIZED,
        'Bước không thuộc về nhiệm vụ này',
      );
    }
  }

  /**
   * Kiểm tra đầy đủ bước (tồn tại, thuộc về nhiệm vụ)
   * @param step Bước cần kiểm tra
   * @param taskId ID của nhiệm vụ
   * @throws AppException nếu bước không hợp lệ
   */
  validateStepFullCheck(step: UserStep | null, taskId: string): void {
    this.validateStepExists(step);
    this.validateStepBelongsToTask(step, taskId);
  }

  /**
   * Kiểm tra agent có tồn tại không
   * @param agentId ID của agent cần kiểm tra
   * @throws AppException nếu agent không tồn tại
   */
  async validateAgentExists(agentId: string): Promise<void> {
    // Kiểm tra agent có tồn tại không
    const agent = await this.dataSource
      .createQueryBuilder()
      .select('id')
      .from('agents', 'agent')
      .where('agent.id = :id', { id: agentId })
      .andWhere('agent.deleted_at IS NULL')
      .andWhere('is_for_sale = true')
      .getRawOne();

    if (!agent) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        'Agent không tồn tại hoặc đã bị xóa'
      );
    }
  }

  /**
   * Kiểm tra kết nối có tồn tại không
   * @param connection Kết nối cần kiểm tra
   * @throws AppException nếu kết nối không tồn tại
   */
  validateConnectionExists(connection: UserStepConnection | null): asserts connection is UserStepConnection {
    if (!connection) {
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_NOT_FOUND,
        'Không tìm thấy kết nối',
      );
    }
  }

  /**
   * Kiểm tra kết nối có thuộc về nhiệm vụ không
   * @param connection Kết nối cần kiểm tra
   * @param taskId ID của nhiệm vụ
   * @throws AppException nếu kết nối không thuộc về nhiệm vụ
   */
  validateConnectionBelongsToTask(connection: UserStepConnection, taskId: string): void {
    if (connection.taskId !== taskId) {
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_UNAUTHORIZED,
        'Kết nối không thuộc về nhiệm vụ này',
      );
    }
  }

  /**
   * Kiểm tra đầy đủ kết nối (tồn tại, thuộc về nhiệm vụ)
   * @param connection Kết nối cần kiểm tra
   * @param taskId ID của nhiệm vụ
   * @throws AppException nếu kết nối không hợp lệ
   */
  validateConnectionFullCheck(connection: UserStepConnection | null, taskId: string): void {
    this.validateConnectionExists(connection);
    this.validateConnectionBelongsToTask(connection, taskId);
  }

  /**
   * Kiểm tra tên trường kết nối có hợp lệ không
   * @param fieldName Tên trường cần kiểm tra
   * @throws AppException nếu tên trường không hợp lệ
   */
  validateConnectionField(fieldName: string): void {
    if (!fieldName || fieldName.trim() === '') {
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_FIELD_REQUIRED,
        'Tên trường kết nối là bắt buộc',
      );
    }

    if (fieldName.length > 255) {
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_FIELD_TOO_LONG,
        'Tên trường kết nối quá dài (tối đa 255 ký tự)',
      );
    }
  }

  /**
   * Kiểm tra step nguồn và đích có hợp lệ không
   * @param fromStepId ID của step nguồn
   * @param toStepId ID của step đích
   * @throws AppException nếu step nguồn và đích không hợp lệ
   */
  validateConnectionSteps(fromStepId: string, toStepId: string): void {
    if (fromStepId === toStepId) {
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_SAME_STEP,
        'Không thể kết nối một bước với chính nó',
      );
    }
  }

  /**
   * Kiểm tra các step có thuộc cùng một nhiệm vụ không
   * @param fromStep Step nguồn
   * @param toStep Step đích
   * @throws AppException nếu các step không thuộc cùng một nhiệm vụ
   */
  validateStepsSameTask(fromStep: UserStep, toStep: UserStep): void {
    if (fromStep.taskId !== toStep.taskId) {
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_STEPS_DIFFERENT_TASKS,
        'Các bước phải thuộc cùng một nhiệm vụ',
      );
    }
  }

  /**
   * Kiểm tra kết nối đã tồn tại chưa
   * @param exists Kết quả kiểm tra từ repository
   * @throws AppException nếu kết nối đã tồn tại
   */
  validateConnectionNotDuplicate(exists: boolean): void {
    if (exists) {
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_DUPLICATE,
        'Kết nối này đã tồn tại',
      );
    }
  }

  /**
   * Kiểm tra kết nối không tạo vòng tròn
   * @param connections Danh sách kết nối hiện có
   * @param fromStepId ID của step nguồn
   * @param toStepId ID của step đích
   * @throws AppException nếu phát hiện tham chiếu vòng tròn
   */
  validateNoCircularReference(
    connections: UserStepConnection[],
    fromStepId: string,
    toStepId: string
  ): void {
    // Xây dựng đồ thị kết nối
    const graph: Record<string, string[]> = {};

    // Thêm tất cả các kết nối hiện có vào đồ thị
    for (const conn of connections) {
      if (!graph[conn.fromStepId]) {
        graph[conn.fromStepId] = [];
      }
      graph[conn.fromStepId].push(conn.toStepId);
    }

    // Thêm kết nối mới vào đồ thị
    if (!graph[fromStepId]) {
      graph[fromStepId] = [];
    }
    graph[fromStepId].push(toStepId);

    // Kiểm tra chu trình bằng DFS
    const visited: Record<string, boolean> = {};
    const recStack: Record<string, boolean> = {};

    const isCyclic = (node: string): boolean => {
      if (!visited[node]) {
        visited[node] = true;
        recStack[node] = true;

        const neighbors = graph[node] || [];
        for (const neighbor of neighbors) {
          if (!visited[neighbor] && isCyclic(neighbor)) {
            return true;
          } else if (recStack[neighbor]) {
            return true;
          }
        }
      }

      recStack[node] = false;
      return false;
    };

    // Kiểm tra từ step nguồn
    if (isCyclic(fromStepId)) {
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_CIRCULAR_REFERENCE,
        'Phát hiện tham chiếu vòng tròn trong kết nối',
      );
    }
  }
}
