import { ApiBody } from '@nestjs/swagger';

/**
 * Decorator để hiển thị các ví dụ về bước trong Swagger
 */
export const ApiStepExamples = () => {
  return ApiBody({
    description: 'Dữ liệu tạo bước',
    examples: {
      prompt: {
        summary: 'Tạo bước loại prompt',
        value: {
          stepName: 'Bước nhập thông tin',
          stepDescription: 'Bước này yêu cầu người dùng nhập thông tin',
          stepType: 'prompt',
          stepConfig: {
            stepType: 'prompt',
            promptText: 'Nhập thông tin khách hàng',
            inputType: 'text',
            required: true,
            defaultValue: ''
          }
        }
      },
      google_sheet: {
        summary: 'Tạo bước loại google_sheet',
        value: {
          stepName: 'Bước lấy dữ liệu từ Google Sheet',
          stepDescription: 'Bước này sẽ lấy dữ liệ<PERSON> từ Google Sheet',
          stepType: 'google_sheet',
          stepConfig: {
            stepType: 'google_sheet',
            sheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
            range: 'Sheet1!A1:B10',
            action: 'read',
            credentialId: '123e4567-e89b-12d3-a456-426614174000'
          }
        }
      },
      google_doc: {
        summary: 'Tạo bước loại google_doc',
        value: {
          stepName: 'Bước tạo tài liệu Google Doc',
          stepDescription: 'Bước này sẽ tạo hoặc cập nhật tài liệu Google Doc',
          stepType: 'google_doc',
          stepConfig: {
            stepType: 'google_doc',
            action: 'append',
            docId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
            content: 'Nội dung cần thêm vào tài liệu',
            credentialId: '123e4567-e89b-12d3-a456-426614174000'
          }
        }
      },
      google_calendar: {
        summary: 'Tạo bước loại google_calendar',
        value: {
          stepName: 'Bước tạo sự kiện lịch',
          stepDescription: 'Bước này sẽ tạo sự kiện trên Google Calendar',
          stepType: 'google_calendar',
          stepConfig: {
            stepType: 'google_calendar',
            calendarId: 'primary',
            action: 'create_event',
            eventDetails: {
              summary: 'Cuộc họp với khách hàng',
              description: 'Thảo luận về dự án mới',
              start: { dateTime: '2023-06-01T09:00:00+07:00', timeZone: 'Asia/Ho_Chi_Minh' },
              end: { dateTime: '2023-06-01T10:00:00+07:00', timeZone: 'Asia/Ho_Chi_Minh' },
              attendees: [{ email: '<EMAIL>' }]
            },
            credentialId: '123e4567-e89b-12d3-a456-426614174000'
          }
        }
      },
      email: {
        summary: 'Tạo bước loại email',
        value: {
          stepName: 'Bước gửi email',
          stepDescription: 'Bước này sẽ gửi email thông báo',
          stepType: 'email',
          stepConfig: {
            stepType: 'email',
            to: ['<EMAIL>'],
            subject: 'Thông báo mới',
            body: 'Xin chào,\n\nĐây là email thông báo từ hệ thống.\n\nTrân trọng,\nAdmin'
          }
        }
      },
      facebook_page: {
        summary: 'Tạo bước loại facebook_page',
        value: {
          stepName: 'Bước đăng bài lên Facebook Page',
          stepDescription: 'Bước này sẽ đăng bài lên trang Facebook',
          stepType: 'facebook_page',
          stepConfig: {
            stepType: 'facebook_page',
            pageId: '123456789012345',
            action: 'post',
            content: 'Thông báo mới từ công ty chúng tôi!',
            credentialId: '123e4567-e89b-12d3-a456-426614174000'
          }
        }
      },
      gen_image: {
        summary: 'Tạo bước loại gen_image',
        value: {
          stepName: 'Bước tạo hình ảnh',
          stepDescription: 'Bước này sẽ tạo hình ảnh từ mô tả',
          stepType: 'gen_image',
          stepConfig: {
            stepType: 'gen_image',
            prompt: 'Một bức tranh phong cảnh về Việt Nam với núi non hùng vĩ',
            resolution: '512x512',
            model: 'dalle-2'
          }
        }
      },
      gen_video: {
        summary: 'Tạo bước loại gen_video',
        value: {
          stepName: 'Bước tạo video',
          stepDescription: 'Bước này sẽ tạo video từ mô tả',
          stepType: 'gen_video',
          stepConfig: {
            stepType: 'gen_video',
            prompt: 'Một video ngắn về thành phố Hồ Chí Minh với các toà nhà cao tầng',
            duration: 15,
            resolution: '1280x720',
            model: 'runway'
          }
        }
      },
      agent: {
        summary: 'Tạo bước loại agent',
        value: {
          stepName: 'Bước xử lý bằng agent',
          stepDescription: 'Bước này sẽ sử dụng agent để xử lý dữ liệu',
          stepType: 'agent',
          stepConfig: {
            stepType: 'agent',
            agentId: '123e4567-e89b-12d3-a456-426614174000',
            parameters: {
              input: 'Dữ liệu đầu vào cho agent',
              options: {
                temperature: 0.7,
                maxTokens: 1000
              }
            }
          }
        }
      }
    }
  });
};
