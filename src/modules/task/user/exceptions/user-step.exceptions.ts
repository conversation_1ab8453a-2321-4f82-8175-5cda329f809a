import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho các thao tác liên quan đến step của người dùng
 * Phạm vi mã lỗi: 10500 - 10599
 */
export const USER_STEP_ERROR_CODES = {
  // Lỗi chung
  USER_STEP_NOT_FOUND: new ErrorCode(10500, 'Không tìm thấy bước', HttpStatus.NOT_FOUND),
  USER_STEP_CREATION_FAILED: new ErrorCode(10501, 'Tạo bước thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_STEP_UPDATE_FAILED: new ErrorCode(10502, 'Cập nhật bước thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_STEP_DELETE_FAILED: new ErrorCode(10503, '<PERSON><PERSON><PERSON> bước thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_STEP_FETCH_FAILED: new ErrorCode(10504, 'Lấy thông tin bước thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi xác thực và phân quyền
  USER_STEP_UNAUTHORIZED: new ErrorCode(10510, 'Không có quyền truy cập bước này', HttpStatus.FORBIDDEN),
  USER_STEP_INVALID_OWNER: new ErrorCode(10511, 'Bạn không phải là chủ sở hữu của bước này', HttpStatus.FORBIDDEN),
  
  // Lỗi trạng thái
  USER_STEP_INVALID_TYPE: new ErrorCode(10520, 'Loại bước không hợp lệ', HttpStatus.BAD_REQUEST),
  USER_STEP_TASK_COMPLETED: new ErrorCode(10521, 'Nhiệm vụ đã hoàn thành, không thể thay đổi bước', HttpStatus.BAD_REQUEST),
  USER_STEP_TASK_CANCELLED: new ErrorCode(10522, 'Nhiệm vụ đã bị hủy, không thể thay đổi bước', HttpStatus.BAD_REQUEST),
  
  // Lỗi dữ liệu
  USER_STEP_INVALID_DATA: new ErrorCode(10530, 'Dữ liệu bước không hợp lệ', HttpStatus.BAD_REQUEST),
  USER_STEP_NAME_REQUIRED: new ErrorCode(10531, 'Tên bước là bắt buộc', HttpStatus.BAD_REQUEST),
  USER_STEP_NAME_TOO_LONG: new ErrorCode(10532, 'Tên bước quá dài (tối đa 255 ký tự)', HttpStatus.BAD_REQUEST),
  USER_STEP_CONFIG_INVALID: new ErrorCode(10533, 'Cấu hình bước không hợp lệ', HttpStatus.BAD_REQUEST),
  USER_STEP_ORDER_INVALID: new ErrorCode(10534, 'Thứ tự bước không hợp lệ', HttpStatus.BAD_REQUEST),
  USER_STEP_ORDER_DUPLICATE: new ErrorCode(10535, 'Thứ tự bước đã tồn tại', HttpStatus.BAD_REQUEST),
  
  // Lỗi giới hạn
  USER_STEP_LIMIT_EXCEEDED: new ErrorCode(10540, 'Đã vượt quá giới hạn số lượng bước trong nhiệm vụ', HttpStatus.BAD_REQUEST),
};
