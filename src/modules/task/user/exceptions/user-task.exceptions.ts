import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho các thao tác liên quan đến task của người dùng
 * Phạm vi mã lỗi: 10400 - 10499
 */
export const USER_TASK_ERROR_CODES = {
  // Lỗi chung
  USER_TASK_NOT_FOUND: new ErrorCode(10400, 'Không tìm thấy nhiệm vụ', HttpStatus.NOT_FOUND),
  USER_TASK_CREATION_FAILED: new ErrorCode(10401, 'Tạo nhiệm vụ thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_TASK_UPDATE_FAILED: new ErrorCode(10402, 'Cập nhật nhiệm vụ thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_TASK_DELETE_FAILED: new ErrorCode(10403, 'Xóa nhiệm vụ thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_TASK_FETCH_FAILED: new ErrorCode(10404, 'Lấy thông tin nhiệm vụ thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_TASK_STATUS_CHANGE_FAILED: new ErrorCode(10405, 'Thay đổi trạng thái nhiệm vụ thất bại', HttpStatus.INTERNAL_SERVER_ERROR),

  // Lỗi xác thực và phân quyền
  USER_TASK_UNAUTHORIZED: new ErrorCode(10410, 'Không có quyền truy cập nhiệm vụ này', HttpStatus.FORBIDDEN),
  USER_TASK_INVALID_OWNER: new ErrorCode(10411, 'Bạn không phải là chủ sở hữu của nhiệm vụ này', HttpStatus.FORBIDDEN),
  
  // Lỗi trạng thái
  USER_TASK_INVALID_STATUS: new ErrorCode(10420, 'Trạng thái nhiệm vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  USER_TASK_ALREADY_COMPLETED: new ErrorCode(10421, 'Nhiệm vụ đã hoàn thành, không thể thay đổi', HttpStatus.BAD_REQUEST),
  USER_TASK_ALREADY_CANCELLED: new ErrorCode(10422, 'Nhiệm vụ đã bị hủy, không thể thay đổi', HttpStatus.BAD_REQUEST),

  // Lỗi dữ liệu
  USER_TASK_INVALID_DATA: new ErrorCode(10430, 'Dữ liệu nhiệm vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  USER_TASK_MISSING_AGENT: new ErrorCode(10431, 'Thiếu thông tin agent cho nhiệm vụ', HttpStatus.BAD_REQUEST),
  USER_TASK_AGENT_NOT_FOUND: new ErrorCode(10432, 'Không tìm thấy agent cho nhiệm vụ', HttpStatus.NOT_FOUND),
  USER_TASK_NAME_REQUIRED: new ErrorCode(10433, 'Tên nhiệm vụ là bắt buộc', HttpStatus.BAD_REQUEST),
  USER_TASK_NAME_TOO_LONG: new ErrorCode(10434, 'Tên nhiệm vụ quá dài (tối đa 255 ký tự)', HttpStatus.BAD_REQUEST),
  USER_TASK_ACCEPT_FAILED: new ErrorCode(10435, 'Nhiệm vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  // Lỗi giới hạn
  USER_TASK_LIMIT_EXCEEDED: new ErrorCode(10440, 'Đã vượt quá giới hạn số lượng nhiệm vụ', HttpStatus.BAD_REQUEST),
};
