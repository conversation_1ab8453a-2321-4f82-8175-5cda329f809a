import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho các thao tác liên quan đến connection của người dùng
 * Phạm vi mã lỗi: 10600 - 10699
 */
export const USER_CONNECTION_ERROR_CODES = {
  // Lỗi chung
  USER_CONNECTION_NOT_FOUND: new ErrorCode(10600, 'Không tìm thấy kết nối', HttpStatus.NOT_FOUND),
  USER_CONNECTION_CREATION_FAILED: new ErrorCode(10601, 'Tạo kết nối thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_CONNECTION_UPDATE_FAILED: new ErrorCode(10602, 'Cập nhật kết nối thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_CONNECTION_DELETE_FAILED: new ErrorCode(10603, '<PERSON><PERSON><PERSON> kết nối thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_CONNECTION_FETCH_FAILED: new ErrorCode(10604, 'Lấy thông tin kết nối thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi xác thực và phân quyền
  USER_CONNECTION_UNAUTHORIZED: new ErrorCode(10610, 'Không có quyền truy cập kết nối này', HttpStatus.FORBIDDEN),
  USER_CONNECTION_INVALID_OWNER: new ErrorCode(10611, 'Bạn không phải là chủ sở hữu của kết nối này', HttpStatus.FORBIDDEN),
  
  // Lỗi trạng thái
  USER_CONNECTION_TASK_COMPLETED: new ErrorCode(10620, 'Nhiệm vụ đã hoàn thành, không thể thay đổi kết nối', HttpStatus.BAD_REQUEST),
  USER_CONNECTION_TASK_CANCELLED: new ErrorCode(10621, 'Nhiệm vụ đã bị hủy, không thể thay đổi kết nối', HttpStatus.BAD_REQUEST),
  
  // Lỗi dữ liệu
  USER_CONNECTION_INVALID_DATA: new ErrorCode(10630, 'Dữ liệu kết nối không hợp lệ', HttpStatus.BAD_REQUEST),
  USER_CONNECTION_SAME_STEP: new ErrorCode(10631, 'Không thể kết nối một bước với chính nó', HttpStatus.BAD_REQUEST),
  USER_CONNECTION_STEP_NOT_FOUND: new ErrorCode(10632, 'Không tìm thấy bước để kết nối', HttpStatus.NOT_FOUND),
  USER_CONNECTION_CIRCULAR: new ErrorCode(10633, 'Không thể tạo kết nối vòng tròn giữa các bước', HttpStatus.BAD_REQUEST),
  USER_CONNECTION_FIELD_REQUIRED: new ErrorCode(10634, 'Tên trường input/output là bắt buộc', HttpStatus.BAD_REQUEST),
  USER_CONNECTION_FIELD_TOO_LONG: new ErrorCode(10635, 'Tên trường quá dài (tối đa 255 ký tự)', HttpStatus.BAD_REQUEST),
  
  // Lỗi giới hạn
  USER_CONNECTION_LIMIT_EXCEEDED: new ErrorCode(10640, 'Đã vượt quá giới hạn số lượng kết nối trong nhiệm vụ', HttpStatus.BAD_REQUEST),
};
