import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho các thao tác liên quan đến execution của người dùng
 * Phạm vi mã lỗi: 10700 - 10799
 */
export const USER_EXECUTION_ERROR_CODES = {
  // Lỗi chung
  USER_EXECUTION_NOT_FOUND: new ErrorCode(10700, 'Không tìm thấy phiên thực thi', HttpStatus.NOT_FOUND),
  USER_EXECUTION_CREATION_FAILED: new ErrorCode(10701, 'Tạo phiên thực thi thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_EXECUTION_UPDATE_FAILED: new ErrorCode(10702, 'Cập nhật phiên thực thi thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_EXECUTION_DELETE_FAILED: new ErrorCode(10703, 'Xóa phiên thực thi thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_EXECUTION_FETCH_FAILED: new ErrorCode(10704, 'Lấy thông tin phiên thực thi thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi xác thực và phân quyền
  USER_EXECUTION_UNAUTHORIZED: new ErrorCode(10710, 'Không có quyền truy cập phiên thực thi này', HttpStatus.FORBIDDEN),
  USER_EXECUTION_INVALID_OWNER: new ErrorCode(10711, 'Bạn không phải là chủ sở hữu của phiên thực thi này', HttpStatus.FORBIDDEN),
  
  // Lỗi trạng thái
  USER_EXECUTION_INVALID_STATUS: new ErrorCode(10720, 'Trạng thái phiên thực thi không hợp lệ', HttpStatus.BAD_REQUEST),
  USER_EXECUTION_ALREADY_COMPLETED: new ErrorCode(10721, 'Phiên thực thi đã hoàn thành, không thể thay đổi', HttpStatus.BAD_REQUEST),
  USER_EXECUTION_ALREADY_CANCELLED: new ErrorCode(10722, 'Phiên thực thi đã bị hủy, không thể thay đổi', HttpStatus.BAD_REQUEST),
  
  // Lỗi dữ liệu
  USER_EXECUTION_INVALID_DATA: new ErrorCode(10730, 'Dữ liệu phiên thực thi không hợp lệ', HttpStatus.BAD_REQUEST),
  USER_EXECUTION_TASK_NOT_FOUND: new ErrorCode(10731, 'Không tìm thấy nhiệm vụ để thực thi', HttpStatus.NOT_FOUND),
  USER_EXECUTION_TASK_INACTIVE: new ErrorCode(10732, 'Nhiệm vụ không hoạt động, không thể thực thi', HttpStatus.BAD_REQUEST),
  USER_EXECUTION_NO_STEPS: new ErrorCode(10733, 'Nhiệm vụ không có bước nào để thực thi', HttpStatus.BAD_REQUEST),
  
  // Lỗi giới hạn
  USER_EXECUTION_LIMIT_EXCEEDED: new ErrorCode(10740, 'Đã vượt quá giới hạn số lượng phiên thực thi', HttpStatus.BAD_REQUEST),
  USER_EXECUTION_CONCURRENT_LIMIT: new ErrorCode(10741, 'Đã vượt quá giới hạn số lượng phiên thực thi đồng thời', HttpStatus.BAD_REQUEST),
};
