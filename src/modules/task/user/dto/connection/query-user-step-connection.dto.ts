import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsUUID } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum định nghĩa các trường sắp xếp cho connection
 */
export enum ConnectionSortBy {
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho việc truy vấn danh sách kết nối giữa các bước trong nhiệm vụ của người dùng
 */
export class QueryUserStepConnectionDto extends QueryDto {
  /**
   * Lọc theo ID nhiệm vụ
   */
  @ApiProperty({
    description: 'Lọc theo ID nhiệm vụ',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { message: 'ID nhiệm vụ phải là UUID hợp lệ' })
  taskId?: string;

  /**
   * Lọ<PERSON> theo ID step nguồn
   */
  @ApiProperty({
    description: 'Lọc theo ID step nguồn',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { message: 'ID step nguồn phải là UUID hợp lệ' })
  fromStepId?: string;

  /**
   * Lọc theo ID step đích
   */
  @ApiProperty({
    description: 'Lọc theo ID step đích',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { message: 'ID step đích phải là UUID hợp lệ' })
  toStepId?: string;

  /**
   * Trường cần sắp xếp
   */
  @ApiProperty({
    description: 'Trường cần sắp xếp',
    enum: ConnectionSortBy,
    default: ConnectionSortBy.CREATED_AT,
    required: false,
  })
  @IsOptional()
  sortBy?: ConnectionSortBy = ConnectionSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  sortDirection?: SortDirection = SortDirection.DESC;
}
