import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

/**
 * DTO cho việc cập nhật kết nối giữa các bước trong nhiệm vụ của người dùng
 */
export class UpdateUserStepConnectionDto {
  /**
   * ID của step nguồn
   */
  @ApiProperty({
    description: 'ID của step nguồn',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { message: 'ID step nguồn phải là UUID hợp lệ' })
  fromStepId?: string;

  /**
   * ID của step đích
   */
  @ApiProperty({
    description: 'ID của step đích',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { message: 'ID step đích phải là UUID hợp lệ' })
  toStepId?: string;

  /**
   * Tên trường trong output của step nguồn
   */
  @ApiProperty({
    description: 'Tên trường trong output của step nguồn',
    example: 'emailContent',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên trường output phải là chuỗi' })
  @MaxLength(255, { message: 'Tên trường output không được vượt quá 255 ký tự' })
  outputField?: string;

  /**
   * Tên trường trong input của step đích
   */
  @ApiProperty({
    description: 'Tên trường trong input của step đích',
    example: 'content',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên trường input phải là chuỗi' })
  @MaxLength(255, { message: 'Tên trường input không được vượt quá 255 ký tự' })
  inputField?: string;
}
