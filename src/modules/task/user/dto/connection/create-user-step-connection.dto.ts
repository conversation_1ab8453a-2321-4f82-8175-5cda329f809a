import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

/**
 * DTO cho việc tạo mới kết nối giữa các bước trong nhiệm vụ của người dùng
 */
export class CreateUserStepConnectionDto {
  /**
   * ID của nhiệm vụ
   * @internal Không hiển thị trong Swagger vì đã có trong URL
   */
  @IsOptional()
  @IsNotEmpty({ message: 'ID nhiệm vụ là bắt buộc' })
  taskId: string;

  /**
   * ID của step nguồn
   */
  @ApiProperty({
    description: 'ID của step nguồn',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty({ message: 'ID step nguồn là bắt buộc' })
  @IsString({ message: 'ID step nguồn phải là chuỗi' })
  fromStepId: string;

  /**
   * ID của step đích
   */
  @ApiProperty({
    description: 'ID của step đích',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty({ message: 'ID step đích là bắt buộc' })
  @IsString({ message: 'Tên bước phải là chuỗi' })
  toStepId: string;

  /**
   * Tên trường trong output của step nguồn
   */
  @ApiProperty({
    description: 'Tên trường trong output của step nguồn',
    example: 'emailContent',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên trường output là bắt buộc' })
  @IsString({ message: 'Tên trường output phải là chuỗi' })
  @MaxLength(255, { message: 'Tên trường output không được vượt quá 255 ký tự' })
  outputField: string;

  /**
   * Tên trường trong input của step đích
   */
  @ApiProperty({
    description: 'Tên trường trong input của step đích',
    example: 'content',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên trường input là bắt buộc' })
  @IsString({ message: 'Tên trường input phải là chuỗi' })
  @MaxLength(255, { message: 'Tên trường input không được vượt quá 255 ký tự' })
  inputField: string;
}
