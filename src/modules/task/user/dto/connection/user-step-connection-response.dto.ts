import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho việc trả về thông tin kết nối giữa các bước trong nhiệm vụ của người dùng
 */
export class UserStepConnectionResponseDto {
  /**
   * ID của kết nối
   */
  @ApiProperty({
    description: 'ID của kết nối',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  connectionId: string;

  /**
   * ID của nhiệm vụ
   */
  @ApiProperty({
    description: 'ID của nhiệm vụ',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  taskId: string;

  /**
   * ID của step nguồn
   */
  @ApiProperty({
    description: 'ID của step nguồn',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  fromStepId: string;

  /**
   * ID của step đích
   */
  @ApiProperty({
    description: 'ID của step đích',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  toStepId: string;

  /**
   * Tên trường trong output của step nguồn
   */
  @ApiProperty({
    description: 'Tên trường trong output của step nguồn',
    example: 'emailContent',
  })
  @Expose()
  outputField: string;

  /**
   * Tên trường trong input của step đích
   */
  @ApiProperty({
    description: 'Tên trường trong input của step đích',
    example: 'content',
  })
  @Expose()
  inputField: string;

  /**
   * Thời điểm tạo kết nối (Unix epoch)
   */
  @ApiProperty({
    description: 'Thời điểm tạo kết nối (Unix epoch)',
    example: 1625097600000,
  })
  @Expose()
  @Type(() => Number)
  createdAt: number;
}
