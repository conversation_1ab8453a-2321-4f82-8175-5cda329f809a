import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { StepType } from '@modules/task/interfaces/step-config.interface';

/**
 * Enum định nghĩa các trường sắp xếp cho step
 */
export enum StepSortBy {
  CREATED_AT = 'createdAt',
  ORDER_INDEX = 'orderIndex',
}

/**
 * DTO cho việc truy vấn danh sách bước trong nhiệm vụ của người dùng
 */
export class QueryUserStepDto extends QueryDto {
  /**
   * Lọc theo ID nhiệm vụ
   * @internal Không hiển thị trong Swagger vì đã có trong URL
   */
  @IsOptional()
  taskId?: string;

  /**
   * Lọ<PERSON> theo loại bước
   */
  @ApiProperty({
    description: '<PERSON>ọ<PERSON> theo loại bước',
    example: 'prompt',
    enum: [
      'prompt',
      'google_sheet',
      'google_doc',
      'google_calendar',
      'email',
      'facebook_page',
      'gen_image',
      'gen_video',
      'agent'
    ],
    required: false,
  })
  @IsOptional()
  @Type(() => String)
  stepType?: StepType;

  /**
   * Trường cần sắp xếp
   */
  @ApiProperty({
    description: 'Trường cần sắp xếp',
    enum: StepSortBy,
    default: StepSortBy.ORDER_INDEX,
    required: false,
  })
  @IsOptional()
  @IsEnum(StepSortBy, { message: 'Trường sắp xếp không hợp lệ' })
  sortBy?: StepSortBy = StepSortBy.ORDER_INDEX;

  /**
   * Hướng sắp xếp
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.ASC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection, { message: 'Hướng sắp xếp không hợp lệ' })
  sortDirection?: SortDirection = SortDirection.ASC;
}
