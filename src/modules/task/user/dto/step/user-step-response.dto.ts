import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { StepType } from '@modules/task/interfaces/step-config.interface';

/**
 * DTO cho việc trả về thông tin bước trong nhiệm vụ của người dùng
 */
export class UserStepResponseDto {
  /**
   * ID của bước
   */
  @ApiProperty({
    description: 'ID của bước',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  stepId: string;

  /**
   * ID của nhiệm vụ
   */
  @ApiProperty({
    description: 'ID của nhiệm vụ',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  taskId: string;

  /**
   * Thứ tự xuất hiện của bước trong nhiệm vụ
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> tự xuất hiện của bước trong nhiệm vụ',
    example: 1,
  })
  @Expose()
  @Type(() => Number)
  orderIndex: number;

  /**
   * Tên của bước
   */
  @ApiProperty({
    description: 'Tên của bước',
    example: 'Nhập nội dung email',
  })
  @Expose()
  stepName: string;

  /**
   * Mô tả chi tiết của bước
   */
  @ApiProperty({
    description: 'Mô tả chi tiết của bước',
    example: 'Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
    nullable: true,
  })
  @Expose()
  stepDescription: string | null;

  /**
   * Loại bước
   */
  @ApiProperty({
    description: 'Loại bước',
    example: 'prompt',
    enum: [
      'prompt',
      'google_sheet',
      'google_doc',
      'google_calendar',
      'email',
      'facebook_page',
      'gen_image',
      'gen_video',
      'agent'
    ]
  })
  @Expose()
  stepType: StepType;

  /**
   * Cấu hình chi tiết của bước ở dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình chi tiết của bước ở dạng JSON',
    example: {
      stepType: 'prompt',
      promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
      inputType: 'text',
      required: true,
      defaultValue: ''
    },
    nullable: true,
  })
  @Expose()
  stepConfig: Record<string, unknown> | null;

  /**
   * ID xác thực Google của người dùng
   */
  @ApiProperty({
    description: 'ID xác thực Google của người dùng',
    example: '123e4567-e89b-12d3-a456-426614174000',
    nullable: true,
  })
  @Expose()
  googleUserAuthId: string | null;

  /**
   * ID trang Facebook
   */
  @ApiProperty({
    description: 'ID trang Facebook',
    example: '123e4567-e89b-12d3-a456-426614174000',
    nullable: true,
  })
  @Expose()
  facebookPageId: string | null;

  /**
   * Thời điểm tạo bước (Unix epoch)
   */
  @ApiProperty({
    description: 'Thời điểm tạo bước (Unix epoch)',
    example: 1625097600000,
  })
  @Expose()
  @Type(() => Number)
  createdAt: number;
}
