import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {  IsInt, IsObject, IsOptional, IsString, MaxLength, Min } from 'class-validator';
import { StepType } from '@modules/task/interfaces/step-config.interface';

/**
 * DTO cho việc cập nhật bước trong nhiệm vụ của người dùng
 */
export class UpdateUserStepDto {
  /**
   * Thứ tự xuất hiện của bước trong nhiệm vụ
   */
  @ApiProperty({
    description: 'Thứ tự xuất hiện của bước trong nhiệm vụ',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Thứ tự bước phải là số nguyên' })
  @Min(0, { message: 'Thứ tự bước phải lớn hơn hoặc bằng 0' })
  @Type(() => Number)
  orderIndex?: number;

  /**
   * T<PERSON><PERSON> của bước
   */
  @ApiProperty({
    description: 'Tên của bước',
    example: 'Nhập nội dung email',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên bước phải là chuỗi' })
  @MaxLength(255, { message: 'Tên bước không được vượt quá 255 ký tự' })
  stepName?: string;

  /**
   * Mô tả chi tiết của bước
   */
  @ApiProperty({
    description: 'Mô tả chi tiết của bước',
    example: 'Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả bước phải là chuỗi' })
  stepDescription?: string;

  /**
   * Loại bước
   */
  @ApiProperty({
    description: 'Loại bước',
    example: 'prompt',
    enum: [
      'prompt',
      'google_sheet',
      'google_doc',
      'google_calendar',
      'email',
      'facebook_page',
      'gen_image',
      'gen_video',
      'agent'
    ],
    required: false,
  })
  @IsOptional()
  stepType?: StepType;

  /**
   * Cấu hình chi tiết của bước ở dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình chi tiết của bước ở dạng JSON',
    example: {
      stepType: 'prompt',
      promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
      inputType: 'text',
      required: true,
      defaultValue: ''
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình bước phải là đối tượng JSON' })
  stepConfig?: Record<string, unknown>;

  /**
   * ID xác thực Google của người dùng
   */
  @ApiProperty({
    description: 'ID xác thực Google của người dùng',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'ID xác thực Google phải là chuỗi' })
  googleUserAuthId?: string;

  /**
   * ID trang Facebook
   */
  @ApiProperty({
    description: 'ID trang Facebook',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'ID trang Facebook phải là chuỗi' })
  facebookPageId?: string;
}
