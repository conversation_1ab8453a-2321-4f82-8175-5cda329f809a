import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { TaskStatus } from '@modules/task/enums';

/**
 * Enum định nghĩa các trường sắp xếp cho task
 */
export enum TaskSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho việc truy vấn danh sách nhiệm vụ của người dùng
 */
export class QueryUserTaskDto extends QueryDto {
  /**
   * Lọc theo trạng thái nhiệm vụ
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái nhiệm vụ',
    enum: TaskStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(TaskStatus, { message: 'Trạng thái nhiệm vụ không hợp lệ' })
  @Type(() => String)
  status?: TaskStatus;

  /**
   * <PERSON><PERSON><PERSON> theo trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái hoạt động',
    example: true,
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  active?: boolean;

  /**
   * Lọc theo ID của agent
   */
  @ApiProperty({
    description: 'Lọc theo ID của agent',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { message: 'ID agent phải là UUID hợp lệ' })
  agentId?: string;

  /**
   * Trường cần sắp xếp
   */
  @ApiProperty({
    description: 'Trường cần sắp xếp',
    enum: TaskSortBy,
    default: TaskSortBy.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(TaskSortBy, { message: 'Trường sắp xếp không hợp lệ' })
  sortBy?: TaskSortBy = TaskSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection, { message: 'Hướng sắp xếp không hợp lệ' })
  sortDirection?: SortDirection = SortDirection.DESC;
}
