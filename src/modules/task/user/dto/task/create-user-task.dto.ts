import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID, MaxLength, ValidateIf } from 'class-validator';

/**
 * DTO cho việc tạo mới nhiệm vụ của người dùng
 */
export class CreateUserTaskDto {
  /**
   * ID của agent đ<PERSON><PERSON><PERSON> sử dụng cho nhiệm vụ
   */
  @IsOptional()
  @ValidateIf((o) => o.agentId !== null && o.agentId !== undefined && o.agentId !== '')
  @IsUUID('4', { message: 'ID agent phải là UUID hợp lệ' })
  agentId: string | null;

  /**
   * Tên của nhiệm vụ
   */
  @ApiProperty({
    description: 'Tên của nhiệm vụ',
    example: 'Tạo chiến dịch email marketing',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên nhiệm vụ là bắt buộc' })
  @IsString({ message: 'Tê<PERSON> nhiệm vụ phải là chuỗi' })
  @MaxLength(255, { message: 'Tên nhiệm vụ không được vượt quá 255 ký tự' })
  taskName: string;

  /**
   * Mô tả chi tiết nhiệm vụ
   */
  @ApiProperty({
    description: 'Mô tả chi tiết nhiệm vụ',
    example: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả nhiệm vụ phải là chuỗi' })
  taskDescription?: string;
}
