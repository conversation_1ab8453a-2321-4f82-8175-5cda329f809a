import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID, MaxLength, ValidateIf } from 'class-validator';
import { TaskStatus } from '@modules/task/enums';

/**
 * DTO cho việc cập nhật nhiệm vụ của người dùng
 */
export class UpdateUserTaskDto {
  /**
   * Tên của nhiệm vụ
   */
  @ApiProperty({
    description: 'Tên của nhiệm vụ',
    example: 'Tạo chiến dịch email marketing',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên nhiệm vụ phải là chuỗi' })
  @MaxLength(255, { message: 'Tên nhiệm vụ không được vượt quá 255 ký tự' })
  taskName?: string;

  /**
   * <PERSON>ô tả chi tiết nhiệm vụ
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết nhiệm vụ',
    example: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả nhiệm vụ phải là chuỗi' })
  taskDescription?: string;

  /**
   * Trạng thái nhiệm vụ
   * @internal - Không cho phép người dùng cập nhật trực tiếp
   */
  @IsOptional()
  @IsEnum(TaskStatus, { message: 'Trạng thái nhiệm vụ không hợp lệ' })
  status?: TaskStatus;

  /**
   * Trạng thái hoạt động của nhiệm vụ
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của nhiệm vụ',
    example: true,
    required: false,
  })
  @IsOptional()
  active?: boolean;

  /**
   * ID của agent được sử dụng cho nhiệm vụ
   */
  @ApiProperty({
    description: 'ID của agent được sử dụng cho nhiệm vụ',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
    nullable: true
  })
  @IsOptional()
  @ValidateIf((o) => o.agentId !== null && o.agentId !== undefined && o.agentId !== '')
  @IsUUID('4', { message: 'ID agent phải là UUID hợp lệ' })
  agentId?: string | null;
}
