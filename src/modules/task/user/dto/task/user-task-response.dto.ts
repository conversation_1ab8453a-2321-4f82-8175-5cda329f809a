import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { TaskStatus } from '@modules/task/enums';

/**
 * DTO cho việc trả về thông tin nhiệm vụ của người dùng
 */
export class UserTaskResponseDto {
  /**
   * ID của nhiệm vụ
   */
  @ApiProperty({
    description: 'ID của nhiệm vụ',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  taskId: string;

  /**
   * ID người dùng tạo nhiệm vụ
   */
  @ApiProperty({
    description: 'ID người dùng tạo nhiệm vụ',
    example: 123,
  })
  @Expose()
  userId: number;

  /**
   * ID của agent đư<PERSON><PERSON> sử dụng cho nhiệm vụ
   */
  @ApiProperty({
    description: 'ID của agent đ<PERSON><PERSON><PERSON> sử dụng cho nhiệm vụ',
    example: '123e4567-e89b-12d3-a456-426614174000',
    nullable: true
  })
  @Expose()
  agentId: string | null;

  /**
   * Tên của nhiệm vụ
   */
  @ApiProperty({
    description: 'Tên của nhiệm vụ',
    example: 'Tạo chiến dịch email marketing',
  })
  @Expose()
  taskName: string;

  /**
   * Mô tả chi tiết nhiệm vụ
   */
  @ApiProperty({
    description: 'Mô tả chi tiết nhiệm vụ',
    example: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
    nullable: true,
  })
  @Expose()
  taskDescription: string | null;

  /**
   * Trạng thái nhiệm vụ
   */
  @ApiProperty({
    description: 'Trạng thái nhiệm vụ',
    enum: TaskStatus,
  })
  @Expose()
  status: TaskStatus;

  /**
   * Trạng thái hoạt động của nhiệm vụ
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của nhiệm vụ',
    example: true,
  })
  @Expose()
  active: boolean;

  /**
   * Thời điểm tạo nhiệm vụ (Unix epoch)
   */
  @ApiProperty({
    description: 'Thời điểm tạo nhiệm vụ (Unix epoch)',
    example: 1625097600000,
  })
  @Expose()
  @Type(() => Number)
  createdAt: number;

  /**
   * Thời điểm cập nhật nhiệm vụ (Unix epoch)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật nhiệm vụ (Unix epoch)',
    example: 1625097600000,
  })
  @Expose()
  @Type(() => Number)
  updatedAt: number;
}
