import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { TaskExecutionStatus } from '@modules/task/enums';

/**
 * Enum định nghĩa các trường sắp xếp cho task execution
 */
export enum ExecutionSortBy {
  CREATED_AT = 'createdAt',
  START_TIME = 'startTime',
  END_TIME = 'endTime',
}

/**
 * DTO cho việc truy vấn danh sách phiên thực thi nhiệm vụ của người dùng
 */
export class QueryUserTaskExecutionDto extends QueryDto {
  /**
   * Lọc theo ID nhiệm vụ
   */
  @ApiProperty({
    description: 'Lọc theo ID nhiệm vụ',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { message: 'ID nhiệm vụ phải là UUID hợp lệ' })
  taskId?: string;

  /**
   * Lọc theo trạng thái thực thi
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái thực thi',
    enum: TaskExecutionStatus,
    example: TaskExecutionStatus.SUCCESS,
    required: false,
  })
  @IsOptional()
  @IsEnum(TaskExecutionStatus, { message: 'Trạng thái thực thi không hợp lệ' })
  @Type(() => String)
  overallStatus?: TaskExecutionStatus;

  /**
   * Trường cần sắp xếp
   */
  @ApiProperty({
    description: 'Trường cần sắp xếp',
    enum: ExecutionSortBy,
    default: ExecutionSortBy.START_TIME,
    required: false,
  })
  @IsOptional()
  @IsEnum(ExecutionSortBy, { message: 'Trường sắp xếp không hợp lệ' })
  sortBy?: ExecutionSortBy = ExecutionSortBy.START_TIME;

  /**
   * Hướng sắp xếp
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection, { message: 'Hướng sắp xếp không hợp lệ' })
  sortDirection?: SortDirection = SortDirection.DESC;
}
