import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { TaskExecutionStatus } from '@modules/task/enums';

/**
 * DTO cho việc trả về thông tin phiên thực thi nhiệm vụ của người dùng
 */
export class UserTaskExecutionResponseDto {
  /**
   * ID của phiên thực thi
   */
  @ApiProperty({
    description: 'ID của phiên thực thi',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  taskExecutionId: string;

  /**
   * ID của nhiệm vụ
   */
  @ApiProperty({
    description: 'ID của nhiệm vụ',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  taskId: string;

  /**
   * Thời gian bắt đầu phiên thực thi (Unix epoch)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu phiên thực thi (Unix epoch)',
    example: 1625097600000,
  })
  @Expose()
  @Type(() => Number)
  startTime: number;

  /**
   * Thời gian kết thúc phiên thực thi (Unix epoch)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc phiên thực thi (Unix epoch)',
    example: 1625097660000,
    nullable: true,
  })
  @Expose()
  @Type(() => Number)
  endTime: number | null;

  /**
   * Trạng thái tổng thể của phiên thực thi
   */
  @ApiProperty({
    description: 'Trạng thái tổng thể của phiên thực thi',
    enum: TaskExecutionStatus,
    example: TaskExecutionStatus.SUCCESS,
  })
  @Expose()
  overallStatus: TaskExecutionStatus;

  /**
   * Log chi tiết của từng bước trong phiên thực thi, lưu dưới dạng mảng JSON
   */
  @ApiProperty({
    description: 'Log chi tiết của từng bước trong phiên thực thi, lưu dưới dạng mảng JSON',
    example: {
      steps: [
        {
          stepId: '123e4567-e89b-12d3-a456-426614174000',
          status: 'SUCCESS',
          output: { content: 'Nội dung email đã được tạo' },
          startTime: 1625097600000,
          endTime: 1625097660000,
        },
      ],
    },
    nullable: true,
  })
  @Expose()
  executionDetails: Record<string, any> | null;

  /**
   * Thời điểm ghi log phiên thực thi (Unix epoch)
   */
  @ApiProperty({
    description: 'Thời điểm ghi log phiên thực thi (Unix epoch)',
    example: 1625097600000,
  })
  @Expose()
  @Type(() => Number)
  createdAt: number;
}
