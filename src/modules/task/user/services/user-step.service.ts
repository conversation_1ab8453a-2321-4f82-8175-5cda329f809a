import { Injectable, Logger } from '@nestjs/common';
import { UserStepRepository, UserTaskRepository } from '../../repositories';
import { CreateUserStepDto, UpdateUserStepDto, QueryUserStepDto, UserStepResponseDto } from '../dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { USER_STEP_ERROR_CODES } from '@modules/task/user/exceptions';
import { plainToInstance } from 'class-transformer';
import { ValidationHelper } from '../helpers';
import { StepConfig } from '@modules/task/interfaces/step-config.interface';

/**
 * Service xử lý logic liên quan đến các bước trong task của người dùng
 */
@Injectable()
export class UserStepService {
  /**
   * Logger cho UserStepService
   */
  private readonly logger = new Logger(UserStepService.name);

  /**
   * Constructor
   * @param userStepRepository Repository xử lý dữ liệu các bước trong task của người dùng
   * @param userTaskRepository Repository xử lý dữ liệu task của người dùng
   * @param validationHelper Helper xử lý validation
   */
  constructor(
    private readonly userStepRepository: UserStepRepository,
    private readonly userTaskRepository: UserTaskRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo mới bước trong nhiệm vụ
   * @param userId ID của người dùng
   * @param createDto Thông tin bước cần tạo
   * @returns Bước đã tạo
   */
  async create(userId: number, createDto: CreateUserStepDto): Promise<UserStepResponseDto> {
    try {
      this.logger.log(`Tạo bước mới cho nhiệm vụ ${createDto.taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại và thuộc về người dùng
      const task = await this.userTaskRepository.findById(createDto.taskId, userId);
      this.validationHelper.validateTaskFullCheck(task, userId);
      this.validationHelper.validateTaskStatusForUpdate(task);

      // Kiểm tra tên bước hợp lệ
      this.validationHelper.validateStepName(createDto.stepName);

      // Kiểm tra loại bước hợp lệ
      this.validationHelper.validateStepType(createDto.stepType);

      // Kiểm tra cấu hình bước hợp lệ
      await this.validationHelper.validateStepConfig(createDto.stepType, createDto.stepConfig);

      // Kiểm tra số lượng bước trong nhiệm vụ
      const stepCount = await this.userStepRepository.countByTaskId(createDto.taskId);
      this.validationHelper.validateStepLimit(stepCount);

      // Tự động tính toán orderIndex nếu không được cung cấp
      if (createDto.orderIndex === undefined) {
        // Lấy thứ tự lớn nhất hiện có cho loại bước này và cộng thêm 1
        const maxOrderIndex = await this.userStepRepository.getMaxOrderIndex(createDto.taskId, createDto.stepType);
        createDto.orderIndex = maxOrderIndex + 1;
        this.logger.log(`Tự động gán orderIndex = ${createDto.orderIndex} cho loại bước ${createDto.stepType} (maxOrderIndex = ${maxOrderIndex})`);
      } else {
        // Nếu orderIndex được cung cấp, kiểm tra tính hợp lệ
        this.validationHelper.validateStepOrderIndex(createDto.orderIndex);

        // Kiểm tra thứ tự bước đã tồn tại chưa trong cùng loại bước
        const orderExists = await this.userStepRepository.isOrderIndexExists(createDto.taskId, createDto.orderIndex, createDto.stepType);
        if (orderExists) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_ORDER_DUPLICATE,
            `Thứ tự bước ${createDto.orderIndex} đã tồn tại trong loại bước ${createDto.stepType}`
          );
        }
      }

      // Đảm bảo stepConfig có stepType đúng
      let stepConfig = createDto.stepConfig || {};
      stepConfig.stepType = createDto.stepType;

      // Tạo bước mới
      const newStep = await this.userStepRepository.createStep({
        taskId: createDto.taskId,
        orderIndex: createDto.orderIndex,
        stepName: createDto.stepName,
        stepDescription: createDto.stepDescription,
        stepType: createDto.stepType,
        stepConfig: stepConfig as unknown as StepConfig,
        googleUserAuthId: createDto.googleUserAuthId,
        facebookPageId: createDto.facebookPageId,
      });

      // Chuyển đổi sang DTO trả về
      return plainToInstance(UserStepResponseDto, newStep, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo bước cho nhiệm vụ ${createDto.taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_CREATION_FAILED,
        `Lỗi khi tạo bước cho nhiệm vụ ${createDto.taskId}`
      );
    }
  }

  /**
   * Cập nhật bước trong nhiệm vụ
   * @param stepId ID của bước
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật
   * @returns Bước đã cập nhật
   */
  async update(stepId: string, taskId: string, userId: number, updateDto: UpdateUserStepDto): Promise<UserStepResponseDto> {
    try {
      this.logger.log(`Cập nhật bước ${stepId} của nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại và thuộc về người dùng
      const task = await this.userTaskRepository.findById(taskId, userId);
      this.validationHelper.validateTaskFullCheck(task, userId);
      this.validationHelper.validateTaskStatusForUpdate(task);

      // Kiểm tra bước tồn tại và thuộc về nhiệm vụ
      const step = await this.userStepRepository.findById(stepId, taskId);
      this.validationHelper.validateStepFullCheck(step, taskId);

      // Kiểm tra tên bước hợp lệ nếu có cập nhật
      if (updateDto.stepName) {
        this.validationHelper.validateStepName(updateDto.stepName);
      }

      // Kiểm tra loại bước hợp lệ nếu có cập nhật
      if (updateDto.stepType) {
        this.validationHelper.validateStepType(updateDto.stepType);
      }

      // Kiểm tra cấu hình bước hợp lệ nếu có cập nhật
      if (updateDto.stepConfig) {
        // Đảm bảo step không null (validateStepFullCheck đã kiểm tra rồi)
        await this.validationHelper.validateStepConfig(
          updateDto.stepType || step!.stepType,
          updateDto.stepConfig
        );
      }

      // Kiểm tra thứ tự bước hợp lệ nếu có cập nhật
      if (updateDto.orderIndex !== undefined) {
        this.validationHelper.validateStepOrderIndex(updateDto.orderIndex);

        // Xác định loại bước (sử dụng loại mới nếu được cập nhật, nếu không thì sử dụng loại hiện tại)
        const stepType = updateDto.stepType || step!.stepType;

        // Kiểm tra thứ tự bước đã tồn tại chưa trong cùng loại bước
        const orderExists = await this.userStepRepository.isOrderIndexExists(
          taskId,
          updateDto.orderIndex,
          stepType,
          stepId // Loại trừ bước hiện tại khi kiểm tra
        );

        if (orderExists) {
          throw new AppException(
            USER_STEP_ERROR_CODES.USER_STEP_ORDER_DUPLICATE,
            `Thứ tự bước ${updateDto.orderIndex} đã tồn tại trong loại bước ${stepType}`
          );
        }
      }

      // Xử lý stepConfig
      let stepConfig = updateDto.stepConfig;
      if (stepConfig) {
        // Đảm bảo stepType trong stepConfig khớp với stepType của bước
        stepConfig.stepType = updateDto.stepType || step!.stepType;
      }

      // Cập nhật bước
      const updatedStep = await this.userStepRepository.updateStep(stepId, taskId, {
        stepName: updateDto.stepName,
        stepDescription: updateDto.stepDescription,
        stepType: updateDto.stepType,
        stepConfig: stepConfig as unknown as StepConfig,
        orderIndex: updateDto.orderIndex,
        googleUserAuthId: updateDto.googleUserAuthId,
        facebookPageId: updateDto.facebookPageId,
      });

      // Chuyển đổi sang DTO trả về
      return plainToInstance(UserStepResponseDto, updatedStep, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật bước ${stepId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_UPDATE_FAILED,
        `Lỗi khi cập nhật bước ${stepId}`
      );
    }
  }

  /**
   * Lấy thông tin bước theo ID
   * @param stepId ID của bước
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @returns Thông tin bước
   */
  async findById(stepId: string, taskId: string, userId: number): Promise<UserStepResponseDto> {
    try {
      this.logger.log(`Lấy thông tin bước ${stepId} của nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại và thuộc về người dùng
      const task = await this.userTaskRepository.findById(taskId, userId);
      this.validationHelper.validateTaskExists(task);
      this.validationHelper.validateTaskNotDeleted(task);

      // Tìm bước theo ID
      const step = await this.userStepRepository.findById(stepId, taskId);
      this.validationHelper.validateStepExists(step);

      // Chuyển đổi sang DTO trả về
      return plainToInstance(UserStepResponseDto, step, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin bước ${stepId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_FETCH_FAILED,
        `Lỗi khi lấy thông tin bước ${stepId}`
      );
    }
  }

  /**
   * Lấy danh sách bước của nhiệm vụ
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách bước với phân trang
   */
  async findAll(taskId: string, userId: number, queryDto: QueryUserStepDto): Promise<PaginatedResult<UserStepResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách bước của nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại và thuộc về người dùng
      const task = await this.userTaskRepository.findById(taskId, userId);
      this.validationHelper.validateTaskExists(task);
      this.validationHelper.validateTaskNotDeleted(task);

      // Lấy danh sách bước
      const result = await this.userStepRepository.findAll(taskId, queryDto);

      // Chuyển đổi sang DTO trả về
      const items = result.items.map(step =>
        plainToInstance(UserStepResponseDto, step, {
          excludeExtraneousValues: true,
        })
      );

      return {
        ...result,
        items,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách bước của nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_FETCH_FAILED,
        `Lỗi khi lấy danh sách bước của nhiệm vụ ${taskId}`
      );
    }
  }

  /**
   * Xóa bước
   * @param stepId ID của bước
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công
   */
  async delete(stepId: string, taskId: string, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa bước ${stepId} của nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại và thuộc về người dùng
      const task = await this.userTaskRepository.findById(taskId, userId);
      this.validationHelper.validateTaskFullCheck(task, userId);
      this.validationHelper.validateTaskStatusForUpdate(task);

      // Kiểm tra bước tồn tại và thuộc về nhiệm vụ
      const step = await this.userStepRepository.findById(stepId, taskId);
      this.validationHelper.validateStepFullCheck(step, taskId);

      // Xóa bước
      return await this.userStepRepository.deleteStep(stepId, taskId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa bước ${stepId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_DELETE_FAILED,
        `Lỗi khi xóa bước ${stepId}`
      );
    }
  }
}
