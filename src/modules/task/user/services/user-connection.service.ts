import { Injectable, Logger } from '@nestjs/common';
import { UserStepConnectionRepository, UserStepRepository, UserTaskRepository } from '../../repositories';
import { CreateUserStepConnectionDto, UpdateUserStepConnectionDto, QueryUserStepConnectionDto, UserStepConnectionResponseDto } from '../dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { CONNECTION_ERROR_CODES } from '@modules/task/exceptions/connection.exceptions';
import { plainToInstance } from 'class-transformer';
import { ValidationHelper } from '../helpers';

/**
 * Service xử lý logic liên quan đến kết nối giữa các bước trong task của người dùng
 */
@Injectable()
export class UserConnectionService {
  /**
   * Logger cho UserConnectionService
   */
  private readonly logger = new Logger(UserConnectionService.name);

  /**
   * Constructor
   * @param userStepConnectionRepository Repository xử lý dữ liệu kết nối giữa các bước trong task của người dùng
   * @param userStepRepository Repository xử lý dữ liệu bước trong task của người dùng
   * @param userTaskRepository Repository xử lý dữ liệu nhiệm vụ của người dùng
   * @param validationHelper Helper xử lý validation
   */
  constructor(
    private readonly userStepConnectionRepository: UserStepConnectionRepository,
    private readonly userStepRepository: UserStepRepository,
    private readonly userTaskRepository: UserTaskRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo mới kết nối giữa các bước
   * @param userId ID của người dùng
   * @param createDto Thông tin kết nối cần tạo
   * @returns Kết nối đã tạo
   */
  async create(userId: number, createDto: CreateUserStepConnectionDto): Promise<UserStepConnectionResponseDto> {
    try {
      this.logger.log(`Tạo kết nối mới cho nhiệm vụ ${createDto.taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại và thuộc về người dùng
      const task = await this.userTaskRepository.findById(createDto.taskId, userId);
      this.validationHelper.validateTaskFullCheck(task, userId);
      this.validationHelper.validateTaskStatusForUpdate(task);

      // Kiểm tra step nguồn và đích không trùng nhau
      this.validationHelper.validateConnectionSteps(createDto.fromStepId, createDto.toStepId);

      // Kiểm tra tên trường kết nối hợp lệ
      this.validationHelper.validateConnectionField(createDto.outputField);
      this.validationHelper.validateConnectionField(createDto.inputField);

      // Kiểm tra step nguồn tồn tại và thuộc về nhiệm vụ
      const fromStep = await this.userStepRepository.findById(createDto.fromStepId, createDto.taskId);
      this.validationHelper.validateStepFullCheck(fromStep, createDto.taskId);

      // Kiểm tra step đích tồn tại và thuộc về nhiệm vụ
      const toStep = await this.userStepRepository.findById(createDto.toStepId, createDto.taskId);
      this.validationHelper.validateStepFullCheck(toStep, createDto.taskId);

      // Kiểm tra các step có thuộc cùng một nhiệm vụ không
      // Đảm bảo cả hai step đều tồn tại trước khi kiểm tra
      this.validationHelper.validateStepExists(fromStep);
      this.validationHelper.validateStepExists(toStep);
      this.validationHelper.validateStepsSameTask(fromStep, toStep);

      // Kiểm tra kết nối đã tồn tại chưa
      const connectionExists = await this.userStepConnectionRepository.isConnectionExists(
        createDto.taskId,
        createDto.fromStepId,
        createDto.toStepId,
        createDto.outputField,
        createDto.inputField
      );
      this.validationHelper.validateConnectionNotDuplicate(connectionExists);

      // Kiểm tra kết nối không tạo vòng tròn
      const existingConnections = await this.userStepConnectionRepository.findAllByTaskId(createDto.taskId);
      this.validationHelper.validateNoCircularReference(
        existingConnections,
        createDto.fromStepId,
        createDto.toStepId
      );

      // Tạo kết nối mới
      const newConnection = await this.userStepConnectionRepository.createConnection({
        taskId: createDto.taskId,
        fromStepId: createDto.fromStepId,
        toStepId: createDto.toStepId,
        outputField: createDto.outputField,
        inputField: createDto.inputField,
      });

      // Chuyển đổi sang DTO trả về
      return plainToInstance(UserStepConnectionResponseDto, newConnection, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo kết nối: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_CREATION_FAILED,
        'Lỗi khi tạo kết nối'
      );
    }
  }

  /**
   * Cập nhật kết nối giữa các bước
   * @param connectionId ID của kết nối
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật
   * @returns Kết nối đã cập nhật
   */
  async update(
    connectionId: string,
    taskId: string,
    userId: number,
    updateDto: UpdateUserStepConnectionDto
  ): Promise<UserStepConnectionResponseDto> {
    try {
      this.logger.log(`Cập nhật kết nối ${connectionId} của nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại và thuộc về người dùng
      const task = await this.userTaskRepository.findById(taskId, userId);
      this.validationHelper.validateTaskFullCheck(task, userId);
      this.validationHelper.validateTaskStatusForUpdate(task);

      // Kiểm tra kết nối tồn tại và thuộc về nhiệm vụ
      const connection = await this.userStepConnectionRepository.findById(connectionId, taskId);
      this.validationHelper.validateConnectionFullCheck(connection, taskId);

      // Đảm bảo connection không null sau khi validate
      if (!connection) {
        throw new AppException(
          CONNECTION_ERROR_CODES.CONNECTION_NOT_FOUND,
          'Không tìm thấy kết nối'
        );
      }

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<UpdateUserStepConnectionDto> = {};

      // Kiểm tra và cập nhật step nguồn
      if (updateDto.fromStepId) {
        const fromStep = await this.userStepRepository.findById(updateDto.fromStepId, taskId);
        this.validationHelper.validateStepFullCheck(fromStep, taskId);
        updateData.fromStepId = updateDto.fromStepId;
      }

      // Kiểm tra và cập nhật step đích
      if (updateDto.toStepId) {
        const toStep = await this.userStepRepository.findById(updateDto.toStepId, taskId);
        this.validationHelper.validateStepFullCheck(toStep, taskId);
        updateData.toStepId = updateDto.toStepId;
      }

      // Kiểm tra step nguồn và đích không trùng nhau
      // Đảm bảo connection không null (đã được kiểm tra ở trên)
      const fromStepId = updateData.fromStepId || connection.fromStepId;
      const toStepId = updateData.toStepId || connection.toStepId;
      this.validationHelper.validateConnectionSteps(fromStepId, toStepId);

      // Kiểm tra và cập nhật tên trường output
      if (updateDto.outputField) {
        this.validationHelper.validateConnectionField(updateDto.outputField);
        updateData.outputField = updateDto.outputField;
      }

      // Kiểm tra và cập nhật tên trường input
      if (updateDto.inputField) {
        this.validationHelper.validateConnectionField(updateDto.inputField);
        updateData.inputField = updateDto.inputField;
      }

      // Kiểm tra kết nối đã tồn tại chưa
      if (updateData.fromStepId || updateData.toStepId || updateData.outputField || updateData.inputField) {
        // Đảm bảo connection không null (đã được kiểm tra ở trên)
        const connectionExists = await this.userStepConnectionRepository.isConnectionExists(
          taskId,
          fromStepId,
          toStepId,
          updateData.outputField || connection.outputField,
          updateData.inputField || connection.inputField,
          connectionId
        );
        this.validationHelper.validateConnectionNotDuplicate(connectionExists);
      }

      // Kiểm tra kết nối không tạo vòng tròn
      if (updateData.fromStepId || updateData.toStepId) {
        const existingConnections = await this.userStepConnectionRepository.findAllByTaskId(taskId);
        // Loại bỏ kết nối hiện tại khỏi danh sách kiểm tra
        const filteredConnections = existingConnections.filter(conn => conn.connectionId !== connectionId);
        this.validationHelper.validateNoCircularReference(
          filteredConnections,
          fromStepId,
          toStepId
        );
      }

      // Cập nhật kết nối
      const updatedConnection = await this.userStepConnectionRepository.updateConnection(
        connectionId,
        taskId,
        updateData
      );

      // Chuyển đổi sang DTO trả về
      return plainToInstance(UserStepConnectionResponseDto, updatedConnection, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật kết nối ${connectionId}: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_UPDATE_FAILED,
        `Lỗi khi cập nhật kết nối ${connectionId}`
      );
    }
  }

  /**
   * Lấy thông tin kết nối theo ID
   * @param connectionId ID của kết nối
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @returns Thông tin kết nối
   */
  async findById(connectionId: string, taskId: string, userId: number): Promise<UserStepConnectionResponseDto> {
    try {
      this.logger.log(`Lấy thông tin kết nối ${connectionId} của nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại và thuộc về người dùng
      const task = await this.userTaskRepository.findById(taskId, userId);
      this.validationHelper.validateTaskExists(task);
      this.validationHelper.validateTaskNotDeleted(task);

      // Tìm kết nối theo ID
      const connection = await this.userStepConnectionRepository.findById(connectionId, taskId);
      this.validationHelper.validateConnectionExists(connection);

      // Chuyển đổi sang DTO trả về
      return plainToInstance(UserStepConnectionResponseDto, connection, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin kết nối ${connectionId}: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_FETCH_FAILED,
        `Lỗi khi lấy thông tin kết nối ${connectionId}`
      );
    }
  }

  /**
   * Lấy danh sách kết nối của nhiệm vụ
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách kết nối với phân trang
   */
  async findAll(
    taskId: string,
    userId: number,
    queryDto: QueryUserStepConnectionDto
  ): Promise<PaginatedResult<UserStepConnectionResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách kết nối của nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại và thuộc về người dùng
      const task = await this.userTaskRepository.findById(taskId, userId);
      this.validationHelper.validateTaskExists(task);
      this.validationHelper.validateTaskNotDeleted(task);

      // Áp dụng taskId vào query
      queryDto.taskId = taskId;

      // Lấy danh sách kết nối
      const result = await this.userStepConnectionRepository.findAll(queryDto);

      // Chuyển đổi sang DTO trả về
      const items = plainToInstance(UserStepConnectionResponseDto, result.items, {
        excludeExtraneousValues: true,
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách kết nối của nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_FETCH_FAILED,
        `Lỗi khi lấy danh sách kết nối của nhiệm vụ ${taskId}`
      );
    }
  }

  /**
   * Xóa kết nối
   * @param connectionId ID của kết nối
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công
   */
  async delete(connectionId: string, taskId: string, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa kết nối ${connectionId} của nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại và thuộc về người dùng
      const task = await this.userTaskRepository.findById(taskId, userId);
      this.validationHelper.validateTaskFullCheck(task, userId);
      this.validationHelper.validateTaskStatusForUpdate(task);

      // Kiểm tra kết nối tồn tại và thuộc về nhiệm vụ
      const connection = await this.userStepConnectionRepository.findById(connectionId, taskId);
      this.validationHelper.validateConnectionFullCheck(connection, taskId);

      // Xóa kết nối
      return await this.userStepConnectionRepository.deleteConnection(connectionId, taskId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa kết nối ${connectionId}: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_DELETE_FAILED,
        `Lỗi khi xóa kết nối ${connectionId}`
      );
    }
  }
}
