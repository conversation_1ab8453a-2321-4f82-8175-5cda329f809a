import { Injectable, Logger } from '@nestjs/common';
import { UserTaskRepository } from '../../repositories';
import { CreateUserTaskDto, UpdateUserTaskDto, QueryUserTaskDto } from '../dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { USER_TASK_ERROR_CODES } from '@modules/task/user/exceptions';
import { plainToInstance } from 'class-transformer';
import { UserTaskResponseDto } from '@modules/task/user/dto';
import { ValidationHelper } from '../helpers';
import {TaskStatus} from "@modules/task/enums";

/**
 * Service xử lý logic liên quan đến task của người dùng
 */
@Injectable()
export class UserTaskService {
  /**
   * Logger cho UserTaskService
   */
  private readonly logger = new Logger(UserTaskService.name);

  /**
   * Constructor
   * @param userTaskRepository Repository xử lý dữ liệu task của người dùng
   * @param validationHelper
   */
  constructor(
    private readonly userTaskRepository: UserTaskRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo mới nhiệm vụ
   * @param userId ID của người dùng
   * @param createDto Thông tin nhiệm vụ cần tạo
   * @returns Nhiệm vụ đã tạo
   */
  async create(userId: number, createDto: CreateUserTaskDto): Promise<UserTaskResponseDto> {
    try {
      this.logger.log(`Tạo nhiệm vụ mới cho người dùng ${userId}`);

      // Kiểm tra tính hợp lệ của tên nhiệm vụ
      this.validationHelper.validateTaskName(createDto.taskName);

      // Tạo nhiệm vụ mới
      const newTask = await this.userTaskRepository.createTask({
        userId,
        agentId: createDto.agentId,
        taskName: createDto.taskName,
        taskDescription: createDto.taskDescription,
      });

      // Chuyển đổi sang DTO trả về
      return plainToInstance(UserTaskResponseDto, newTask, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo nhiệm vụ: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_CREATION_FAILED,
        'Lỗi khi tạo nhiệm vụ'
      );
    }
  }

  /**
   * Cập nhật nhiệm vụ
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật
   * @returns Nhiệm vụ đã cập nhật
   */
  async update(taskId: string, userId: number, updateDto: UpdateUserTaskDto): Promise<UserTaskResponseDto> {
    try {
      this.logger.log(`Cập nhật nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại
      const existingTask = await this.userTaskRepository.findById(taskId, userId);

      // Kiểm tra nhiệm vụ tồn tại, không bị xóa và thuộc về người dùng
      this.validationHelper.validateTaskFullCheck(existingTask, userId);

      // Kiểm tra trạng thái nhiệm vụ có cho phép cập nhật không
      this.validationHelper.validateTaskStatusForUpdate(existingTask);

      // Kiểm tra tên nhiệm vụ nếu có cập nhật
      if (updateDto.taskName) {
        this.validationHelper.validateTaskName(updateDto.taskName);
      }

      // Cập nhật nhiệm vụ
      const updatedTask = await this.userTaskRepository.updateTask(taskId, userId, {
        taskName: updateDto.taskName,
        taskDescription: updateDto.taskDescription,
        active: updateDto.active,
        agentId: updateDto.agentId,
      });

      // Chuyển đổi sang DTO trả về
      return plainToInstance(UserTaskResponseDto, updatedTask, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_UPDATE_FAILED,
        `Lỗi khi cập nhật nhiệm vụ ${taskId}`
      );
    }
  }

  /**
   * Lấy thông tin nhiệm vụ theo ID
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @returns Thông tin nhiệm vụ
   */
  async findById(taskId: string, userId: number): Promise<UserTaskResponseDto> {
    try {
      this.logger.log(`Lấy thông tin nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Tìm nhiệm vụ theo ID
      const task = await this.userTaskRepository.findById(taskId, userId);

      // Kiểm tra nhiệm vụ tồn tại và không bị xóa
      this.validationHelper.validateTaskExists(task);
      this.validationHelper.validateTaskNotDeleted(task);

      // Chuyển đổi sang DTO trả về
      return plainToInstance(UserTaskResponseDto, task, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_FETCH_FAILED,
        `Lỗi khi lấy thông tin nhiệm vụ ${taskId}`
      );
    }
  }

  /**
   * Lấy danh sách nhiệm vụ của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách nhiệm vụ với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserTaskDto): Promise<PaginatedResult<UserTaskResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách nhiệm vụ của người dùng ${userId}`);

      // Lấy danh sách nhiệm vụ
      const result = await this.userTaskRepository.findAll(userId, queryDto);

      // Chuyển đổi sang DTO trả về
      const items = result.items.map(task =>
        plainToInstance(UserTaskResponseDto, task, {
          excludeExtraneousValues: true,
        })
      );

      return {
        ...result,
        items,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách nhiệm vụ: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_FETCH_FAILED,
        'Lỗi khi lấy danh sách nhiệm vụ'
      );
    }
  }

  /**
   * Xóa nhiệm vụ
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công
   */
  async delete(taskId: string, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại
      const task = await this.userTaskRepository.findById(taskId, userId);

      // Kiểm tra nhiệm vụ tồn tại, không bị xóa và thuộc về người dùng
      this.validationHelper.validateTaskFullCheck(task, userId);

      // Xóa nhiệm vụ
      return await this.userTaskRepository.softDeleteTask(taskId, userId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_DELETE_FAILED,
        `Lỗi khi xóa nhiệm vụ ${taskId}`
      );
    }
  }


  /**
   * Gửi duyệt nhiệm vụ và chuyển trạng thái thành PENDING
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   */
  async submitForApproval(taskId: string, userId: number): Promise<void> {
    try {
      this.logger.log(`Gửi duyệt nhiệm vụ ${taskId} của người dùng ${userId}`);

      // Kiểm tra nhiệm vụ tồn tại
      const task = await this.userTaskRepository.findById(taskId, userId);

      // Kiểm tra nhiệm vụ tồn tại, không bị xóa và thuộc về người dùng
      this.validationHelper.validateTaskFullCheck(task, userId);

      // Cập nhật trạng thái thành PENDING
      await this.userTaskRepository.updateTask(taskId, userId, {
        status: TaskStatus.PENDING,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi gửi duyệt nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_STATUS_CHANGE_FAILED,
        `Lỗi khi gửi duyệt nhiệm vụ ${taskId}`
      );
    }
  }
}
