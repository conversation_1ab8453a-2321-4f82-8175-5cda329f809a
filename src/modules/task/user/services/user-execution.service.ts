import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { UserTaskExecutionRepository } from '../../repositories';
import { QueryUserTaskExecutionDto } from '@modules/task/user/dto';
import { UserTaskExecutionResponseDto } from '@modules/task/user/dto';
import { USER_EXECUTION_ERROR_CODES } from '@modules/task/user/exceptions';

/**
 * Service xử lý logic liên quan đến phiên thực thi nhiệm vụ của người dùng
 */
@Injectable()
export class UserExecutionService {
  private readonly logger = new Logger(UserExecutionService.name);

  /**
   * Constructor
   * @param userTaskExecutionRepository Repository xử lý dữ liệu phiên thực thi nhiệm vụ
   */
  constructor(
    private readonly userTaskExecutionRepository: UserTaskExecutionRepository,
  ) {}

  /**
   * Lấy danh sách phiên thực thi nhiệm vụ với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách phiên thực thi với phân trang
   */
  async findAll(queryDto: QueryUserTaskExecutionDto): Promise<PaginatedResult<UserTaskExecutionResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách phiên thực thi với query: ${JSON.stringify(queryDto)}`);

      // Lấy danh sách phiên thực thi từ repository
      const result = await this.userTaskExecutionRepository.findAll(queryDto);

      // Chuyển đổi sang DTO trả về
      const items = result.items.map(execution =>
        plainToInstance(UserTaskExecutionResponseDto, execution, {
          excludeExtraneousValues: true,
        })
      );

      return {
        ...result,
        items,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách phiên thực thi: ${error.message}`, error.stack);
      throw new AppException(
        USER_EXECUTION_ERROR_CODES.USER_EXECUTION_FETCH_FAILED,
        'Lỗi khi lấy danh sách phiên thực thi'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết phiên thực thi theo ID
   * @param executionId ID của phiên thực thi
   * @returns Thông tin chi tiết phiên thực thi
   */
  async findById(executionId: string): Promise<UserTaskExecutionResponseDto> {
    try {
      this.logger.log(`Lấy thông tin chi tiết phiên thực thi ${executionId}`);

      // Tìm phiên thực thi theo ID
      const execution = await this.userTaskExecutionRepository.findById(executionId);

      // Kiểm tra phiên thực thi tồn tại
      if (!execution) {
        throw new AppException(
          USER_EXECUTION_ERROR_CODES.USER_EXECUTION_NOT_FOUND,
          `Không tìm thấy phiên thực thi với ID ${executionId}`
        );
      }

      // Chuyển đổi sang DTO trả về
      return plainToInstance(UserTaskExecutionResponseDto, execution, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin chi tiết phiên thực thi ${executionId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_EXECUTION_ERROR_CODES.USER_EXECUTION_FETCH_FAILED,
        `Lỗi khi lấy thông tin chi tiết phiên thực thi ${executionId}`
      );
    }
  }
}
