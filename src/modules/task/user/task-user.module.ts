import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  UserTask,
  UserStep,
  UserTaskExecution,
  UserStepConnection
} from '../entities';
import {
  UserTaskRepository,
  UserStepRepository,
  UserTaskExecutionRepository,
  UserStepConnectionRepository
} from '../repositories';
import {
  UserTaskController,
  UserStepController,
  UserConnectionController,
  UserExecutionController
} from './controllers';
import {
  UserTaskService,
  UserStepService,
  UserConnectionService,
  UserExecutionService
} from './services';
import { ValidationHelper } from './helpers';

/**
 * Module quản lý task cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserTask,
      UserStep,
      UserTaskExecution,
      UserStepConnection
    ])
  ],
  controllers: [
    UserTask<PERSON>ontroller,
    UserStepController,
    UserConnectionController,
    UserExecutionController
  ],
  providers: [
    UserTaskRepository,
    UserStepRepository,
    UserTaskExecutionRepository,
    UserStepConnectionRepository,
    ValidationHelper,
    UserTaskService,
    UserStepService,
    UserConnectionService,
    UserExecutionService
  ],
  exports: [
    UserTaskService,
    UserStepService,
    UserConnectionService,
    UserExecutionService
  ],
})
export class TaskUserModule {}
