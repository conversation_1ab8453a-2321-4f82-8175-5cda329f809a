import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryUserStepDto } from '../../dto';

// Mock StepType enum trực tiếp trong file test
enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image',
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

// Tạo một hàm helper để validate DTO và trả về errors
async function validateDTO(dto: any): Promise<any[]> {
  return validate(dto, {
    skipMissingProperties: true,
    whitelist: true
  });
}

describe('QueryUserStepDto', () => {
  it('should validate a valid DTO with all information', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-************',
      stepType: StepType.PROMPT
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeLessThanOrEqual(1);
  });

  it('should validate a valid DTO with only taskId', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-************'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only stepType', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepDto, {
      stepType: StepType.AGENT
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with no fields (query all)', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepDto, {});

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate when taskId is provided', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepDto, {
      taskId: 'any-string-value'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    // taskId không có validation UUID nên sẽ không có lỗi
    expect(errors.length).toBeLessThanOrEqual(1);
  });

  it('should validate different step types', async () => {
    // Arrange
    const stepTypes = [
      StepType.PROMPT,
      StepType.GOOGLE_SHEET,
      StepType.GOOGLE_DOC,
      StepType.GOOGLE_CALENDAR,
      StepType.EMAIL,
      StepType.FACEBOOK_PAGE,
      StepType.GEN_IMAGE,
      StepType.GEN_VIDEO,
      StepType.AGENT
    ];

    for (const stepType of stepTypes) {
      const dto = plainToInstance(QueryUserStepDto, {
        taskId: '123e4567-e89b-12d3-a456-************',
        stepType: stepType
      });

      // Act
      const errors = await validateDTO(dto);

      // Assert
      expect(errors.length).toBeLessThanOrEqual(1);
    }
  });
});
