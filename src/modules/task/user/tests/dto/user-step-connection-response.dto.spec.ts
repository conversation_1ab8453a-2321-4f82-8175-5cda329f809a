import { plainToInstance } from 'class-transformer';
import { UserStepConnectionResponseDto } from '../../dto/connection/user-step-connection-response.dto';

describe('UserStepConnectionResponseDto', () => {
  it('should correctly transform from plain object to DTO with all information', () => {
    // Arrange
    const plainObject = {
      connectionId: '123e4567-e89b-12d3-a456-426614174000',
      taskId: '123e4567-e89b-12d3-a456-426614174001',
      fromStepId: '123e4567-e89b-12d3-a456-426614174002',
      toStepId: '123e4567-e89b-12d3-a456-426614174003',
      outputField: 'emailContent',
      inputField: 'content',
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      extraField: 'This field should not be transformed'
    };

    // Act
    const dto = plainToInstance(UserStepConnectionResponseDto, plainObject, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserStepConnectionResponseDto);
    expect(dto.connectionId).toBe('123e4567-e89b-12d3-a456-426614174000');
    expect(dto.taskId).toBe('123e4567-e89b-12d3-a456-426614174001');
    expect(dto.fromStepId).toBe('123e4567-e89b-12d3-a456-426614174002');
    expect(dto.toStepId).toBe('123e4567-e89b-12d3-a456-426614174003');
    expect(dto.outputField).toBe('emailContent');
    expect(dto.inputField).toBe('content');
    expect(dto.createdAt).toBe(1625097600000);
    // updatedAt is not part of the DTO, so it should not be transformed
    expect((dto as any).updatedAt).toBeUndefined();
    // Fields not declared in DTO should not be transformed
    expect((dto as any).extraField).toBeUndefined();
  });

  it('should correctly transform from plain object to DTO with different time fields', () => {
    // Arrange
    const plainObject = {
      connectionId: '123e4567-e89b-12d3-a456-426614174000',
      taskId: '123e4567-e89b-12d3-a456-426614174001',
      fromStepId: '123e4567-e89b-12d3-a456-426614174002',
      toStepId: '123e4567-e89b-12d3-a456-426614174003',
      outputField: 'emailContent',
      inputField: 'content',
      createdAt: 1625097600000,
      updatedAt: 1625097700000
    };

    // Act
    const dto = plainToInstance(UserStepConnectionResponseDto, plainObject, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserStepConnectionResponseDto);
    expect(dto.createdAt).toBe(1625097600000);
    // updatedAt is not part of the DTO, so it should not be transformed
    expect((dto as any).updatedAt).toBeUndefined();
  });

  it('should correctly transform from plain object to DTO with different fields', () => {
    // Arrange
    const testCases = [
      {
        outputField: 'emailContent',
        inputField: 'content'
      },
      {
        outputField: 'imageUrl',
        inputField: 'imageSource'
      },
      {
        outputField: 'documentText',
        inputField: 'inputText'
      }
    ];

    for (const testCase of testCases) {
      const plainObject = {
        connectionId: '123e4567-e89b-12d3-a456-426614174000',
        taskId: '123e4567-e89b-12d3-a456-426614174001',
        fromStepId: '123e4567-e89b-12d3-a456-426614174002',
        toStepId: '123e4567-e89b-12d3-a456-426614174003',
        outputField: testCase.outputField,
        inputField: testCase.inputField,
        createdAt: 1625097600000
      };

      // Act
      const dto = plainToInstance(UserStepConnectionResponseDto, plainObject, {
        excludeExtraneousValues: true,
        enableImplicitConversion: true
      });

      // Assert
      expect(dto).toBeInstanceOf(UserStepConnectionResponseDto);
      expect(dto.outputField).toBe(testCase.outputField);
      expect(dto.inputField).toBe(testCase.inputField);
    }
  });
});
