import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateUserStepConnectionDto } from '../../dto';

describe('CreateUserStepConnectionDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174001',
      toStepId: '123e4567-e89b-12d3-a456-426614174002',
      outputField: 'emailContent',
      inputField: 'content'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('ph<PERSON>i báo lỗi khi fromStepId bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      toStepId: '123e4567-e89b-12d3-a456-426614174002',
      outputField: 'emailContent',
      inputField: 'content'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi fromStepId không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: 123,
      toStepId: '123e4567-e89b-12d3-a456-426614174002',
      outputField: 'emailContent',
      inputField: 'content'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('phải báo lỗi khi toStepId bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174001',
      outputField: 'emailContent',
      inputField: 'content'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi toStepId không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174001',
      toStepId: 123,
      outputField: 'emailContent',
      inputField: 'content'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('phải báo lỗi khi outputField bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174001',
      toStepId: '123e4567-e89b-12d3-a456-426614174002',
      inputField: 'content'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi outputField không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174001',
      toStepId: '123e4567-e89b-12d3-a456-426614174002',
      outputField: 123,
      inputField: 'content'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('phải báo lỗi khi outputField vượt quá 255 ký tự', async () => {
    // Arrange
    const longOutputField = 'a'.repeat(256);
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174001',
      toStepId: '123e4567-e89b-12d3-a456-426614174002',
      outputField: longOutputField,
      inputField: 'content'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });

  it('phải báo lỗi khi inputField bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174001',
      toStepId: '123e4567-e89b-12d3-a456-426614174002',
      outputField: 'emailContent'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi inputField không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174001',
      toStepId: '123e4567-e89b-12d3-a456-426614174002',
      outputField: 'emailContent',
      inputField: 123
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('phải báo lỗi khi inputField vượt quá 255 ký tự', async () => {
    // Arrange
    const longInputField = 'a'.repeat(256);
    const dto = plainToInstance(CreateUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174001',
      toStepId: '123e4567-e89b-12d3-a456-426614174002',
      outputField: 'emailContent',
      inputField: longInputField
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });
});
