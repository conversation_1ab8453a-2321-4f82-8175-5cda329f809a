module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../../../../../',
  testRegex: 'src/modules/task/user/tests/dto/.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['src/modules/task/user/dto/**/*.(t|j)s'],
  coverageDirectory: './coverage/task-user-dto',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@modules/(.*)$': '<rootDir>/src/modules/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
    '^@common/(.*)$': '<rootDir>/src/common/$1',
    '^@utils/(.*)$': '<rootDir>/src/shared/utils/$1',
    '^@database/(.*)$': '<rootDir>/src/database/$1',
    '^@config$': '<rootDir>/src/config',
  },
  passWithNoTests: true,
  testTimeout: 30000,
  maxWorkers: 1,
  globals: {
    'ts-jest': {
      isolatedModules: true,
    },
  },
}
