import { plainToInstance } from 'class-transformer';
import { UserStepResponseDto } from '../../dto';

// Mock StepType enum trực tiếp trong file test
enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image',
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

describe('UserStepResponseDto', () => {
  it('should correctly transform from plain object to DTO with all information', () => {
    // Arrange
    const plainObject = {
      stepId: '123e4567-e89b-12d3-a456-426614174000',
      taskId: '123e4567-e89b-12d3-a456-426614174001',
      orderIndex: 1,
      stepName: 'Nhập nội dung email',
      stepDescription: '<PERSON>ướ<PERSON> này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true,
        defaultValue: ''
      },
      googleUserAuthId: null,
      facebookPageId: null,
      createdAt: 1625097600000,
      extraField: 'This field should not be transformed'
    };

    // Act
    const dto = plainToInstance(UserStepResponseDto, plainObject, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserStepResponseDto);
    expect(dto.stepId).toBe('123e4567-e89b-12d3-a456-426614174000');
    expect(dto.taskId).toBe('123e4567-e89b-12d3-a456-426614174001');
    expect(dto.orderIndex).toBe(1);
    expect(dto.stepName).toBe('Nhập nội dung email');
    expect(dto.stepDescription).toBe('Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng');
    expect(dto.stepType).toBe(StepType.PROMPT);
    // Kiểm tra stepConfig tồn tại nhưng không kiểm tra nội dung chi tiết
    expect(dto.stepConfig).toBeDefined();
    expect(dto.googleUserAuthId).toBeNull();
    expect(dto.facebookPageId).toBeNull();
    expect(dto.createdAt).toBe(1625097600000);
    // Fields not declared in DTO should not be transformed
    expect((dto as any).extraField).toBeUndefined();
  });

  it('should correctly transform from plain object to DTO when stepDescription is null', () => {
    // Arrange
    const plainObject = {
      stepId: '123e4567-e89b-12d3-a456-426614174000',
      taskId: '123e4567-e89b-12d3-a456-426614174001',
      orderIndex: 1,
      stepName: 'Nhập nội dung email',
      stepDescription: null,
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true
      },
      googleUserAuthId: null,
      facebookPageId: null,
      createdAt: 1625097600000
    };

    // Act
    const dto = plainToInstance(UserStepResponseDto, plainObject, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserStepResponseDto);
    expect(dto.stepDescription).toBeNull();
  });

  it('should correctly transform from plain object to DTO when stepConfig is null', () => {
    // Arrange
    const plainObject = {
      stepId: '123e4567-e89b-12d3-a456-426614174000',
      taskId: '123e4567-e89b-12d3-a456-426614174001',
      orderIndex: 1,
      stepName: 'Nhập nội dung email',
      stepDescription: 'Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepType: StepType.PROMPT,
      stepConfig: null,
      googleUserAuthId: null,
      facebookPageId: null,
      createdAt: 1625097600000
    };

    // Act
    const dto = plainToInstance(UserStepResponseDto, plainObject, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserStepResponseDto);
    expect(dto.stepConfig).toBeNull();
  });

  it('should correctly transform from plain object to DTO with different step types', () => {
    // Arrange
    const stepTypes = [
      StepType.PROMPT,
      StepType.GOOGLE_SHEET,
      StepType.GOOGLE_DOC,
      StepType.GOOGLE_CALENDAR,
      StepType.EMAIL,
      StepType.FACEBOOK_PAGE,
      StepType.GEN_IMAGE,
      StepType.GEN_VIDEO,
      StepType.AGENT
    ];

    for (const stepType of stepTypes) {
      const plainObject = {
        stepId: '123e4567-e89b-12d3-a456-426614174000',
        taskId: '123e4567-e89b-12d3-a456-426614174001',
        orderIndex: 1,
        stepName: `Bước ${stepType}`,
        stepDescription: `Mô tả cho bước ${stepType}`,
        stepType: stepType,
        stepConfig: {
          stepType: stepType
        },
        googleUserAuthId: null,
        facebookPageId: null,
        createdAt: 1625097600000
      };

      // Act
      const dto = plainToInstance(UserStepResponseDto, plainObject, {
        excludeExtraneousValues: true,
        enableImplicitConversion: true
      });

      // Assert
      expect(dto).toBeInstanceOf(UserStepResponseDto);
      expect(dto.stepType).toBe(stepType);
    }
  });
});
