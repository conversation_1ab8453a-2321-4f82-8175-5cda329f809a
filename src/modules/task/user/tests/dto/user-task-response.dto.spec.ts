import { plainToInstance } from 'class-transformer';
import { UserTaskResponseDto } from '../../dto';
import { TaskStatus } from '@modules/task/enums';

describe('UserTaskResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO với đầy đủ thông tin', () => {
    // Arrange
    const plainObject = {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      userId: 123,
      agentId: '123e4567-e89b-12d3-a456-426614174001',
      taskName: 'Tạo chiến dịch email marketing',
      taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
      status: TaskStatus.DRAFT,
      active: true,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      extraField: 'Trường này không nên đượ<PERSON> chuyển đổi'
    };

    // Act
    const dto = plainToInstance(UserTaskResponseDto, plainObject, { 
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserTaskResponseDto);
    expect(dto.taskId).toBe('123e4567-e89b-12d3-a456-426614174000');
    expect(dto.userId).toBe(123);
    expect(dto.agentId).toBe('123e4567-e89b-12d3-a456-426614174001');
    expect(dto.taskName).toBe('Tạo chiến dịch email marketing');
    expect(dto.taskDescription).toBe('Tạo chiến dịch email marketing cho sản phẩm mới ra mắt');
    expect(dto.status).toBe(TaskStatus.DRAFT);
    expect(dto.active).toBe(true);
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625097600000);
    // Trường không được khai báo trong DTO không nên được chuyển đổi
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO khi agentId là null', () => {
    // Arrange
    const plainObject = {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      userId: 123,
      agentId: null,
      taskName: 'Tạo chiến dịch email marketing',
      taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
      status: TaskStatus.DRAFT,
      active: true,
      createdAt: 1625097600000,
      updatedAt: 1625097600000
    };

    // Act
    const dto = plainToInstance(UserTaskResponseDto, plainObject, { 
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserTaskResponseDto);
    expect(dto.agentId).toBeNull();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO khi taskDescription là null', () => {
    // Arrange
    const plainObject = {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      userId: 123,
      agentId: '123e4567-e89b-12d3-a456-426614174001',
      taskName: 'Tạo chiến dịch email marketing',
      taskDescription: null,
      status: TaskStatus.DRAFT,
      active: true,
      createdAt: 1625097600000,
      updatedAt: 1625097600000
    };

    // Act
    const dto = plainToInstance(UserTaskResponseDto, plainObject, { 
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserTaskResponseDto);
    expect(dto.taskDescription).toBeNull();
  });

  it('nên chuyển đổi đúng các trạng thái khác nhau của task', () => {
    // Arrange
    const statuses = [TaskStatus.DRAFT, TaskStatus.PENDING, TaskStatus.APPROVED, TaskStatus.REJECTED];
    
    for (const status of statuses) {
      const plainObject = {
        taskId: '123e4567-e89b-12d3-a456-426614174000',
        userId: 123,
        agentId: '123e4567-e89b-12d3-a456-426614174001',
        taskName: 'Tạo chiến dịch email marketing',
        taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
        status: status,
        active: true,
        createdAt: 1625097600000,
        updatedAt: 1625097600000
      };

      // Act
      const dto = plainToInstance(UserTaskResponseDto, plainObject, { 
        excludeExtraneousValues: true,
        enableImplicitConversion: true
      });

      // Assert
      expect(dto).toBeInstanceOf(UserTaskResponseDto);
      expect(dto.status).toBe(status);
    }
  });
});
