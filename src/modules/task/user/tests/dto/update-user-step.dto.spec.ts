import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateUserStepDto } from '../../dto';

// Mock StepType enum trực tiếp trong file test
enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image',
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

// Tạo một hàm helper để validate DTO và trả về errors
async function validateDTO(dto: any): Promise<any[]> {
  return validate(dto, {
    skipMissingProperties: false,
    whitelist: true,
    forbidNonWhitelisted: true
  });
}

describe('UpdateUserStepDto', () => {
  it('should validate a valid DTO with all information', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      stepName: 'Nhập nội dung email',
      stepDescription: '<PERSON><PERSON>ớ<PERSON> này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true,
        defaultValue: ''
      },
      googleUserAuthId: '123e4567-e89b-12d3-a456-426614174000',
      facebookPageId: '123e4567-e89b-12d3-a456-426614174001'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only stepName', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      stepName: 'Nhập nội dung email mới'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only stepDescription', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      stepDescription: 'Mô tả mới cho bước nhập nội dung email'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only stepType', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      stepType: StepType.AGENT
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only stepConfig', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing mới cho sản phẩm',
        inputType: 'text',
        required: false,
        defaultValue: 'Nội dung mặc định'
      }
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only googleUserAuthId', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      googleUserAuthId: '123e4567-e89b-12d3-a456-426614174000'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only facebookPageId', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      facebookPageId: '123e4567-e89b-12d3-a456-426614174001'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with no fields (empty update)', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {});

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when stepName is not a string', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      stepName: 123
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const stepNameError = errors.find(e => e.property === 'stepName');
    expect(stepNameError).toBeDefined();
    expect(stepNameError.constraints).toHaveProperty('isString');
  });

  it('should fail validation when stepName exceeds 255 characters', async () => {
    // Arrange
    const longStepName = 'a'.repeat(256);
    const dto = plainToInstance(UpdateUserStepDto, {
      stepName: longStepName
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const stepNameError = errors.find(e => e.property === 'stepName');
    expect(stepNameError).toBeDefined();
    expect(stepNameError.constraints).toHaveProperty('maxLength');
  });

  it('should fail validation when stepDescription is not a string', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      stepDescription: 123
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const stepDescriptionError = errors.find(e => e.property === 'stepDescription');
    expect(stepDescriptionError).toBeDefined();
    expect(stepDescriptionError.constraints).toHaveProperty('isString');
  });

  // Bỏ test này vì stepType không có validator @IsEnum
  // it('should fail validation when stepType is not a valid StepType enum value', async () => {
  //   // Arrange
  //   const dto = plainToInstance(UpdateUserStepDto, {
  //     stepType: 'invalid_step_type'
  //   });

  //   // Act
  //   const errors = await validateDTO(dto);

  //   // Assert
  //   expect(errors.length).toBeGreaterThan(0);
  //   const stepTypeError = errors.find(e => e.property === 'stepType');
  //   expect(stepTypeError).toBeDefined();
  //   expect(stepTypeError.constraints).toHaveProperty('isEnum');
  // });

  it('should fail validation when stepConfig is not an object', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      stepConfig: 'invalid_config'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const stepConfigError = errors.find(e => e.property === 'stepConfig');
    expect(stepConfigError).toBeDefined();
    expect(stepConfigError.constraints).toHaveProperty('isObject');
  });

  it('should fail validation when googleUserAuthId is not a string', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      googleUserAuthId: 123 // Not a string
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const googleUserAuthIdError = errors.find(e => e.property === 'googleUserAuthId');
    expect(googleUserAuthIdError).toBeDefined();
    expect(googleUserAuthIdError.constraints).toHaveProperty('isString');
  });

  it('should fail validation when facebookPageId is not a string', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepDto, {
      facebookPageId: 123 // Not a string
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const facebookPageIdError = errors.find(e => e.property === 'facebookPageId');
    expect(facebookPageIdError).toBeDefined();
    expect(facebookPageIdError.constraints).toHaveProperty('isString');
  });
});
