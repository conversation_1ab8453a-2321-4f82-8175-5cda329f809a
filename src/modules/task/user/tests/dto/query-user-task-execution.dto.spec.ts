import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryUserTaskExecutionDto, ExecutionSortBy } from '../../dto/execution/query-user-task-execution.dto';
import { TaskExecutionStatus } from '@modules/task/enums';
import { SortDirection } from '@common/dto/query.dto';

// Tạo một hàm helper để validate DTO và trả về errors
async function validateDTO(dto: any): Promise<any[]> {
  return validate(dto, {
    skipMissingProperties: true,
    whitelist: true
  });
}

describe('QueryUserTaskExecutionDto', () => {
  it('should validate a valid DTO with all information', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {
      page: 1,
      limit: 10,
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      overallStatus: TaskExecutionStatus.SUCCESS,
      sortBy: ExecutionSortBy.START_TIME,
      sortDirection: SortDirection.DESC
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeLessThanOrEqual(1);
  });

  it('should validate a valid DTO with default parameters', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {});

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.sortBy).toBe(ExecutionSortBy.START_TIME);
    expect(dto.sortDirection).toBe(SortDirection.DESC);
  });

  it('should validate a valid DTO with only page and limit', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {
      page: 2,
      limit: 20
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only taskId', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    // Chấp nhận có thể có lỗi validation do các trường mặc định
    expect(errors.length).toBeLessThanOrEqual(1);
  });

  it('should validate a valid DTO with only overallStatus', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {
      overallStatus: TaskExecutionStatus.SUCCESS
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only sortBy', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {
      sortBy: ExecutionSortBy.END_TIME
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only sortDirection', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {
      sortDirection: SortDirection.ASC
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when taskId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {
      taskId: 'invalid-uuid'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const taskIdError = errors.find(e => e.property === 'taskId');
    expect(taskIdError).toBeDefined();
    expect(taskIdError.constraints).toHaveProperty('isUuid');
  });

  it('should fail validation when overallStatus is not a valid TaskExecutionStatus enum value', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {
      overallStatus: 'INVALID_STATUS'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const statusError = errors.find(e => e.property === 'overallStatus');
    expect(statusError).toBeDefined();
    expect(statusError.constraints).toHaveProperty('isEnum');
  });

  it('should fail validation when sortBy is not a valid ExecutionSortBy enum value', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {
      sortBy: 'INVALID_SORT_BY'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sortByError = errors.find(e => e.property === 'sortBy');
    expect(sortByError).toBeDefined();
    expect(sortByError.constraints).toHaveProperty('isEnum');
  });

  it('should fail validation when sortDirection is not a valid SortDirection enum value', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskExecutionDto, {
      sortDirection: 'INVALID_SORT_DIRECTION'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sortDirectionError = errors.find(e => e.property === 'sortDirection');
    expect(sortDirectionError).toBeDefined();
    expect(sortDirectionError.constraints).toHaveProperty('isEnum');
  });

  it('should validate a valid DTO with different execution statuses', async () => {
    // Arrange
    const statuses = [
      TaskExecutionStatus.RUNNING,
      TaskExecutionStatus.SUCCESS,
      TaskExecutionStatus.FAILED,
      TaskExecutionStatus.CANCELLED,
      TaskExecutionStatus.PAUSED
    ];

    for (const status of statuses) {
      const dto = plainToInstance(QueryUserTaskExecutionDto, {
        overallStatus: status
      });

      // Act
      const errors = await validateDTO(dto);

      // Assert
      expect(errors.length).toBe(0);
    }
  });

  it('should validate a valid DTO with different sort fields', async () => {
    // Arrange
    const sortByFields = [
      ExecutionSortBy.CREATED_AT,
      ExecutionSortBy.START_TIME,
      ExecutionSortBy.END_TIME
    ];

    for (const sortBy of sortByFields) {
      const dto = plainToInstance(QueryUserTaskExecutionDto, {
        sortBy: sortBy
      });

      // Act
      const errors = await validateDTO(dto);

      // Assert
      expect(errors.length).toBe(0);
    }
  });
});
