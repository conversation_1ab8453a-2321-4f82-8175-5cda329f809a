const { execSync } = require('child_process');
const path = require('path');

// <PERSON><PERSON><PERSON> nghĩa các màu sắc cho terminal
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',

  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

/**
 * Hàm in log với màu sắc
 * @param {string} message - Nội dung cần in
 */
function log(message) {
  console.log(message);
}

/**
 * Chạy lệnh shell và trả về kết quả
 * @param {string} command - <PERSON><PERSON><PERSON> cần chạy
 * @returns {{success: boolean, output: string}} Kết quả chạy lệnh
 */
function runCommand(command) {
  try {
    const output = execSync(command, { encoding: 'utf8' });
    return { success: true, output };
  } catch (error) {
    return { success: false, output: error.stdout };
  }
}

/**
 * Chạy tất cả các test DTO
 * @returns {boolean} Kết quả chạy test
 */
function runAllDtoTests() {
  log(`\n${colors.bright}${colors.magenta}Chạy tất cả bài test cho các DTO của User Task...${colors.reset}`);

  const command = `npx jest src/modules/task/user/tests/dto --config=src/modules/task/user/tests/jest.config.js`;
  const { success, output } = runCommand(command);

  if (success) {
    log(`${colors.green}✓ Tất cả test thành công${colors.reset}`);
    return true;
  } else {
    log(`${colors.red}✗ Có test thất bại${colors.reset}`);
    log(output);
    return false;
  }
}

/**
 * Chạy tất cả các test DTO với coverage
 * @returns {boolean} Kết quả chạy test
 */
function runAllDtoTestsWithCoverage() {
  log(`\n${colors.bright}${colors.magenta}Chạy tất cả bài test DTO với coverage...${colors.reset}`);

  const command = `npx jest src/modules/task/user/tests/dto --config=src/modules/task/user/tests/jest.config.js --coverage`;
  const { success, output } = runCommand(command);

  if (success) {
    log(`${colors.green}✓ Tất cả test thành công${colors.reset}`);
    log(`${colors.cyan}Báo cáo coverage đã được tạo tại ./coverage/task-user/lcov-report/index.html${colors.reset}`);
    return true;
  } else {
    log(`${colors.red}✗ Có test thất bại${colors.reset}`);
    log(output);
    return false;
  }
}

/**
 * Chạy một file test cụ thể
 * @param {string} filePath - Đường dẫn đến file test
 * @returns {boolean} Kết quả chạy test
 */
function runSpecificTest(filePath) {
  const fileName = path.basename(filePath);
  log(`\n${colors.bright}${colors.blue}Đang chạy test: ${fileName}${colors.reset}`);

  const command = `npx jest ${filePath} --config=src/modules/task/user/tests/jest.config.js`;
  const { success, output } = runCommand(command);

  if (success) {
    log(`${colors.green}✓ Test thành công: ${fileName}${colors.reset}`);
    return true;
  } else {
    log(`${colors.red}✗ Test thất bại: ${fileName}${colors.reset}`);
    log(output);
    return false;
  }
}

// Danh sách các file test DTO
const dtoTestFiles = [
  // Task DTOs
  'create-user-task.dto.spec.ts',
  'update-user-task.dto.spec.ts',
  'user-task-response.dto.spec.ts',
  'query-user-task.dto.spec.ts',

  // Step DTOs
  'create-user-step.dto.spec.ts',
  'update-user-step.dto.spec.ts',
  'user-step-response.dto.spec.ts',
  'query-user-step.dto.spec.ts',

  // Connection DTOs
  'create-user-step-connection.dto.spec.ts',
  'update-user-step-connection.dto.spec.ts',
  'user-step-connection-response.dto.spec.ts',
  'query-user-step-connection.dto.spec.ts',

  // Execution DTOs
  'query-user-task-execution.dto.spec.ts',
  'user-task-execution-response.dto.spec.ts',
];

// Xử lý tham số dòng lệnh
const args = process.argv.slice(2);
if (args.length === 0) {
  // Không có tham số, chạy tất cả các test DTO
  runAllDtoTests();
} else if (args[0] === '--coverage') {
  // Chạy với coverage
  runAllDtoTestsWithCoverage();
} else if (args[0] === '--file' && args[1]) {
  // Chạy một file test cụ thể
  const fileName = args[1];
  const filePath = path.join(__dirname, fileName);
  runSpecificTest(filePath);
} else if (args[0] === '--all-files') {
  // Chạy từng file test một
  log(`\n${colors.bright}${colors.magenta}Chạy từng file test DTO...${colors.reset}`);

  let totalFiles = dtoTestFiles.length;
  let passedFiles = 0;

  for (const fileName of dtoTestFiles) {
    const filePath = path.join(__dirname, fileName);
    const success = runSpecificTest(filePath);
    if (success) passedFiles++;
  }

  // Hiển thị kết quả tổng quan
  log(`\n${colors.bright}${colors.magenta}=== KẾT QUẢ TỔNG QUAN ===${colors.reset}`);
  log(`${colors.bright}Tổng số file test: ${totalFiles}${colors.reset}`);
  log(`${colors.bright}${colors.green}Số file test thành công: ${passedFiles}${colors.reset}`);
  log(`${colors.bright}${colors.red}Số file test thất bại: ${totalFiles - passedFiles}${colors.reset}`);

  // Trả về mã lỗi nếu có test thất bại
  process.exit(passedFiles < totalFiles ? 1 : 0);
} else {
  // Hiển thị hướng dẫn sử dụng
  log(`${colors.bright}${colors.yellow}Cách sử dụng:${colors.reset}`);
  log(`  node run-dto-tests.js                  - Chạy tất cả các test DTO`);
  log(`  node run-dto-tests.js --coverage       - Chạy tất cả các test DTO với coverage`);
  log(`  node run-dto-tests.js --file <tên-file> - Chạy một file test DTO cụ thể`);
  log(`  node run-dto-tests.js --all-files      - Chạy từng file test DTO một`);
}
