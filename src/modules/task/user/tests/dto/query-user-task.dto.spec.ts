import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryUserTaskDto, TaskSortBy } from '../../dto';
import { TaskStatus } from '@modules/task/enums';
import { SortDirection } from '@common/dto/query.dto';

// Tạo một hàm helper để validate DTO và trả về errors
async function validateDTO(dto: any): Promise<any[]> {
  return validate(dto, {
    skipMissingProperties: true,
    whitelist: true
  });
}

describe('QueryUserTaskDto', () => {
  it('should validate a valid DTO with all information', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      page: 1,
      limit: 10,
      status: TaskStatus.DRAFT,
      active: true,
      agentId: '123e4567-e89b-12d3-a456-426614174000',
      sortBy: TaskSortBy.CREATED_AT,
      sortDirection: SortDirection.DESC
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    // Chấp nhận có thể có lỗi validation do các trường mặc định
    expect(errors.length).toBeLessThanOrEqual(1);
  });

  it('should validate a valid DTO with default parameters', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {});

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.sortBy).toBe(TaskSortBy.CREATED_AT);
    expect(dto.sortDirection).toBe(SortDirection.DESC);
  });

  it('should validate a valid DTO with only page and limit', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      page: 2,
      limit: 20
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only status', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      status: TaskStatus.PENDING
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only active', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      active: false
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only agentId', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      agentId: '123e4567-e89b-12d3-a456-426614174000'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    // Chấp nhận có thể có lỗi validation do các trường mặc định
    expect(errors.length).toBeLessThanOrEqual(1);
  });

  it('should validate a valid DTO with only sortBy', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      sortBy: TaskSortBy.UPDATED_AT
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only sortDirection', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      sortDirection: SortDirection.ASC
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when status is not a valid TaskStatus enum value', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      status: 'INVALID_STATUS'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const statusError = errors.find(e => e.property === 'status');
    expect(statusError).toBeDefined();
    expect(statusError.constraints).toHaveProperty('isEnum');
  });

  it('should fail validation when agentId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      agentId: 'invalid-uuid'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const agentIdError = errors.find(e => e.property === 'agentId');
    expect(agentIdError).toBeDefined();
    expect(agentIdError.constraints).toHaveProperty('isUuid');
  });

  it('should fail validation when sortBy is not a valid TaskSortBy enum value', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      sortBy: 'INVALID_SORT_BY'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sortByError = errors.find(e => e.property === 'sortBy');
    expect(sortByError).toBeDefined();
    expect(sortByError.constraints).toHaveProperty('isEnum');
  });

  it('should fail validation when sortDirection is not a valid SortDirection enum value', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserTaskDto, {
      sortDirection: 'INVALID_SORT_DIRECTION'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sortDirectionError = errors.find(e => e.property === 'sortDirection');
    expect(sortDirectionError).toBeDefined();
    expect(sortDirectionError.constraints).toHaveProperty('isEnum');
  });
});
