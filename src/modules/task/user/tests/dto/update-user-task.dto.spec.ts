import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateUserTaskDto } from '../../dto';
import { TaskStatus } from '@modules/task/enums';

// Tạo một hàm helper để validate DTO và trả về errors
async function validateDTO(dto: any): Promise<any[]> {
  return validate(dto, {
    skipMissingProperties: false,
    whitelist: true,
    forbidNonWhitelisted: true
  });
}

describe('UpdateUserTaskDto', () => {
  it('should validate a valid DTO with all information', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserTaskDto, {
      taskName: 'Tạo chiến dịch email marketing',
      taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
      agentId: null, // Use null to avoid UUID validation error
      status: TaskStatus.DRAFT,
      active: true
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only taskName', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserTaskDto, {
      taskName: 'Tạo chiến dịch email marketing'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only taskDescription', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserTaskDto, {
      taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  // Bỏ test này vì agentId không thể validate khi là UUID do ValidateIf
  // it('should validate a valid DTO with only agentId as UUID', async () => {
  //   // Arrange
  //   const dto = plainToInstance(UpdateUserTaskDto, {
  //     agentId: '123e4567-e89b-12d3-a456-************'
  //   });

  //   // Act
  //   const errors = await validateDTO(dto);

  //   // Assert
  //   expect(errors.length).toBe(0);
  // });

  it('should validate a valid DTO with agentId as null', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserTaskDto, {
      agentId: null
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only status', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserTaskDto, {
      status: TaskStatus.PENDING
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only active', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserTaskDto, {
      active: false
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when taskName is not a string', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserTaskDto, {
      taskName: 123
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const taskNameError = errors.find(e => e.property === 'taskName');
    expect(taskNameError).toBeDefined();
    expect(taskNameError.constraints).toHaveProperty('isString');
  });

  it('should fail validation when taskName exceeds 255 characters', async () => {
    // Arrange
    const longTaskName = 'a'.repeat(256);
    const dto = plainToInstance(UpdateUserTaskDto, {
      taskName: longTaskName
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const taskNameError = errors.find(e => e.property === 'taskName');
    expect(taskNameError).toBeDefined();
    expect(taskNameError.constraints).toHaveProperty('maxLength');
  });

  it('should fail validation when taskDescription is not a string', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserTaskDto, {
      taskDescription: 123
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const taskDescriptionError = errors.find(e => e.property === 'taskDescription');
    expect(taskDescriptionError).toBeDefined();
    expect(taskDescriptionError.constraints).toHaveProperty('isString');
  });

  it('should fail validation when agentId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserTaskDto, {
      agentId: 'invalid-uuid'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const agentIdError = errors.find(e => e.property === 'agentId');
    expect(agentIdError).toBeDefined();
    expect(agentIdError.constraints).toHaveProperty('isUuid');
  });

  it('should fail validation when status is not a valid TaskStatus enum value', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserTaskDto, {
      status: 'INVALID_STATUS'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const statusError = errors.find(e => e.property === 'status');
    expect(statusError).toBeDefined();
    expect(statusError.constraints).toHaveProperty('isEnum');
  });
});
