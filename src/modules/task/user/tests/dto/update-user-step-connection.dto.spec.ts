import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateUserStepConnectionDto } from '../../dto';

// Tạo một hàm helper để validate DTO và trả về errors
async function validateDTO(dto: any): Promise<any[]> {
  return validate(dto, { 
    skipMissingProperties: true,
    whitelist: true
  });
}

describe('UpdateUserStepConnectionDto', () => {
  it('should validate a valid DTO with all information', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepConnectionDto, {
      outputField: 'emailContent',
      inputField: 'content'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only outputField', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepConnectionDto, {
      outputField: 'newEmailContent'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with only inputField', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepConnectionDto, {
      inputField: 'newContent'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO with no fields (empty update)', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepConnectionDto, {});

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when outputField is not a string', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepConnectionDto, {
      outputField: 123,
      inputField: 'content'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const outputFieldError = errors.find(e => e.property === 'outputField');
    expect(outputFieldError).toBeDefined();
    expect(outputFieldError.constraints).toHaveProperty('isString');
  });

  it('should fail validation when outputField exceeds 255 characters', async () => {
    // Arrange
    const longOutputField = 'a'.repeat(256);
    const dto = plainToInstance(UpdateUserStepConnectionDto, {
      outputField: longOutputField,
      inputField: 'content'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const outputFieldError = errors.find(e => e.property === 'outputField');
    expect(outputFieldError).toBeDefined();
    expect(outputFieldError.constraints).toHaveProperty('maxLength');
  });

  it('should fail validation when inputField is not a string', async () => {
    // Arrange
    const dto = plainToInstance(UpdateUserStepConnectionDto, {
      outputField: 'emailContent',
      inputField: 123
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const inputFieldError = errors.find(e => e.property === 'inputField');
    expect(inputFieldError).toBeDefined();
    expect(inputFieldError.constraints).toHaveProperty('isString');
  });

  it('should fail validation when inputField exceeds 255 characters', async () => {
    // Arrange
    const longInputField = 'a'.repeat(256);
    const dto = plainToInstance(UpdateUserStepConnectionDto, {
      outputField: 'emailContent',
      inputField: longInputField
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const inputFieldError = errors.find(e => e.property === 'inputField');
    expect(inputFieldError).toBeDefined();
    expect(inputFieldError.constraints).toHaveProperty('maxLength');
  });
});
