import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryUserStepConnectionDto } from '../../dto/connection/query-user-step-connection.dto';

// Tạo một hàm helper để validate DTO và trả về errors
async function validateDTO(dto: any): Promise<any[]> {
  return validate(dto, {
    skipMissingProperties: true,
    whitelist: true
  });
}

describe('QueryUserStepConnectionDto', () => {
  it('should validate a valid DTO with all information', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      fromStepId: '123e4567-e89b-12d3-a456-426614174001',
      toStepId: '123e4567-e89b-12d3-a456-************'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeLessThanOrEqual(3);
  });

  it('should validate a valid DTO with only taskId', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepConnectionDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeLessThanOrEqual(1);
  });

  it('should validate a valid DTO with only fromStepId', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepConnectionDto, {
      fromStepId: '123e4567-e89b-12d3-a456-426614174001'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeLessThanOrEqual(1);
  });

  it('should validate a valid DTO with only toStepId', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepConnectionDto, {
      toStepId: '123e4567-e89b-12d3-a456-************'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeLessThanOrEqual(1);
  });

  it('should validate a valid DTO with no fields (query all)', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepConnectionDto, {});

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when taskId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepConnectionDto, {
      taskId: 'invalid-uuid'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const taskIdError = errors.find(e => e.property === 'taskId');
    expect(taskIdError).toBeDefined();
    expect(taskIdError.constraints).toHaveProperty('isUuid');
  });

  it('should fail validation when fromStepId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepConnectionDto, {
      fromStepId: 'invalid-uuid'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const fromStepIdError = errors.find(e => e.property === 'fromStepId');
    expect(fromStepIdError).toBeDefined();
    expect(fromStepIdError.constraints).toHaveProperty('isUuid');
  });

  it('should fail validation when toStepId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserStepConnectionDto, {
      toStepId: 'invalid-uuid'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const toStepIdError = errors.find(e => e.property === 'toStepId');
    expect(toStepIdError).toBeDefined();
    expect(toStepIdError.constraints).toHaveProperty('isUuid');
  });
});
