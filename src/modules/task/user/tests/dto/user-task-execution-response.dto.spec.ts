import { plainToInstance } from 'class-transformer';
import { UserTaskExecutionResponseDto } from '../../dto/execution/user-task-execution-response.dto';
import { TaskExecutionStatus } from '@modules/task/enums';

describe('UserTaskExecutionResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO với đầy đủ thông tin', () => {
    // Arrange
    const plainObject = {
      taskExecutionId: '123e4567-e89b-12d3-a456-426614174000',
      taskId: '123e4567-e89b-12d3-a456-426614174001',
      startTime: 1625097600000,
      endTime: 1625097660000,
      overallStatus: TaskExecutionStatus.SUCCESS,
      executionDetails: {
        steps: [
          {
            stepId: '123e4567-e89b-12d3-a456-426614174002',
            status: 'SUCCESS',
            output: { content: 'Nội dung email đã được tạo' },
            startTime: 1625097600000,
            endTime: 1625097660000,
          },
        ],
      },
      createdAt: 1625097600000,
      extraField: 'Trường này không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(UserTaskExecutionResponseDto, plainObject, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserTaskExecutionResponseDto);
    expect(dto.taskExecutionId).toBe('123e4567-e89b-12d3-a456-426614174000');
    expect(dto.taskId).toBe('123e4567-e89b-12d3-a456-426614174001');
    expect(dto.startTime).toBe(1625097600000);
    expect(dto.endTime).toBe(1625097660000);
    expect(dto.overallStatus).toBe(TaskExecutionStatus.SUCCESS);
    // Kiểm tra executionDetails tồn tại nhưng không kiểm tra nội dung chi tiết
    expect(dto.executionDetails).toBeDefined();
    expect(dto.createdAt).toBe(1625097600000);
    // Trường không được khai báo trong DTO không nên được chuyển đổi
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO khi endTime là null', () => {
    // Arrange
    const plainObject = {
      taskExecutionId: '123e4567-e89b-12d3-a456-426614174000',
      taskId: '123e4567-e89b-12d3-a456-426614174001',
      startTime: 1625097600000,
      endTime: null,
      overallStatus: TaskExecutionStatus.RUNNING,
      executionDetails: {
        steps: [
          {
            stepId: '123e4567-e89b-12d3-a456-426614174002',
            status: 'RUNNING',
            output: null,
            startTime: 1625097600000,
            endTime: null,
          },
        ],
      },
      createdAt: 1625097600000
    };

    // Act
    const dto = plainToInstance(UserTaskExecutionResponseDto, plainObject, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserTaskExecutionResponseDto);
    expect(dto.endTime).toBeNull();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO khi executionDetails là null', () => {
    // Arrange
    const plainObject = {
      taskExecutionId: '123e4567-e89b-12d3-a456-426614174000',
      taskId: '123e4567-e89b-12d3-a456-426614174001',
      startTime: 1625097600000,
      endTime: 1625097660000,
      overallStatus: TaskExecutionStatus.SUCCESS,
      executionDetails: null,
      createdAt: 1625097600000
    };

    // Act
    const dto = plainToInstance(UserTaskExecutionResponseDto, plainObject, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Assert
    expect(dto).toBeInstanceOf(UserTaskExecutionResponseDto);
    expect(dto.executionDetails).toBeNull();
  });

  it('nên chuyển đổi đúng các trạng thái thực thi khác nhau', () => {
    // Arrange
    const statuses = [
      TaskExecutionStatus.RUNNING,
      TaskExecutionStatus.SUCCESS,
      TaskExecutionStatus.FAILED,
      TaskExecutionStatus.CANCELLED,
      TaskExecutionStatus.PAUSED
    ];

    for (const status of statuses) {
      const plainObject = {
        taskExecutionId: '123e4567-e89b-12d3-a456-426614174000',
        taskId: '123e4567-e89b-12d3-a456-426614174001',
        startTime: 1625097600000,
        endTime: status === TaskExecutionStatus.RUNNING ? null : 1625097660000,
        overallStatus: status,
        executionDetails: {
          steps: [
            {
              stepId: '123e4567-e89b-12d3-a456-426614174002',
              status: status,
              output: status === TaskExecutionStatus.SUCCESS ? { content: 'Nội dung' } : null,
              startTime: 1625097600000,
              endTime: status === TaskExecutionStatus.RUNNING ? null : 1625097660000,
            },
          ],
        },
        createdAt: 1625097600000
      };

      // Act
      const dto = plainToInstance(UserTaskExecutionResponseDto, plainObject, {
        excludeExtraneousValues: true,
        enableImplicitConversion: true
      });

      // Assert
      expect(dto).toBeInstanceOf(UserTaskExecutionResponseDto);
      expect(dto.overallStatus).toBe(status);
    }
  });
});
