import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateUserStepDto } from '../../dto';

// Mock StepType enum
enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image',
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

describe('CreateUserStepDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin cho prompt step', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Nhập nội dung email',
      stepDescription: '<PERSON><PERSON>ớc này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true,
        defaultValue: ''
      }
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin cho agent step', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Tạo nội dung email bằng AI',
      stepDescription: 'Bước này sử dụng AI để tạo nội dung email marketing',
      stepType: StepType.AGENT,
      stepConfig: {
        stepType: 'agent',
        agentId: '123e4567-e89b-12d3-a456-426614174001',
        prompt: 'Tạo nội dung email marketing cho sản phẩm mới',
        parameters: {
          productName: 'Sản phẩm A',
          targetAudience: 'Khách hàng doanh nghiệp'
        }
      }
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin cho google_sheet step', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Lấy dữ liệu từ Google Sheet',
      stepDescription: 'Bước này lấy dữ liệu từ Google Sheet',
      stepType: StepType.GOOGLE_SHEET,
      stepConfig: {
        stepType: 'google_sheet',
        sheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
        range: 'A1:E10'
      },
      googleUserAuthId: '123e4567-e89b-12d3-a456-426614174002'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin cho facebook_page step', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Đăng bài lên Facebook Page',
      stepDescription: 'Bước này đăng bài lên Facebook Page',
      stepType: StepType.FACEBOOK_PAGE,
      stepConfig: {
        stepType: 'facebook_page',
        content: 'Nội dung bài đăng',
        imageUrl: 'https://example.com/image.jpg'
      },
      facebookPageId: '123e4567-e89b-12d3-a456-426614174003'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ khi không có stepDescription', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Nhập nội dung email',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true
      }
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải báo lỗi khi taskId có giá trị rỗng', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '', // Giá trị rỗng
      stepName: 'Nhập nội dung email',
      stepDescription: 'Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true
      }
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi stepName bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepDescription: 'Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true
      }
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi stepName không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 123,
      stepDescription: 'Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true
      }
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('phải báo lỗi khi stepName vượt quá 255 ký tự', async () => {
    // Arrange
    const longStepName = 'a'.repeat(256);
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: longStepName,
      stepDescription: 'Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true
      }
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });

  it('phải báo lỗi khi stepDescription không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Nhập nội dung email',
      stepDescription: 123,
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true
      }
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('phải báo lỗi khi stepType bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Nhập nội dung email',
      stepDescription: 'Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true
      }
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi stepType không phải là giá trị hợp lệ từ enum StepType', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Nhập nội dung email',
      stepDescription: 'Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepType: 'invalid_step_type',
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung email marketing cho sản phẩm mới',
        inputType: 'text',
        required: true
      }
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('phải báo lỗi khi stepConfig không phải là đối tượng', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Nhập nội dung email',
      stepDescription: 'Bước này yêu cầu người dùng nhập nội dung email để gửi cho khách hàng',
      stepType: StepType.PROMPT,
      stepConfig: 'invalid_config' // Không phải là đối tượng
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isObject');
  });



  it('phải báo lỗi khi googleUserAuthId không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Lấy dữ liệu từ Google Sheet',
      stepDescription: 'Bước này lấy dữ liệu từ Google Sheet',
      stepType: StepType.GOOGLE_SHEET,
      stepConfig: {
        stepType: 'google_sheet',
        sheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
        range: 'A1:E10'
      },
      googleUserAuthId: 123 // Không phải là chuỗi
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('phải báo lỗi khi facebookPageId không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserStepDto, {
      taskId: '123e4567-e89b-12d3-a456-426614174000',
      stepName: 'Đăng bài lên Facebook Page',
      stepDescription: 'Bước này đăng bài lên Facebook Page',
      stepType: StepType.FACEBOOK_PAGE,
      stepConfig: {
        stepType: 'facebook_page',
        content: 'Nội dung bài đăng',
        imageUrl: 'https://example.com/image.jpg'
      },
      facebookPageId: 123 // Không phải là chuỗi
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });
});
