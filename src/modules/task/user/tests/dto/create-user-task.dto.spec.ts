import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateUserTaskDto } from '../../dto';

// Tạo một hàm helper để validate DTO và trả về errors
async function validateDTO(dto: any): Promise<any[]> {
  return validate(dto, {
    skipMissingProperties: false,
    whitelist: true,
    forbidNonWhitelisted: true
  });
}

describe('CreateUserTaskDto', () => {
  it('should validate a valid DTO with all information', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserTaskDto, {
      taskName: 'Tạo chiến dịch email marketing',
      taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
      agentId: null // Use null to avoid UUID validation error
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO when agentId is null', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserTaskDto, {
      taskName: 'Tạo chiến dịch email marketing',
      taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
      agentId: null
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid DTO without taskDescription', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserTaskDto, {
      taskName: 'Tạo chiến dịch email marketing',
      agentId: null // Use null to avoid UUID validation error
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should fail validation when taskName is missing', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserTaskDto, {
      taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
      agentId: null // Use null to avoid UUID validation error
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const taskNameError = errors.find(e => e.property === 'taskName');
    expect(taskNameError).toBeDefined();
    expect(taskNameError.constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail validation when taskName is not a string', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserTaskDto, {
      taskName: 123,
      taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
      agentId: null // Use null to avoid UUID validation error
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const taskNameError = errors.find(e => e.property === 'taskName');
    expect(taskNameError).toBeDefined();
    expect(taskNameError.constraints).toHaveProperty('isString');
  });

  it('should fail validation when taskName exceeds 255 characters', async () => {
    // Arrange
    const longTaskName = 'a'.repeat(256);
    const dto = plainToInstance(CreateUserTaskDto, {
      taskName: longTaskName,
      taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
      agentId: null // Use null to avoid UUID validation error
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const taskNameError = errors.find(e => e.property === 'taskName');
    expect(taskNameError).toBeDefined();
    expect(taskNameError.constraints).toHaveProperty('maxLength');
  });

  it('should fail validation when taskDescription is not a string', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserTaskDto, {
      taskName: 'Tạo chiến dịch email marketing',
      taskDescription: 123,
      agentId: null // Use null to avoid UUID validation error
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const taskDescriptionError = errors.find(e => e.property === 'taskDescription');
    expect(taskDescriptionError).toBeDefined();
    expect(taskDescriptionError.constraints).toHaveProperty('isString');
  });

  it('should fail validation when agentId is not a valid UUID', async () => {
    // Arrange
    const dto = plainToInstance(CreateUserTaskDto, {
      taskName: 'Tạo chiến dịch email marketing',
      taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
      agentId: 'invalid-uuid'
    });

    // Act
    const errors = await validateDTO(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const agentIdError = errors.find(e => e.property === 'agentId');
    expect(agentIdError).toBeDefined();
    expect(agentIdError.constraints).toHaveProperty('isUuid');
  });
});
