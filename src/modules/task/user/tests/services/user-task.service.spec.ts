import { Test, TestingModule } from '@nestjs/testing';
import { plainToInstance } from 'class-transformer';

// Mock TaskStatus enum
enum TaskStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

// Mock classes
class MockUserTaskService {
  constructor(
    private readonly userTaskRepository: any,
    private readonly validationHelper: any,
  ) {}

  async create(userId: number, createDto: any) {
    return {};
  }

  async update(taskId: string, userId: number, updateDto: any) {
    return {};
  }

  async findById(taskId: string, userId: number) {
    return {};
  }

  async findAll(userId: number, queryDto: any) {
    return { items: [], meta: {} };
  }

  async delete(taskId: string, userId: number) {
    return true;
  }

  async submitForApproval(taskId: string, userId: number) {
    return;
  }
}

class MockUserTaskRepository {
  async createTask(data: any) { return {}; }
  async updateTask(taskId: string, userId: number, data: any) { return {}; }
  async findById(taskId: string, userId: number) { return {}; }
  async findAll(userId: number, queryDto: any) { return { items: [], meta: {} }; }
  async softDeleteTask(taskId: string, userId: number) { return true; }
}

class MockValidationHelper {
  validateTaskName(taskName: string) {}
  validateTaskExists(task: any) {}
  validateTaskNotDeleted(task: any) {}
  validateTaskOwnership(task: any, userId: number) {}
  validateTaskStatusForUpdate(task: any) {}
  validateTaskActive(task: any) {}
  validateTaskFullCheck(task: any, userId: number) {}
}

describe('UserTaskService', () => {
  let service: UserTaskService;
  let repository: UserTaskRepository;
  let validationHelper: ValidationHelper;

  // Mock user task
  const mockUserTask: UserTask = {
    taskId: '123e4567-e89b-12d3-a456-426614174000',
    userId: 1,
    taskName: 'Nhiệm vụ test',
    taskDescription: 'Mô tả nhiệm vụ test',
    status: TaskStatus.DRAFT,
    active: true,
    agentId: null,
    createdAt: new Date(1625097600000),
    updatedAt: new Date(1625097600000),
    deletedAt: null
  };

  // Mock task response
  const mockTaskResponse: UserTaskResponseDto = {
    taskId: '123e4567-e89b-12d3-a456-426614174000',
    taskName: 'Nhiệm vụ test',
    taskDescription: 'Mô tả nhiệm vụ test',
    status: TaskStatus.DRAFT,
    active: true,
    agentId: null,
    createdAt: 1625097600000
  };

  // Mock paginated result
  const mockPaginatedResult: PaginatedResult<UserTask> = {
    items: [mockUserTask],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1
    }
  };

  // Mock repository
  const mockUserTaskRepository = {
    createTask: jest.fn(),
    updateTask: jest.fn(),
    findById: jest.fn(),
    findAll: jest.fn(),
    softDeleteTask: jest.fn()
  };

  // Mock validation helper
  const mockValidationHelper = {
    validateTaskName: jest.fn(),
    validateTaskExists: jest.fn(),
    validateTaskNotDeleted: jest.fn(),
    validateTaskOwnership: jest.fn(),
    validateTaskStatusForUpdate: jest.fn(),
    validateTaskActive: jest.fn(),
    validateTaskFullCheck: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserTaskService,
        {
          provide: UserTaskRepository,
          useValue: mockUserTaskRepository
        },
        {
          provide: ValidationHelper,
          useValue: mockValidationHelper
        }
      ]
    }).compile();

    service = module.get<UserTaskService>(UserTaskService);
    repository = module.get<UserTaskRepository>(UserTaskRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new task successfully', async () => {
      // Arrange
      const userId = 1;
      const createDto: CreateUserTaskDto = {
        taskName: 'Nhiệm vụ test',
        taskDescription: 'Mô tả nhiệm vụ test',
        agentId: null
      };
      mockUserTaskRepository.createTask.mockResolvedValue(mockUserTask);
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue(mockTaskResponse);

      // Act
      const result = await service.create(userId, createDto);

      // Assert
      expect(validationHelper.validateTaskName).toHaveBeenCalledWith(createDto.taskName);
      expect(repository.createTask).toHaveBeenCalledWith({
        userId,
        agentId: createDto.agentId,
        taskName: createDto.taskName,
        taskDescription: createDto.taskDescription
      });
      expect(result).toEqual(mockTaskResponse);
    });

    it('should handle validation errors', async () => {
      // Arrange
      const userId = 1;
      const createDto: CreateUserTaskDto = {
        taskName: '',
        taskDescription: 'Mô tả nhiệm vụ test',
        agentId: null
      };
      const error = new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_NAME_REQUIRED,
        'Tên nhiệm vụ là bắt buộc'
      );
      mockValidationHelper.validateTaskName.mockImplementation(() => {
        throw error;
      });

      // Act & Assert
      await expect(service.create(userId, createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateTaskName).toHaveBeenCalledWith(createDto.taskName);
      expect(repository.createTask).not.toHaveBeenCalled();
    });
  });

  describe('update', () => {
    it('should update a task successfully', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const userId = 1;
      const updateDto: UpdateUserTaskDto = {
        taskName: 'Nhiệm vụ đã cập nhật',
        taskDescription: 'Mô tả nhiệm vụ đã cập nhật'
      };
      const updatedTask = { ...mockUserTask, ...updateDto };
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserTaskRepository.updateTask.mockResolvedValue(updatedTask);
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue({
        ...mockTaskResponse,
        ...updateDto
      });

      // Act
      const result = await service.update(taskId, userId, updateDto);

      // Assert
      expect(repository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(validationHelper.validateTaskFullCheck).toHaveBeenCalledWith(mockUserTask, userId);
      expect(validationHelper.validateTaskStatusForUpdate).toHaveBeenCalledWith(mockUserTask);
      if (updateDto.taskName) {
        expect(validationHelper.validateTaskName).toHaveBeenCalledWith(updateDto.taskName);
      }
      expect(repository.updateTask).toHaveBeenCalledWith(taskId, updateDto);
      expect(result).toEqual({
        ...mockTaskResponse,
        ...updateDto
      });
    });
  });

  describe('findById', () => {
    it('should return a task by id', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const userId = 1;
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue(mockTaskResponse);

      // Act
      const result = await service.findById(taskId, userId);

      // Assert
      expect(repository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(validationHelper.validateTaskFullCheck).toHaveBeenCalledWith(mockUserTask, userId);
      expect(result).toEqual(mockTaskResponse);
    });

    it('should handle not found errors', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const userId = 1;
      mockUserTaskRepository.findById.mockResolvedValue(null);
      const error = new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_NOT_FOUND,
        'Không tìm thấy nhiệm vụ'
      );
      mockValidationHelper.validateTaskFullCheck.mockImplementation(() => {
        throw error;
      });

      // Act & Assert
      await expect(service.findById(taskId, userId)).rejects.toThrow(AppException);
      expect(repository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(validationHelper.validateTaskFullCheck).toHaveBeenCalledWith(null, userId);
    });
  });

  describe('findAll', () => {
    it('should return a paginated list of tasks', async () => {
      // Arrange
      const userId = 1;
      const queryDto: QueryUserTaskDto = {
        page: 1,
        limit: 10
      };
      mockUserTaskRepository.findAll.mockResolvedValue(mockPaginatedResult);
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue(mockTaskResponse);

      // Act
      const result = await service.findAll(userId, queryDto);

      // Assert
      expect(repository.findAll).toHaveBeenCalledWith(userId, queryDto);
      expect(result.items).toHaveLength(1);
      expect(result.items[0]).toEqual(mockTaskResponse);
      expect(result.meta).toEqual(mockPaginatedResult.meta);
    });
  });

  describe('delete', () => {
    it('should delete a task successfully', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174000';
      const userId = 1;
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserTaskRepository.softDeleteTask.mockResolvedValue(true);

      // Act
      const result = await service.delete(taskId, userId);

      // Assert
      expect(repository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(validationHelper.validateTaskFullCheck).toHaveBeenCalledWith(mockUserTask, userId);
      expect(repository.softDeleteTask).toHaveBeenCalledWith(taskId, userId);
      expect(result).toBe(true);
    });
  });
});
