import { Test, TestingModule } from '@nestjs/testing';
import { plainToInstance } from 'class-transformer';

// Mock TaskStatus enum
enum TaskStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

// Mock StepType enum
enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image',
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

// Mock classes
class MockUserConnectionService {
  constructor(
    private readonly userStepConnectionRepository: any,
    private readonly userStepRepository: any,
    private readonly userTaskRepository: any,
    private readonly validationHelper: any,
  ) {}

  async create(userId: number, createDto: any) {
    return {};
  }

  async update(connectionId: string, taskId: string, userId: number, updateDto: any) {
    return {};
  }

  async findById(connectionId: string, taskId: string, userId: number) {
    return {};
  }

  async findAll(taskId: string, userId: number, queryDto: any) {
    return { items: [], meta: {} };
  }

  async delete(connectionId: string, taskId: string, userId: number) {
    return true;
  }
}

class MockUserStepConnectionRepository {
  async createConnection(data: any) { return {}; }
  async updateConnection(connectionId: string, taskId: string, data: any) { return {}; }
  async findById(connectionId: string) { return {}; }
  async findAll(taskId: string, queryDto: any) { return { items: [], meta: {} }; }
  async deleteConnection(connectionId: string) { return true; }
  async isConnectionExists(taskId: string, fromStepId: string, toStepId: string) { return false; }
  async findAllByTaskId(taskId: string) { return []; }
  async findConnectionsByTaskId(taskId: string) { return []; }
}

class MockUserStepRepository {
  async findById(stepId: string) { return {}; }
}

class MockUserTaskRepository {
  async findById(taskId: string, userId: number) { return {}; }
}

class MockValidationHelper {
  validateTaskExists(task: any) {}
  validateTaskNotDeleted(task: any) {}
  validateTaskOwnership(task: any, userId: number) {}
  validateTaskStatusForUpdate(task: any) {}
  validateTaskFullCheck(task: any, userId: number) {}
  validateStepExists(step: any) {}
  validateStepBelongsToTask(step: any, taskId: string) {}
  validateStepsSameTask(fromStep: any, toStep: any) {}
  validateConnectionExists(connection: any) {}
  validateConnectionBelongsToTask(connection: any, taskId: string) {}
  validateConnectionFullCheck(connection: any, taskId: string) {}
  validateConnectionNotDuplicate(exists: boolean) {}
  validateNoCircularReference(connections: any[], fromStepId: string, toStepId: string) {}
  validateConnectionSteps(fromStepId: string, toStepId: string) {}
}

describe('UserConnectionService', () => {
  let service: UserConnectionService;
  let connectionRepository: UserStepConnectionRepository;
  let stepRepository: UserStepRepository;
  let taskRepository: UserTaskRepository;
  let validationHelper: ValidationHelper;

  // Mock user task
  const mockUserTask: UserTask = {
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    userId: 1,
    taskName: 'Nhiệm vụ test',
    taskDescription: 'Mô tả nhiệm vụ test',
    status: TaskStatus.DRAFT,
    active: true,
    agentId: null,
    createdAt: new Date(1625097600000),
    updatedAt: new Date(1625097600000),
    deletedAt: null,
  };

  // Mock user steps
  const mockFromStep: UserStep = {
    stepId: '123e4567-e89b-12d3-a456-426614174002',
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    orderIndex: 1,
    stepName: 'Bước nguồn',
    stepDescription: 'Mô tả bước nguồn',
    stepType: StepType.PROMPT,
    stepConfig: {
      stepType: 'prompt',
      promptText: 'Vui lòng nhập nội dung',
      inputType: 'text',
      required: true,
    },
    googleUserAuthId: null,
    facebookPageId: null,
    createdAt: new Date(1625097600000),
    updatedAt: new Date(1625097600000),
  };

  const mockToStep: UserStep = {
    stepId: '123e4567-e89b-12d3-a456-426614174003',
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    orderIndex: 2,
    stepName: 'Bước đích',
    stepDescription: 'Mô tả bước đích',
    stepType: StepType.EMAIL,
    stepConfig: {
      stepType: 'email',
      subject: 'Tiêu đề email',
      content: 'Nội dung email',
    },
    googleUserAuthId: null,
    facebookPageId: null,
    createdAt: new Date(1625097600000),
    updatedAt: new Date(1625097600000),
  };

  // Mock user step connection
  const mockUserStepConnection: UserStepConnection = {
    connectionId: '123e4567-e89b-12d3-a456-426614174000',
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    fromStepId: '123e4567-e89b-12d3-a456-426614174002',
    toStepId: '123e4567-e89b-12d3-a456-426614174003',
    outputField: 'emailContent',
    inputField: 'content',
    createdAt: new Date(1625097600000),
    updatedAt: new Date(1625097600000),
  };

  // Mock connection response
  const mockConnectionResponse: UserStepConnectionResponseDto = {
    connectionId: '123e4567-e89b-12d3-a456-426614174000',
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    fromStepId: '123e4567-e89b-12d3-a456-426614174002',
    toStepId: '123e4567-e89b-12d3-a456-426614174003',
    outputField: 'emailContent',
    inputField: 'content',
    createdAt: 1625097600000,
  };

  // Mock paginated result
  const mockPaginatedResult: PaginatedResult<UserStepConnection> = {
    items: [mockUserStepConnection],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  // Mock repositories
  const mockUserStepConnectionRepository = {
    createConnection: jest.fn(),
    updateConnection: jest.fn(),
    findById: jest.fn(),
    findAll: jest.fn(),
    softDeleteConnection: jest.fn(),
    checkConnectionExists: jest.fn(),
    findAllByTaskId: jest.fn(),
    findConnectionsByTaskId: jest.fn(),
  };

  const mockUserStepRepository = {
    findById: jest.fn(),
  };

  const mockUserTaskRepository = {
    findById: jest.fn(),
  };

  // Mock validation helper
  const mockValidationHelper = {
    validateTaskExists: jest.fn(),
    validateTaskNotDeleted: jest.fn(),
    validateTaskOwnership: jest.fn(),
    validateTaskStatusForUpdate: jest.fn(),
    validateTaskFullCheck: jest.fn(),
    validateStepExists: jest.fn(),
    validateStepBelongsToTask: jest.fn(),
    validateStepsSameTask: jest.fn(),
    validateConnectionExists: jest.fn(),
    validateConnectionBelongsToTask: jest.fn(),
    validateConnectionFullCheck: jest.fn(),
    validateConnectionNotDuplicate: jest.fn(),
    validateNoCircularReference: jest.fn(),
    validateConnectionSteps: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserConnectionService,
        {
          provide: UserStepConnectionRepository,
          useValue: mockUserStepConnectionRepository,
        },
        {
          provide: UserStepRepository,
          useValue: mockUserStepRepository,
        },
        {
          provide: UserTaskRepository,
          useValue: mockUserTaskRepository,
        },
        {
          provide: ValidationHelper,
          useValue: mockValidationHelper,
        },
      ],
    }).compile();

    service = module.get<UserConnectionService>(UserConnectionService);
    connectionRepository = module.get<UserStepConnectionRepository>(
      UserStepConnectionRepository,
    );
    stepRepository = module.get<UserStepRepository>(UserStepRepository);
    taskRepository = module.get<UserTaskRepository>(UserTaskRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new connection successfully', async () => {
      // Arrange
      const userId = 1;
      const createDto: CreateUserStepConnectionDto = {
        taskId: '123e4567-e89b-12d3-a456-426614174001',
        fromStepId: '123e4567-e89b-12d3-a456-426614174002',
        toStepId: '123e4567-e89b-12d3-a456-426614174003',
        outputField: 'emailContent',
        inputField: 'content',
      };
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepRepository.findById.mockImplementation((stepId) => {
        if (stepId === createDto.fromStepId)
          return Promise.resolve(mockFromStep);
        if (stepId === createDto.toStepId) return Promise.resolve(mockToStep);
        return Promise.resolve(null);
      });
      mockUserStepConnectionRepository.checkConnectionExists.mockResolvedValue(
        false,
      );
      mockUserStepConnectionRepository.findAllByTaskId.mockResolvedValue([]);
      mockUserStepConnectionRepository.createConnection.mockResolvedValue(
        mockUserStepConnection,
      );

      // Act
      const result = await service.create(userId, createDto);

      // Assert
      expect(taskRepository.findById).toHaveBeenCalledWith(
        createDto.taskId,
        userId,
      );
      expect(validationHelper.validateTaskFullCheck).toHaveBeenCalledWith(
        mockUserTask,
        userId,
      );
      expect(validationHelper.validateTaskStatusForUpdate).toHaveBeenCalledWith(
        mockUserTask,
      );
      expect(validationHelper.validateConnectionSteps).toHaveBeenCalledWith(
        createDto.fromStepId,
        createDto.toStepId,
      );
      expect(stepRepository.findById).toHaveBeenCalledWith(
        createDto.fromStepId,
      );
      expect(stepRepository.findById).toHaveBeenCalledWith(createDto.toStepId);
      expect(validationHelper.validateStepExists).toHaveBeenCalledWith(
        mockFromStep,
      );
      expect(validationHelper.validateStepExists).toHaveBeenCalledWith(
        mockToStep,
      );
      expect(validationHelper.validateStepBelongsToTask).toHaveBeenCalledWith(
        mockFromStep,
        createDto.taskId,
      );
      expect(validationHelper.validateStepBelongsToTask).toHaveBeenCalledWith(
        mockToStep,
        createDto.taskId,
      );
      expect(validationHelper.validateStepsSameTask).toHaveBeenCalledWith(
        mockFromStep,
        mockToStep,
      );
      expect(connectionRepository.checkConnectionExists).toHaveBeenCalledWith(
        createDto.taskId,
        createDto.fromStepId,
        createDto.toStepId,
      );
      expect(
        validationHelper.validateConnectionNotDuplicate,
      ).toHaveBeenCalledWith(false);
      expect(connectionRepository.findAllByTaskId).toHaveBeenCalledWith(
        createDto.taskId,
      );
      expect(validationHelper.validateNoCircularReference).toHaveBeenCalledWith(
        [],
        createDto.fromStepId,
        createDto.toStepId,
      );
      expect(connectionRepository.createConnection).toHaveBeenCalledWith({
        taskId: createDto.taskId,
        fromStepId: createDto.fromStepId,
        toStepId: createDto.toStepId,
        outputField: createDto.outputField,
        inputField: createDto.inputField,
      });
      expect(result).toEqual(expect.any(Object));
    });

    it('should throw an exception when task validation fails', async () => {
      // Arrange
      const userId = 1;
      const createDto: CreateUserStepConnectionDto = {
        taskId: '123e4567-e89b-12d3-a456-426614174001',
        fromStepId: '123e4567-e89b-12d3-a456-426614174002',
        toStepId: '123e4567-e89b-12d3-a456-426614174003',
        outputField: 'emailContent',
        inputField: 'content',
      };
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      validationHelper.validateTaskFullCheck.mockImplementation(() => {
        throw new AppException(
          CONNECTION_ERROR_CODES.CONNECTION_TASK_NOT_FOUND,
          'Không tìm thấy nhiệm vụ',
        );
      });

      // Act & Assert
      await expect(service.create(userId, createDto)).rejects.toThrow(AppException);
      expect(taskRepository.findById).toHaveBeenCalledWith(createDto.taskId, userId);
      expect(validationHelper.validateTaskFullCheck).toHaveBeenCalledWith(mockUserTask, userId);
    });
  });

  describe('update', () => {
    it('should update a connection successfully', async () => {
      // Arrange
      const connectionId = '123e4567-e89b-12d3-a456-426614174000';
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = 1;
      const updateDto: UpdateUserStepConnectionDto = {
        outputField: 'newEmailContent',
        inputField: 'newContent',
      };
      const updatedConnection = { ...mockUserStepConnection, ...updateDto };
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepConnectionRepository.findById.mockResolvedValue(
        mockUserStepConnection,
      );
      mockUserStepConnectionRepository.updateConnection.mockResolvedValue(
        updatedConnection,
      );
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue({
        ...mockConnectionResponse,
        ...updateDto,
      });

      // Act
      const result = await service.update(
        connectionId,
        taskId,
        userId,
        updateDto,
      );

      // Assert
      expect(taskRepository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(validationHelper.validateTaskFullCheck).toHaveBeenCalledWith(
        mockUserTask,
        userId,
      );
      expect(validationHelper.validateTaskStatusForUpdate).toHaveBeenCalledWith(
        mockUserTask,
      );
      expect(connectionRepository.findById).toHaveBeenCalledWith(connectionId);
      expect(validationHelper.validateConnectionFullCheck).toHaveBeenCalledWith(
        mockUserStepConnection,
        taskId,
      );
      expect(connectionRepository.updateConnection).toHaveBeenCalledWith(
        connectionId,
        taskId,
        updateDto,
      );
      expect(result).toEqual(expect.any(Object));
    });

    it('should throw an exception when connection validation fails', async () => {
      // Arrange
      const connectionId = '123e4567-e89b-12d3-a456-426614174000';
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = 1;
      const updateDto: UpdateUserStepConnectionDto = {
        outputField: 'newEmailContent',
        inputField: 'newContent',
      };
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepConnectionRepository.findById.mockResolvedValue(mockUserStepConnection);
      validationHelper.validateConnectionFullCheck.mockImplementation(() => {
        throw new AppException(
          CONNECTION_ERROR_CODES.CONNECTION_NOT_FOUND,
          'Không tìm thấy kết nối'
        );
      });

      // Act & Assert
      await expect(service.update(connectionId, taskId, userId, updateDto)).rejects.toThrow(AppException);
      expect(taskRepository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(validationHelper.validateTaskFullCheck).toHaveBeenCalledWith(mockUserTask, userId);
      expect(connectionRepository.findById).toHaveBeenCalledWith(connectionId);
      expect(validationHelper.validateConnectionFullCheck).toHaveBeenCalledWith(mockUserStepConnection, taskId);
    });
  });

  describe('findById', () => {
    it('should return a connection by id', async () => {
      // Arrange
      const connectionId = '123e4567-e89b-12d3-a456-426614174000';
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = 1;
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepConnectionRepository.findById.mockResolvedValue(
        mockUserStepConnection,
      );
      jest
        .spyOn(plainToInstance as any, 'call')
        .mockReturnValue(mockConnectionResponse);

      // Act
      const result = await service.findById(connectionId, taskId, userId);

      // Assert
      expect(taskRepository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(validationHelper.validateTaskExists).toHaveBeenCalledWith(
        mockUserTask,
      );
      expect(validationHelper.validateTaskNotDeleted).toHaveBeenCalledWith(
        mockUserTask,
      );
      expect(connectionRepository.findById).toHaveBeenCalledWith(connectionId);
      expect(validationHelper.validateConnectionFullCheck).toHaveBeenCalledWith(
        mockUserStepConnection,
        taskId,
      );
      expect(result).toEqual(mockConnectionResponse);
    });
  });

  describe('findAll', () => {
    it('should return a paginated list of connections', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = 1;
      const queryDto: QueryUserStepConnectionDto = {
        page: 1,
        limit: 10,
      };
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepConnectionRepository.findAll.mockResolvedValue(
        mockPaginatedResult,
      );
      jest
        .spyOn(plainToInstance as any, 'call')
        .mockReturnValue(mockConnectionResponse);

      // Act
      const result = await service.findAll(taskId, userId, queryDto);

      // Assert
      expect(taskRepository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(validationHelper.validateTaskExists).toHaveBeenCalledWith(
        mockUserTask,
      );
      expect(validationHelper.validateTaskNotDeleted).toHaveBeenCalledWith(
        mockUserTask,
      );
      expect(connectionRepository.findAll).toHaveBeenCalledWith(
        taskId,
        queryDto,
      );
      expect(result.items).toHaveLength(1);
      expect(result.items[0]).toEqual(mockConnectionResponse);
      expect(result.meta).toEqual(mockPaginatedResult.meta);
    });
  });

  describe('delete', () => {
    it('should delete a connection successfully', async () => {
      // Arrange
      const connectionId = '123e4567-e89b-12d3-a456-426614174000';
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = 1;
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepConnectionRepository.findById.mockResolvedValue(
        mockUserStepConnection,
      );
      mockUserStepConnectionRepository.softDeleteConnection.mockResolvedValue(
        true,
      );

      // Act
      const result = await service.delete(connectionId, taskId, userId);

      // Assert
      expect(taskRepository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(validationHelper.validateTaskExists).toHaveBeenCalledWith(
        mockUserTask,
      );
      expect(validationHelper.validateTaskNotDeleted).toHaveBeenCalledWith(
        mockUserTask,
      );
      expect(validationHelper.validateTaskStatusForUpdate).toHaveBeenCalledWith(
        mockUserTask,
      );
      expect(connectionRepository.findById).toHaveBeenCalledWith(connectionId);
      expect(validationHelper.validateConnectionFullCheck).toHaveBeenCalledWith(
        mockUserStepConnection,
        taskId,
      );
      expect(connectionRepository.softDeleteConnection).toHaveBeenCalledWith(
        connectionId,
      );
      expect(result).toBe(true);
    });
  });
});
