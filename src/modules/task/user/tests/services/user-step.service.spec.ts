import { Test, TestingModule } from '@nestjs/testing';
import { UserStepService } from '../../services/user-step.service';
import { UserStepRepository, UserTaskRepository } from '@modules/task/repositories';
import { ValidationHelper } from '../../helpers/validation.helper';
import { CreateUserStepDto, QueryUserStepDto, UpdateUserStepDto, UserStepResponseDto } from '../../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions/app.exception';
import { USER_STEP_ERROR_CODES } from '@modules/task/user/exceptions';
import { UserStep, UserTask } from '@modules/task/entities';
import { TaskStatus } from '@modules/task/enums';
import { plainToInstance } from 'class-transformer';

// Mock StepType enum trực tiếp trong file test
enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image',
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

describe('UserStepService', () => {
  let service: UserStepService;
  let stepRepository: UserStepRepository;
  let taskRepository: UserTaskRepository;
  let validationHelper: ValidationHelper;

  // Mock user task
  const mockUserTask: UserTask = {
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    userId: 1,
    taskName: 'Nhiệm vụ test',
    taskDescription: 'Mô tả nhiệm vụ test',
    status: TaskStatus.DRAFT,
    active: true,
    agentId: null,
    createdAt: new Date(1625097600000),
    updatedAt: new Date(1625097600000),
    deletedAt: null
  };

  // Mock user step
  const mockUserStep: UserStep = {
    stepId: '123e4567-e89b-12d3-a456-426614174000',
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    orderIndex: 1,
    stepName: 'Bước test',
    stepDescription: 'Mô tả bước test',
    stepType: StepType.PROMPT,
    stepConfig: {
      stepType: 'prompt',
      promptText: 'Vui lòng nhập nội dung',
      inputType: 'text',
      required: true
    },
    googleUserAuthId: null,
    facebookPageId: null,
    createdAt: new Date(1625097600000),
    updatedAt: new Date(1625097600000)
  };

  // Mock step response
  const mockStepResponse: UserStepResponseDto = {
    stepId: '123e4567-e89b-12d3-a456-426614174000',
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    orderIndex: 1,
    stepName: 'Bước test',
    stepDescription: 'Mô tả bước test',
    stepType: StepType.PROMPT,
    stepConfig: {
      stepType: 'prompt',
      promptText: 'Vui lòng nhập nội dung',
      inputType: 'text',
      required: true
    },
    googleUserAuthId: null,
    facebookPageId: null,
    createdAt: 1625097600000
  };

  // Mock paginated result
  const mockPaginatedResult: PaginatedResult<UserStep> = {
    items: [mockUserStep],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1
    }
  };

  // Mock repositories
  const mockUserStepRepository = {
    createStep: jest.fn(),
    updateStep: jest.fn(),
    findById: jest.fn(),
    findAll: jest.fn(),
    softDeleteStep: jest.fn(),
    countByTaskId: jest.fn(),
    getMaxOrderIndex: jest.fn(),
    isOrderIndexExists: jest.fn()
  };

  const mockUserTaskRepository = {
    findById: jest.fn()
  };

  // Mock validation helper
  const mockValidationHelper = {
    validateTaskExists: jest.fn(),
    validateTaskNotDeleted: jest.fn(),
    validateTaskOwnership: jest.fn(),
    validateTaskStatusForUpdate: jest.fn(),
    validateTaskFullCheck: jest.fn(),
    validateStepName: jest.fn(),
    validateStepType: jest.fn(),
    validateStepConfig: jest.fn(),
    validateStepLimit: jest.fn(),
    validateStepExists: jest.fn(),
    validateStepBelongsToTask: jest.fn(),
    validateStepOrderIndex: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserStepService,
        {
          provide: UserStepRepository,
          useValue: mockUserStepRepository
        },
        {
          provide: UserTaskRepository,
          useValue: mockUserTaskRepository
        },
        {
          provide: ValidationHelper,
          useValue: mockValidationHelper
        }
      ]
    }).compile();

    service = module.get<UserStepService>(UserStepService);
    stepRepository = module.get<UserStepRepository>(UserStepRepository);
    taskRepository = module.get<UserTaskRepository>(UserTaskRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new step successfully', async () => {
      // Arrange
      const userId = 1;
      const createDto: CreateUserStepDto = {
        taskId: '123e4567-e89b-12d3-a456-426614174001',
        stepName: 'Bước test',
        stepDescription: 'Mô tả bước test',
        stepType: StepType.PROMPT,
        stepConfig: {
          stepType: 'prompt',
          promptText: 'Vui lòng nhập nội dung',
          inputType: 'text',
          required: true
        }
      };
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepRepository.countByTaskId.mockResolvedValue(0);
      mockUserStepRepository.getMaxOrderIndex.mockResolvedValue(0);
      mockUserStepRepository.createStep.mockResolvedValue(mockUserStep);
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue(mockStepResponse);

      // Act
      const result = await service.create(userId, createDto);

      // Assert
      expect(mockUserTaskRepository.findById).toHaveBeenCalledWith(createDto.taskId, userId);
      expect(mockValidationHelper.validateTaskFullCheck).toHaveBeenCalledWith(mockUserTask, userId);
      expect(mockValidationHelper.validateTaskStatusForUpdate).toHaveBeenCalledWith(mockUserTask);
      expect(mockValidationHelper.validateStepName).toHaveBeenCalledWith(createDto.stepName);
      expect(mockValidationHelper.validateStepType).toHaveBeenCalledWith(createDto.stepType);
      expect(mockValidationHelper.validateStepConfig).toHaveBeenCalledWith(createDto.stepType, createDto.stepConfig);
      expect(mockUserStepRepository.countByTaskId).toHaveBeenCalledWith(createDto.taskId);
      expect(mockValidationHelper.validateStepLimit).toHaveBeenCalledWith(0);
      expect(mockUserStepRepository.createStep).toHaveBeenCalled();
      expect(result).toEqual(mockStepResponse);
    });
  });

  describe('update', () => {
    it('should update a step successfully', async () => {
      // Arrange
      const stepId = '123e4567-e89b-12d3-a456-426614174000';
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = 1;
      const updateDto: UpdateUserStepDto = {
        stepName: 'Bước đã cập nhật',
        stepDescription: 'Mô tả bước đã cập nhật'
      };
      const updatedStep = { ...mockUserStep, ...updateDto };
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepRepository.findById.mockResolvedValue(mockUserStep);
      mockUserStepRepository.updateStep.mockResolvedValue(updatedStep);
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue({
        ...mockStepResponse,
        ...updateDto
      });

      // Act
      const result = await service.update(stepId, taskId, userId, updateDto);

      // Assert
      expect(mockUserTaskRepository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(mockValidationHelper.validateTaskFullCheck).toHaveBeenCalledWith(mockUserTask, userId);
      expect(mockValidationHelper.validateTaskStatusForUpdate).toHaveBeenCalledWith(mockUserTask);
      expect(mockUserStepRepository.findById).toHaveBeenCalledWith(stepId, taskId);
      expect(mockValidationHelper.validateStepFullCheck).toHaveBeenCalledWith(mockUserStep, taskId);
      if (updateDto.stepName) {
        expect(mockValidationHelper.validateStepName).toHaveBeenCalledWith(updateDto.stepName);
      }
      if (updateDto.stepType) {
        expect(mockValidationHelper.validateStepType).toHaveBeenCalledWith(updateDto.stepType);
      }
      if (updateDto.stepConfig) {
        expect(mockValidationHelper.validateStepConfig).toHaveBeenCalledWith(
          updateDto.stepType || mockUserStep.stepType,
          updateDto.stepConfig
        );
      }
      expect(mockUserStepRepository.updateStep).toHaveBeenCalledWith(stepId, taskId, expect.any(Object));
      expect(result).toEqual({
        ...mockStepResponse,
        ...updateDto
      });
    });
  });

  describe('findById', () => {
    it('should return a step by id', async () => {
      // Arrange
      const stepId = '123e4567-e89b-12d3-a456-426614174000';
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = 1;
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepRepository.findById.mockResolvedValue(mockUserStep);
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue(mockStepResponse);

      // Act
      const result = await service.findById(stepId, taskId, userId);

      // Assert
      expect(mockUserTaskRepository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(mockValidationHelper.validateTaskExists).toHaveBeenCalledWith(mockUserTask);
      expect(mockValidationHelper.validateTaskNotDeleted).toHaveBeenCalledWith(mockUserTask);
      expect(mockUserStepRepository.findById).toHaveBeenCalledWith(stepId, taskId);
      expect(mockValidationHelper.validateStepExists).toHaveBeenCalledWith(mockUserStep);
      expect(result).toEqual(mockStepResponse);
    });
  });

  describe('findAll', () => {
    it('should return a paginated list of steps', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = 1;
      const queryDto: QueryUserStepDto = {
        page: 1,
        limit: 10
      };
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepRepository.findAll.mockResolvedValue(mockPaginatedResult);
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue(mockStepResponse);

      // Act
      const result = await service.findAll(taskId, userId, queryDto);

      // Assert
      expect(mockUserTaskRepository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(mockValidationHelper.validateTaskExists).toHaveBeenCalledWith(mockUserTask);
      expect(mockValidationHelper.validateTaskNotDeleted).toHaveBeenCalledWith(mockUserTask);
      expect(mockUserStepRepository.findAll).toHaveBeenCalledWith(taskId, queryDto);
      expect(result.items).toHaveLength(1);
      expect(result.items[0]).toEqual(mockStepResponse);
      expect(result.meta).toEqual(mockPaginatedResult.meta);
    });
  });

  describe('delete', () => {
    it('should delete a step successfully', async () => {
      // Arrange
      const stepId = '123e4567-e89b-12d3-a456-426614174000';
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = 1;
      mockUserTaskRepository.findById.mockResolvedValue(mockUserTask);
      mockUserStepRepository.findById.mockResolvedValue(mockUserStep);
      mockUserStepRepository.softDeleteStep.mockResolvedValue(true);

      // Act
      const result = await service.delete(stepId, taskId, userId);

      // Assert
      expect(mockUserTaskRepository.findById).toHaveBeenCalledWith(taskId, userId);
      expect(mockValidationHelper.validateTaskFullCheck).toHaveBeenCalledWith(mockUserTask, userId);
      expect(mockValidationHelper.validateTaskStatusForUpdate).toHaveBeenCalledWith(mockUserTask);
      expect(mockUserStepRepository.findById).toHaveBeenCalledWith(stepId, taskId);
      expect(mockValidationHelper.validateStepFullCheck).toHaveBeenCalledWith(mockUserStep, taskId);
      expect(mockUserStepRepository.softDeleteStep).toHaveBeenCalledWith(stepId);
      expect(result).toBe(true);
    });
  });
});
