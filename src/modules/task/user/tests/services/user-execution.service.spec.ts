import { Test, TestingModule } from '@nestjs/testing';
import { UserExecutionService } from '../../services/user-execution.service';
import { UserTaskExecutionRepository } from '@modules/task/repositories';
import { QueryUserTaskExecutionDto, UserTaskExecutionResponseDto } from '../../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions/app.exception';
import { USER_EXECUTION_ERROR_CODES } from '@modules/task/user/exceptions';
import { UserTaskExecution } from '@modules/task/entities';
import { TaskExecutionStatus } from '@modules/task/enums';
import { plainToInstance } from 'class-transformer';

describe('UserExecutionService', () => {
  let service: UserExecutionService;
  let repository: UserTaskExecutionRepository;

  // Mock user task execution
  const mockUserTaskExecution: UserTaskExecution = {
    taskExecutionId: '123e4567-e89b-12d3-a456-426614174000',
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    startTime: new Date(1625097600000),
    endTime: new Date(1625097660000),
    overallStatus: TaskExecutionStatus.SUCCESS,
    executionDetails: {
      steps: [
        {
          stepId: '123e4567-e89b-12d3-a456-426614174002',
          status: 'SUCCESS',
          output: { content: 'Nội dung đã được tạo' },
          startTime: 1625097600000,
          endTime: 1625097660000,
        },
      ],
    },
    createdAt: new Date(1625097600000),
    updatedAt: new Date(1625097600000)
  };

  // Mock execution response
  const mockExecutionResponse: UserTaskExecutionResponseDto = {
    taskExecutionId: '123e4567-e89b-12d3-a456-426614174000',
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    startTime: 1625097600000,
    endTime: 1625097660000,
    overallStatus: TaskExecutionStatus.SUCCESS,
    executionDetails: {
      steps: [
        {
          stepId: '123e4567-e89b-12d3-a456-426614174002',
          status: 'SUCCESS',
          output: { content: 'Nội dung đã được tạo' },
          startTime: 1625097600000,
          endTime: 1625097660000,
        },
      ],
    },
    createdAt: 1625097600000
  };

  // Mock paginated result
  const mockPaginatedResult: PaginatedResult<UserTaskExecution> = {
    items: [mockUserTaskExecution],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1
    }
  };

  // Mock repository
  const mockUserTaskExecutionRepository = {
    findById: jest.fn(),
    findAll: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserExecutionService,
        {
          provide: UserTaskExecutionRepository,
          useValue: mockUserTaskExecutionRepository
        }
      ]
    }).compile();

    service = module.get<UserExecutionService>(UserExecutionService);
    repository = module.get<UserTaskExecutionRepository>(UserTaskExecutionRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findById', () => {
    it('should return an execution by id', async () => {
      // Arrange
      const executionId = '123e4567-e89b-12d3-a456-426614174000';
      mockUserTaskExecutionRepository.findById.mockResolvedValue(mockUserTaskExecution);
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue(mockExecutionResponse);

      // Act
      const result = await service.findById(executionId);

      // Assert
      expect(repository.findById).toHaveBeenCalledWith(executionId);
      expect(result).toEqual(mockExecutionResponse);
    });

    it('should throw an exception when execution is not found', async () => {
      // Arrange
      const executionId = '123e4567-e89b-12d3-a456-426614174000';
      mockUserTaskExecutionRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findById(executionId)).rejects.toThrow(
        new AppException(
          USER_EXECUTION_ERROR_CODES.USER_EXECUTION_NOT_FOUND,
          'Không tìm thấy phiên thực thi'
        )
      );
      expect(repository.findById).toHaveBeenCalledWith(executionId);
    });

    it('should handle repository errors', async () => {
      // Arrange
      const executionId = '123e4567-e89b-12d3-a456-426614174000';
      const error = new Error('Database error');
      mockUserTaskExecutionRepository.findById.mockRejectedValue(error);

      // Act & Assert
      await expect(service.findById(executionId)).rejects.toThrow(
        new AppException(
          USER_EXECUTION_ERROR_CODES.USER_EXECUTION_FETCH_FAILED,
          'Lỗi khi lấy thông tin phiên thực thi'
        )
      );
      expect(repository.findById).toHaveBeenCalledWith(executionId);
    });
  });

  describe('findAll', () => {
    it('should return a paginated list of executions', async () => {
      // Arrange
      const queryDto: QueryUserTaskExecutionDto = {
        page: 1,
        limit: 10
      };
      mockUserTaskExecutionRepository.findAll.mockResolvedValue(mockPaginatedResult);
      jest.spyOn(plainToInstance as any, 'call').mockReturnValue(mockExecutionResponse);

      // Act
      const result = await service.findAll(queryDto);

      // Assert
      expect(repository.findAll).toHaveBeenCalledWith(queryDto);
      expect(result.items).toHaveLength(1);
      expect(result.items[0]).toEqual(mockExecutionResponse);
      expect(result.meta).toEqual(mockPaginatedResult.meta);
    });

    it('should handle repository errors', async () => {
      // Arrange
      const queryDto: QueryUserTaskExecutionDto = {
        page: 1,
        limit: 10
      };
      const error = new Error('Database error');
      mockUserTaskExecutionRepository.findAll.mockRejectedValue(error);

      // Act & Assert
      await expect(service.findAll(queryDto)).rejects.toThrow(
        new AppException(
          USER_EXECUTION_ERROR_CODES.USER_EXECUTION_FETCH_FAILED,
          'Lỗi khi lấy danh sách phiên thực thi'
        )
      );
      expect(repository.findAll).toHaveBeenCalledWith(queryDto);
    });
  });
});
