const { execSync } = require('child_process');
const path = require('path');

// Increase memory limit for Node.js
const nodeOptions = '--max-old-space-size=5120';

// Run the test
try {
  console.log('Running test for user-task.controller.spec.ts...');
  execSync(`node ${nodeOptions} ./node_modules/jest/bin/jest.js src/modules/task/user/tests/controllers/user-task.controller.spec.ts --config=src/modules/task/user/tests/controllers/jest.config.js --no-cache --detectOpenHandles --testTimeout=30000`,
    { stdio: 'inherit' }
  );
  console.log('Test completed successfully!');
} catch (error) {
  console.error('Test failed with error:', error.message);
  process.exit(1);
}
