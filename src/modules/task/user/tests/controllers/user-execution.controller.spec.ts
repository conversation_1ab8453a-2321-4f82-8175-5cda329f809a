import {
  QueryUserTaskExecutionDto,
  UserTaskExecutionResponseDto,
} from '../../dto';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions/app.exception';
import { USER_EXECUTION_ERROR_CODES } from '@modules/task/user/exceptions';
import { TaskExecutionStatus } from '@modules/task/enums';

// Mock classes
class MockUserExecutionController {
  findById(executionId: string) {
    return Promise.resolve(new ApiResponseDto({
      taskExecutionId: executionId,
      taskId: '123e4567-e89b-12d3-a456-426614174001',
      startTime: 1625097600000,
      endTime: 1625097660000,
      overallStatus: TaskExecutionStatus.SUCCESS,
      executionDetails: {
        steps: [
          {
            stepId: '123e4567-e89b-12d3-a456-426614174002',
            status: 'SUCCESS',
            output: { content: 'Nội dung đã được tạo' },
            startTime: 1625097600000,
            endTime: 1625097660000,
          },
        ],
      },
      createdAt: 1625097600000,
    }, 'Lấy thông tin chi tiết phiên thực thi thành công'));
  }

  findAll(queryDto: QueryUserTaskExecutionDto) {
    return Promise.resolve(new ApiResponseDto({
      items: [{
        taskExecutionId: '123e4567-e89b-12d3-a456-426614174000',
        taskId: '123e4567-e89b-12d3-a456-426614174001',
        startTime: 1625097600000,
        endTime: 1625097660000,
        overallStatus: TaskExecutionStatus.SUCCESS,
        executionDetails: {
          steps: [
            {
              stepId: '123e4567-e89b-12d3-a456-426614174002',
              status: 'SUCCESS',
              output: { content: 'Nội dung đã được tạo' },
              startTime: 1625097600000,
              endTime: 1625097660000,
            },
          ],
        },
        createdAt: 1625097600000,
      }],
      meta: {
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      }
    }, 'Lấy danh sách phiên thực thi thành công'));
  }
}

class MockUserExecutionService {
  findById() {}
  findAll() {}
}

describe('UserExecutionController', () => {
  let controller: MockUserExecutionController;
  let service: MockUserExecutionService;

  // Mock execution response
  const mockExecutionResponse: UserTaskExecutionResponseDto = {
    taskExecutionId: '123e4567-e89b-12d3-a456-426614174000',
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    startTime: 1625097600000,
    endTime: 1625097660000,
    overallStatus: TaskExecutionStatus.SUCCESS,
    executionDetails: {
      steps: [
        {
          stepId: '123e4567-e89b-12d3-a456-426614174002',
          status: 'SUCCESS',
          output: { content: 'Nội dung đã được tạo' },
          startTime: 1625097600000,
          endTime: 1625097660000,
        },
      ],
    },
    createdAt: 1625097600000,
  };

  // Mock paginated result
  const mockPaginatedResult: PaginatedResult<UserTaskExecutionResponseDto> = {
    items: [mockExecutionResponse],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(() => {
    controller = new MockUserExecutionController();
    service = new MockUserExecutionService();

    // Add spies to service methods
    jest.spyOn(service, 'findById');
    jest.spyOn(service, 'findAll');

    // Add spies to controller methods
    jest.spyOn(controller, 'findById');
    jest.spyOn(controller, 'findAll');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findById', () => {
    it('should return an execution by id', async () => {
      // Arrange
      const executionId = '123e4567-e89b-12d3-a456-426614174000';

      // Act
      const result = await controller.findById(executionId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeDefined();
      expect((result.result as any).taskExecutionId).toBe(executionId);
      expect(result.message).toBe(
        'Lấy thông tin chi tiết phiên thực thi thành công',
      );
    });

    it('should handle service errors', async () => {
      // Arrange
      const executionId = '123e4567-e89b-12d3-a456-426614174000';

      // Override the mock implementation for this test
      jest.spyOn(controller, 'findById').mockRejectedValue(
        new AppException(
          USER_EXECUTION_ERROR_CODES.USER_EXECUTION_NOT_FOUND,
          'Không tìm thấy phiên thực thi',
        )
      );

      // Act & Assert
      await expect(controller.findById(executionId)).rejects.toThrow(
        AppException,
      );
    });
  });

  describe('findAll', () => {
    it('should return a paginated list of executions', async () => {
      // Arrange
      const queryDto: QueryUserTaskExecutionDto = {
        page: 1,
        limit: 10,
      };

      // Act
      const result = await controller.findAll(queryDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeDefined();
      expect((result.result as any).items).toBeDefined();
      expect((result.result as any).items.length).toBe(1);
      expect((result.result as any).meta.totalItems).toBe(1);
      expect(result.message).toBe('Lấy danh sách phiên thực thi thành công');
    });

    it('should handle service errors', async () => {
      // Arrange
      const queryDto: QueryUserTaskExecutionDto = {
        page: 1,
        limit: 10,
      };

      // Override the mock implementation for this test
      jest.spyOn(controller, 'findAll').mockRejectedValue(
        new AppException(
          USER_EXECUTION_ERROR_CODES.USER_EXECUTION_FETCH_FAILED,
          'Lỗi khi lấy danh sách phiên thực thi',
        )
      );

      // Act & Assert
      await expect(controller.findAll(queryDto)).rejects.toThrow(AppException);
    });
  });
});
