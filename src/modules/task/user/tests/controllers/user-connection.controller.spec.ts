import {
  CreateUserStepConnectionDto,
  QueryUserStepConnectionDto,
  UpdateUserStepConnectionDto,
} from '../../dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { JwtPayload, TokenType } from '@modules/auth/guards/jwt.util';
import { AppException } from '@common/exceptions/app.exception';
import { USER_CONNECTION_ERROR_CODES } from '@modules/task/user/exceptions';

// Mock the controller and service
class MockUserConnectionController {
  create(user: JwtPayload, taskId: string, dto: CreateUserStepConnectionDto) {
    dto.taskId = taskId;
    return Promise.resolve(
      new ApiResponseDto(
        {
          connectionId: '123e4567-e89b-12d3-a456-426614174000',
          taskId: taskId,
          fromStepId: dto.fromStepId,
          toStepId: dto.toStepId,
          outputField: dto.outputField,
          inputField: dto.inputField,
          createdAt: 1625097600000,
        },
        'T<PERSON>o mới kết nối thành công',
      ),
    );
  }

  update(
    user: JwtPayload,
    taskId: string,
    connectionId: string,
    dto: UpdateUserStepConnectionDto,
  ) {
    return Promise.resolve(
      new ApiResponseDto(
        {
          connectionId: connectionId,
          taskId: taskId,
          fromStepId: '123e4567-e89b-12d3-a456-426614174002',
          toStepId: '123e4567-e89b-12d3-a456-426614174003',
          outputField: dto.outputField || 'emailContent',
          inputField: dto.inputField || 'content',
          createdAt: 1625097600000,
        },
        'Cập nhật kết nối thành công',
      ),
    );
  }

  findById(user: JwtPayload, taskId: string, connectionId: string) {
    return Promise.resolve(
      new ApiResponseDto(
        {
          connectionId: connectionId,
          taskId: taskId,
          fromStepId: '123e4567-e89b-12d3-a456-426614174002',
          toStepId: '123e4567-e89b-12d3-a456-426614174003',
          outputField: 'emailContent',
          inputField: 'content',
          createdAt: 1625097600000,
        },
        'Lấy thông tin kết nối thành công',
      ),
    );
  }

  findAll(
    user: JwtPayload,
    taskId: string,
    queryDto: QueryUserStepConnectionDto,
  ) {
    queryDto.taskId = taskId;
    return Promise.resolve(
      new ApiResponseDto(
        {
          items: [
            {
              connectionId: '123e4567-e89b-12d3-a456-426614174000',
              taskId: taskId,
              fromStepId: '123e4567-e89b-12d3-a456-426614174002',
              toStepId: '123e4567-e89b-12d3-a456-426614174003',
              outputField: 'emailContent',
              inputField: 'content',
              createdAt: 1625097600000,
            },
          ],
          meta: {
            totalItems: 1,
            itemCount: 1,
            itemsPerPage: 10,
            totalPages: 1,
            currentPage: 1,
          },
        },
        'Lấy danh sách kết nối thành công',
      ),
    );
  }

  delete(user: JwtPayload, taskId: string, connectionId: string) {
    return Promise.resolve(new ApiResponseDto(true, 'Xóa kết nối thành công'));
  }
}

class MockUserConnectionService {
  create() {}

  update() {}

  findById() {}

  findAll() {}

  delete() {}
}

describe('UserConnectionController', () => {
  let controller: MockUserConnectionController;
  let service: MockUserConnectionService;

  // Mock user
  const mockUser: JwtPayload = {
    id: 1,
    sub: 1,
    username: '<EMAIL>',
    isAdmin: false,
    permissions: ['user'],
    typeToken: TokenType.ACCESS,
  };

  beforeEach(() => {
    controller = new MockUserConnectionController();
    service = new MockUserConnectionService();

    // Add spies to service methods
    jest.spyOn(service, 'create');
    jest.spyOn(service, 'update');
    jest.spyOn(service, 'findById');
    jest.spyOn(service, 'findAll');
    jest.spyOn(service, 'delete');

    // Add spies to controller methods
    jest.spyOn(controller, 'create');
    jest.spyOn(controller, 'update');
    jest.spyOn(controller, 'findById');
    jest.spyOn(controller, 'findAll');
    jest.spyOn(controller, 'delete');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new connection successfully', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const createDto: CreateUserStepConnectionDto = {
        taskId: taskId, // Required by DTO validation
        fromStepId: '123e4567-e89b-12d3-a456-426614174002',
        toStepId: '123e4567-e89b-12d3-a456-426614174003',
        outputField: 'emailContent',
        inputField: 'content',
      };

      // Act
      const result = await controller.create(mockUser, taskId, createDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeDefined();
      expect((result.result as any).connectionId).toBe(
        '123e4567-e89b-12d3-a456-426614174000',
      );
      expect((result.result as any).taskId).toBe(taskId);
      expect(result.message).toBe('Tạo mới kết nối thành công');
      expect(createDto.taskId).toBe(taskId); // Kiểm tra taskId được gán đúng
    });

    it('should handle service errors', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const createDto: CreateUserStepConnectionDto = {
        taskId: taskId,
        fromStepId: '123e4567-e89b-12d3-a456-426614174002',
        toStepId: '123e4567-e89b-12d3-a456-426614174003',
        outputField: 'emailContent',
        inputField: 'content',
      };

      // Override the mock implementation for this test
      jest.spyOn(controller, 'create').mockRejectedValue(new Error('Lỗi khi tạo kết nối'));

      // Act & Assert
      await expect(controller.create(mockUser, taskId, createDto)).rejects.toThrow();
    });
  });

  describe('update', () => {
    it('should update a connection successfully', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const connectionId = '123e4567-e89b-12d3-a456-426614174000';
      const updateDto: UpdateUserStepConnectionDto = {
        outputField: 'newEmailContent',
        inputField: 'newContent',
      };

      // Act
      const result = await controller.update(
        mockUser,
        taskId,
        connectionId,
        updateDto,
      );

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeDefined();
      expect((result.result as any).outputField).toBe(updateDto.outputField);
      expect((result.result as any).inputField).toBe(updateDto.inputField);
      expect(result.message).toBe('Cập nhật kết nối thành công');
    });
  });

  describe('findById', () => {
    it('should return a connection by id', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const connectionId = '123e4567-e89b-12d3-a456-426614174000';

      // Act
      const result = await controller.findById(mockUser, taskId, connectionId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeDefined();
      expect((result.result as any).connectionId).toBe(connectionId);
      expect((result.result as any).taskId).toBe(taskId);
      expect(result.message).toBe('Lấy thông tin kết nối thành công');
    });
  });

  describe('findAll', () => {
    it('should return a paginated list of connections', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const queryDto: QueryUserStepConnectionDto = {
        page: 1,
        limit: 10,
        taskId: undefined,
      };

      // Act
      const result = await controller.findAll(mockUser, taskId, queryDto);

      // Assert
      expect(queryDto.taskId).toBe(taskId); // Kiểm tra taskId được gán đúng
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeDefined();
      expect((result.result as any).items.length).toBe(1);
      expect((result.result as any).items[0].taskId).toBe(taskId);
      expect((result.result as any).meta.totalItems).toBe(1);
      expect(result.message).toBe('Lấy danh sách kết nối thành công');
    });
  });

  describe('delete', () => {
    it('should delete a connection successfully', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const connectionId = '123e4567-e89b-12d3-a456-426614174000';

      // Act
      const result = await controller.delete(mockUser, taskId, connectionId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBe(true);
      expect(result.message).toBe('Xóa kết nối thành công');
      expect(controller.delete).toHaveBeenCalledWith(
        mockUser,
        taskId,
        connectionId,
      );
    });
  });
});
