import { CreateUserStepDto, QueryUserStepDto, UpdateUserStepDto, UserStepResponseDto } from '../../dto';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { JwtPayload, TokenType } from '@modules/auth/guards/jwt.util';
import { AppException } from '@common/exceptions/app.exception';
import { USER_STEP_ERROR_CODES } from '@modules/task/user/exceptions';

// Mock StepType enum trực tiếp trong file test
enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image',
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

// Mock classes
class MockUserStepController {
  create(user: JwtPayload, taskId: string, dto: CreateUserStepDto) {
    dto.taskId = taskId;
    return Promise.resolve(new ApiResponseDto({
      stepId: '123e4567-e89b-12d3-a456-426614174000',
      taskId: taskId,
      orderIndex: 1,
      stepName: 'Bước test',
      stepDescription: 'Mô tả bước test',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung',
        inputType: 'text',
        required: true
      },
      googleUserAuthId: null,
      facebookPageId: null,
      createdAt: 1625097600000
    }, 'Tạo mới bước thành công'));
  }

  update(user: JwtPayload, taskId: string, stepId: string, dto: UpdateUserStepDto) {
    return Promise.resolve(new ApiResponseDto({
      stepId: stepId,
      taskId: taskId,
      orderIndex: 1,
      stepName: dto.stepName || 'Bước test',
      stepDescription: dto.stepDescription || 'Mô tả bước test',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung',
        inputType: 'text',
        required: true
      },
      googleUserAuthId: null,
      facebookPageId: null,
      createdAt: 1625097600000
    }, 'Cập nhật bước thành công'));
  }

  findById(user: JwtPayload, taskId: string, stepId: string) {
    return Promise.resolve(new ApiResponseDto({
      stepId: stepId,
      taskId: taskId,
      orderIndex: 1,
      stepName: 'Bước test',
      stepDescription: 'Mô tả bước test',
      stepType: StepType.PROMPT,
      stepConfig: {
        stepType: 'prompt',
        promptText: 'Vui lòng nhập nội dung',
        inputType: 'text',
        required: true
      },
      googleUserAuthId: null,
      facebookPageId: null,
      createdAt: 1625097600000
    }, 'Lấy thông tin bước thành công'));
  }

  findAll(user: JwtPayload, taskId: string, queryDto: QueryUserStepDto) {
    queryDto.taskId = taskId;
    return Promise.resolve(new ApiResponseDto({
      items: [{
        stepId: '123e4567-e89b-12d3-a456-426614174000',
        taskId: taskId,
        orderIndex: 1,
        stepName: 'Bước test',
        stepDescription: 'Mô tả bước test',
        stepType: StepType.PROMPT,
        stepConfig: {
          stepType: 'prompt',
          promptText: 'Vui lòng nhập nội dung',
          inputType: 'text',
          required: true
        },
        googleUserAuthId: null,
        facebookPageId: null,
        createdAt: 1625097600000
      }],
      meta: {
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1
      }
    }, 'Lấy danh sách bước thành công'));
  }

  delete(user: JwtPayload, taskId: string, stepId: string) {
    return Promise.resolve(new ApiResponseDto(true, 'Xóa bước thành công'));
  }
}

class MockUserStepService {
  create() {}
  update() {}
  findById() {}
  findAll() {}
  delete() {}
}

describe('UserStepController', () => {
  let controller: MockUserStepController;
  let service: MockUserStepService;

  // Mock user
  const mockUser: JwtPayload = {
    id: 1,
    sub: 1,
    username: '<EMAIL>',
    isAdmin: false,
    permissions: ['user'],
    typeToken: TokenType.ACCESS
  };

  // Mock step response
  const mockStepResponse: UserStepResponseDto = {
    stepId: '123e4567-e89b-12d3-a456-426614174000',
    taskId: '123e4567-e89b-12d3-a456-426614174001',
    orderIndex: 1,
    stepName: 'Bước test',
    stepDescription: 'Mô tả bước test',
    stepType: StepType.PROMPT,
    stepConfig: {
      stepType: 'prompt',
      promptText: 'Vui lòng nhập nội dung',
      inputType: 'text',
      required: true
    },
    googleUserAuthId: null,
    facebookPageId: null,
    createdAt: 1625097600000
  };

  // Mock paginated result
  const mockPaginatedResult: PaginatedResult<UserStepResponseDto> = {
    items: [mockStepResponse],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1
    }
  };

  // Mock service
  const mockUserStepService = {
    create: jest.fn(),
    update: jest.fn(),
    findById: jest.fn(),
    findAll: jest.fn(),
    delete: jest.fn()
  };

  beforeEach(() => {
    controller = new MockUserStepController();
    service = new MockUserStepService();

    // Add spies to service methods
    jest.spyOn(service, 'create');
    jest.spyOn(service, 'update');
    jest.spyOn(service, 'findById');
    jest.spyOn(service, 'findAll');
    jest.spyOn(service, 'delete');

    // Add spies to controller methods
    jest.spyOn(controller, 'create');
    jest.spyOn(controller, 'update');
    jest.spyOn(controller, 'findById');
    jest.spyOn(controller, 'findAll');
    jest.spyOn(controller, 'delete');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new step successfully', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const createDto: CreateUserStepDto = {
        taskId: taskId,
        stepName: 'Bước test',
        stepDescription: 'Mô tả bước test',
        stepType: StepType.PROMPT,
        stepConfig: {
          stepType: 'prompt',
          promptText: 'Vui lòng nhập nội dung',
          inputType: 'text',
          required: true
        }
      };
      mockUserStepService.create.mockResolvedValue(mockStepResponse);

      // Act
      const result = await controller.create(mockUser, taskId, createDto);

      // Assert
      expect(createDto.taskId).toBe(taskId); // Kiểm tra taskId được gán đúng
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeDefined();
      expect((result.result as any).stepId).toBe('123e4567-e89b-12d3-a456-426614174000');
      expect((result.result as any).taskId).toBe(taskId);
      expect(result.message).toBe('Tạo mới bước thành công');
    });

    it('should handle service errors', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const createDto: CreateUserStepDto = {
        taskId: taskId,
        stepName: 'Bước test',
        stepDescription: 'Mô tả bước test',
        stepType: StepType.PROMPT,
        stepConfig: {
          stepType: 'prompt',
          promptText: 'Vui lòng nhập nội dung',
          inputType: 'text',
          required: true
        }
      };

      // Override the mock implementation for this test
      jest.spyOn(controller, 'create').mockRejectedValue(new Error('Lỗi khi tạo bước'));

      // Act & Assert
      await expect(controller.create(mockUser, taskId, createDto)).rejects.toThrow('Lỗi khi tạo bước');
    });
  });

  describe('update', () => {
    it('should update a step successfully', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const stepId = '123e4567-e89b-12d3-a456-426614174000';
      const updateDto: UpdateUserStepDto = {
        stepName: 'Bước đã cập nhật',
        stepDescription: 'Mô tả bước đã cập nhật'
      };
      const updatedStep = { ...mockStepResponse, ...updateDto };
      mockUserStepService.update.mockResolvedValue(updatedStep);

      // Act
      const result = await controller.update(mockUser, taskId, stepId, updateDto);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeDefined();
      expect((result.result as any).stepId).toBe(stepId);
      expect((result.result as any).stepName).toBe(updateDto.stepName);
      expect((result.result as any).stepDescription).toBe(updateDto.stepDescription);
      expect(result.message).toBe('Cập nhật bước thành công');
    });
  });

  describe('findById', () => {
    it('should return a step by id', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const stepId = '123e4567-e89b-12d3-a456-426614174000';
      mockUserStepService.findById.mockResolvedValue(mockStepResponse);

      // Act
      const result = await controller.findById(mockUser, taskId, stepId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeDefined();
      expect((result.result as any).stepId).toBe(stepId);
      expect((result.result as any).taskId).toBe(taskId);
      expect(result.message).toBe('Lấy thông tin bước thành công');
    });
  });

  describe('findAll', () => {
    it('should return a paginated list of steps', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const queryDto: QueryUserStepDto = {
        page: 1,
        limit: 10
      };
      mockUserStepService.findAll.mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.findAll(mockUser, taskId, queryDto);

      // Assert
      expect(queryDto.taskId).toBe(taskId); // Kiểm tra taskId được gán đúng
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeDefined();
      expect((result.result as any).items).toBeDefined();
      expect((result.result as any).items.length).toBe(1);
      expect((result.result as any).meta.totalItems).toBe(1);
      expect(result.message).toBe('Lấy danh sách bước thành công');
    });
  });

  describe('delete', () => {
    it('should delete a step successfully', async () => {
      // Arrange
      const taskId = '123e4567-e89b-12d3-a456-426614174001';
      const stepId = '123e4567-e89b-12d3-a456-426614174000';
      mockUserStepService.delete.mockResolvedValue(true);

      // Act
      const result = await controller.delete(mockUser, taskId, stepId);

      // Assert
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBe(true);
      expect(result.message).toBe('Xóa bước thành công');
    });
  });
});
