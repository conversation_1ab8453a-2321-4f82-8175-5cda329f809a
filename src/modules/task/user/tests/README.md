# Hướng dẫn chạy test DTO cho User Task

Thư mục này chứa các file test cho các DTO của module User Task. Các test này kiểm tra tính hợp lệ của các DTO và đảm bảo chúng hoạt động đúng như mong đợi.

## Danh sách các file test

### Task DTOs
1. `create-user-task.dto.spec.ts` - Test cho CreateUserTaskDto
2. `update-user-task.dto.spec.ts` - Test cho UpdateUserTaskDto
3. `user-task-response.dto.spec.ts` - Test cho UserTaskResponseDto
4. `query-user-task.dto.spec.ts` - Test cho QueryUserTaskDto

### Step DTOs
5. `create-user-step.dto.spec.ts` - Test cho CreateUserStepDto
6. `update-user-step.dto.spec.ts` - Test cho UpdateUserStepDto
7. `user-step-response.dto.spec.ts` - Test cho UserStepResponseDto
8. `query-user-step.dto.spec.ts` - Test cho QueryUserStepDto

### Connection DTOs
9. `create-user-step-connection.dto.spec.ts` - Test cho CreateUserStepConnectionDto
10. `update-user-step-connection.dto.spec.ts` - Test cho UpdateUserStepConnectionDto
11. `user-step-connection-response.dto.spec.ts` - Test cho UserStepConnectionResponseDto
12. `query-user-step-connection.dto.spec.ts` - Test cho QueryUserStepConnectionDto

### Execution DTOs
13. `query-user-task-execution.dto.spec.ts` - Test cho QueryUserTaskExecutionDto
14. `user-task-execution-response.dto.spec.ts` - Test cho UserTaskExecutionResponseDto

## Cách chạy test

### Sử dụng script Bash

```bash
# Cấp quyền thực thi cho script
chmod +x run-dto-tests.sh

# Chạy tất cả các test DTO
./run-dto-tests.sh
```

### Sử dụng script JavaScript

```bash
# Chạy tất cả các test DTO
node run-dto-tests.js

# Chạy tất cả các test DTO với coverage
node run-dto-tests.js --coverage

# Chạy một file test DTO cụ thể
node run-dto-tests.js --file create-user-task.dto.spec.ts

# Chạy từng file test DTO một
node run-dto-tests.js --all-files
```

### Sử dụng Jest trực tiếp

```bash
# Chạy tất cả các test DTO
npx jest src/modules/task/user/tests/dto --config=src/modules/task/user/tests/jest.config.js

# Chạy một file test DTO cụ thể
npx jest src/modules/task/user/tests/dto/create-user-task.dto.spec.ts --config=src/modules/task/user/tests/jest.config.js

# Chạy tất cả các test DTO với coverage
npx jest src/modules/task/user/tests/dto --config=src/modules/task/user/tests/jest.config.js --coverage
```

## Cấu trúc của một file test DTO

Mỗi file test DTO tuân theo cấu trúc sau:

1. **Import các thư viện và DTO cần test**
2. **Mô tả test suite** - Mô tả chung về DTO đang được test
3. **Các test case** - Các trường hợp test cụ thể:
   - **Arrange**: Chuẩn bị dữ liệu test
   - **Act**: Thực hiện hành động cần test (thường là validate DTO)
   - **Assert**: Kiểm tra kết quả

## Ví dụ về một test case

```typescript
it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
  // Arrange
  const dto = plainToInstance(CreateUserTaskDto, {
    taskName: 'Tạo chiến dịch email marketing',
    taskDescription: 'Tạo chiến dịch email marketing cho sản phẩm mới ra mắt',
    agentId: '123e4567-e89b-12d3-a456-426614174000'
  });

  // Act
  const errors = await validate(dto);

  // Assert
  expect(errors.length).toBe(0);
});
```
