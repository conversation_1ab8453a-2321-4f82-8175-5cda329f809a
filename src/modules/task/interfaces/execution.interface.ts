import { TaskExecutionStatus } from '@modules/task/enums';

/**
 * Interface cho dữ liệu execution
 */
export interface IExecution {
  /**
   * ID của execution
   */
  taskExecutionId: string;
  
  /**
   * ID của task được thực thi
   */
  taskId: string;
  
  /**
   * Thời gian bắt đầu thực thi
   */
  startTime: number;
  
  /**
   * Thời gian kết thúc thực thi
   */
  endTime?: number;
  
  /**
   * Trạng thái tổng thể của execution
   */
  overallStatus: TaskExecutionStatus;
  
  /**
   * Chi tiết thực thi
   */
  executionDetails?: IExecutionDetails;
  
  /**
   * Thời gian tạo bản ghi execution
   */
  createdAt: number;
}

/**
 * Interface cho chi tiết thực thi
 */
export interface IExecutionDetails {
  /**
   * Danh sách các bước đã thực thi
   */
  steps: IStepExecution[];
  
  /**
   * Dữ liệu đầu vào ban đầu
   */
  initialInput?: Record<string, any>;
  
  /**
   * Dữ liệu đầu ra cuối cùng
   */
  finalOutput?: Record<string, any>;
  
  /**
   * Thông tin lỗi (nếu có)
   */
  error?: IExecutionError;
  
  /**
   * Thông tin bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface cho thực thi một bước
 */
export interface IStepExecution {
  /**
   * ID của step
   */
  stepId: string;
  
  /**
   * Thứ tự của step
   */
  orderIndex: number;
  
  /**
   * Tên của step
   */
  stepName: string;
  
  /**
   * Loại của step
   */
  stepType: string;
  
  /**
   * Trạng thái thực thi của step
   */
  status: TaskExecutionStatus;
  
  /**
   * Thời gian bắt đầu thực thi step
   */
  startTime: number;
  
  /**
   * Thời gian kết thúc thực thi step
   */
  endTime?: number;
  
  /**
   * Dữ liệu đầu vào của step
   */
  input?: Record<string, any>;
  
  /**
   * Dữ liệu đầu ra của step
   */
  output?: Record<string, any>;
  
  /**
   * Thông tin lỗi (nếu có)
   */
  error?: IExecutionError;
}

/**
 * Interface cho thông tin lỗi thực thi
 */
export interface IExecutionError {
  /**
   * Mã lỗi
   */
  code: string;
  
  /**
   * Thông báo lỗi
   */
  message: string;
  
  /**
   * Chi tiết lỗi
   */
  details?: Record<string, any>;
  
  /**
   * Stack trace (nếu có)
   */
  stack?: string;
}
