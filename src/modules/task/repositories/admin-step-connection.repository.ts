import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Not, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminStepConnection } from '../entities/admin-step-connection.entity';

/**
 * Repository cho entity AdminStepConnection
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng admin_step_connections
 */
@Injectable()
export class AdminStepConnectionRepository extends Repository<AdminStepConnection> {
  /**
   * Logger cho AdminStepConnectionRepository
   */
  private readonly logger = new Logger(AdminStepConnectionRepository.name);

  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(AdminStepConnection, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AdminStepConnection
   * @returns SelectQueryBuilder cho AdminStepConnection
   */
  private createBaseQuery(): SelectQueryBuilder<AdminStepConnection> {
    return this.createQueryBuilder('connection');
  }

  /**
   * Tìm kiếm kết nối theo ID
   * @param connectionId ID của kết nối
   * @returns Kết nối nếu tìm thấy, null nếu không tìm thấy
   */
  async findConnectionById(connectionId: string): Promise<AdminStepConnection | null> {
    this.logger.log(`Tìm kiếm kết nối với ID: ${connectionId}`);

    return this.createBaseQuery()
      .where('connection.connectionId = :connectionId', { connectionId })
      .getOne();
  }

  /**
   * Tìm kiếm danh sách kết nối theo task ID
   * @param taskId ID của task
   * @returns Danh sách kết nối
   */
  async findConnectionsByTaskId(taskId: string): Promise<AdminStepConnection[]> {
    this.logger.log(`Tìm kiếm danh sách kết nối của task với ID: ${taskId}`);

    return this.createBaseQuery()
      .where('connection.taskId = :taskId', { taskId })
      .getMany();
  }

  /**
   * Tìm kiếm kết nối trùng lặp
   * @param taskId ID của task
   * @param fromStepId ID của bước nguồn
   * @param toStepId ID của bước đích
   * @param outputField Tên trường output
   * @param inputField Tên trường input
   * @param excludeConnectionId ID của kết nối cần loại trừ (tùy chọn)
   * @returns Kết nối nếu tìm thấy, null nếu không tìm thấy
   */
  async findDuplicateConnection(
    taskId: string,
    fromStepId: string,
    toStepId: string,
    outputField: string,
    inputField: string,
    excludeConnectionId?: string
  ): Promise<AdminStepConnection | null> {
    this.logger.log(`Tìm kiếm kết nối trùng lặp: taskId=${taskId}, fromStepId=${fromStepId}, toStepId=${toStepId}, outputField=${outputField}, inputField=${inputField}, excludeConnectionId=${excludeConnectionId}`);

    const query = this.createBaseQuery()
      .where('connection.taskId = :taskId', { taskId })
      .andWhere('connection.fromStepId = :fromStepId', { fromStepId })
      .andWhere('connection.toStepId = :toStepId', { toStepId })
      .andWhere('connection.outputField = :outputField', { outputField })
      .andWhere('connection.inputField = :inputField', { inputField });

    if (excludeConnectionId) {
      query.andWhere('connection.connectionId != :excludeConnectionId', { excludeConnectionId });
    }

    return query.getOne();
  }

  /**
   * Tạo mới kết nối
   * @param connectionData Dữ liệu kết nối
   * @returns Kết nối đã tạo
   */
  async createConnection(connectionData: Partial<AdminStepConnection>): Promise<AdminStepConnection> {
    this.logger.log(`Tạo mới kết nối với dữ liệu: ${JSON.stringify(connectionData)}`);

    const connection = this.create(connectionData);
    return this.save(connection);
  }

  /**
   * Cập nhật thông tin kết nối
   * @param connectionId ID của kết nối
   * @param connectionData Dữ liệu cập nhật
   * @returns Kết nối đã cập nhật
   */
  async updateConnection(connectionId: string, connectionData: Partial<AdminStepConnection>): Promise<AdminStepConnection> {
    this.logger.log(`Cập nhật kết nối với ID: ${connectionId}, dữ liệu: ${JSON.stringify(connectionData)}`);

    await this.update(connectionId, connectionData);
    const updatedConnection = await this.findConnectionById(connectionId);
    if (!updatedConnection) {
      this.logger.error(`Connection with ID ${connectionId} not found after update`);
      throw new Error(`Connection with ID ${connectionId} not found after update`);
    }
    return updatedConnection;
  }

  /**
   * Xóa kết nối
   * @param connectionId ID của kết nối
   */
  async deleteConnection(connectionId: string): Promise<void> {
    this.logger.log(`Xóa kết nối với ID: ${connectionId}`);

    await this.delete(connectionId);
  }

  /**
   * Xóa tất cả kết nối liên quan đến một bước
   * @param stepId ID của bước
   */
  async deleteConnectionsByStepId(stepId: string): Promise<void> {
    this.logger.log(`Xóa tất cả kết nối liên quan đến bước với ID: ${stepId}`);

    await this.createQueryBuilder()
      .delete()
      .from(AdminStepConnection)
      .where('fromStepId = :stepId', { stepId })
      .orWhere('toStepId = :stepId', { stepId })
      .execute();
  }
}
