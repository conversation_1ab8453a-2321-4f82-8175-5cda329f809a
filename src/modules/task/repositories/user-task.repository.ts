import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder} from 'typeorm';
import { UserTask } from '@modules/task/entities';
import { QueryUserTaskDto, TaskSortBy } from '@modules/task/user/dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { USER_TASK_ERROR_CODES } from '@modules/task/user/exceptions';

/**
 * Repository cho entity UserTask
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng user_tasks
 */
@Injectable()
export class UserTaskRepository extends Repository<UserTask> {
  private readonly logger = new Logger(UserTaskRepository.name);

  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(UserTask, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho UserTask
   * @returns SelectQueryBuilder cho UserTask
   */
  private createBaseQuery(): SelectQueryBuilder<UserTask> {
    return this.createQueryBuilder('task');
  }

  /**
   * Tìm nhiệm vụ theo ID
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng (tùy chọn)
   * @returns Thông tin nhiệm vụ hoặc null nếu không tìm thấy
   */
  async findById(taskId: string, userId?: number): Promise<UserTask | null> {
    try {
      const queryBuilder = this.createBaseQuery()
        .where('task.taskId = :taskId', { taskId })
        .andWhere('task.deletedAt IS NULL');

      if (userId) {
        queryBuilder.andWhere('task.userId = :userId', { userId });
      }

      return await queryBuilder.getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm nhiệm vụ theo ID ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_FETCH_FAILED,
        `Lỗi khi tìm nhiệm vụ theo ID ${taskId}`
      );
    }
  }

  /**
   * Lấy danh sách nhiệm vụ của người dùng với phân trang và lọc
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Kết quả phân trang
   */
  async findAll(userId: number, queryDto: QueryUserTaskDto): Promise<PaginatedResult<UserTask>> {
    try {
      const { page, limit, search, sortBy, sortDirection, status, active, agentId } = queryDto;
      const offset = (page - 1) * limit;

      const queryBuilder = this.createBaseQuery()
        .where('task.userId = :userId', { userId })
        .andWhere('task.deletedAt IS NULL');

      // Áp dụng các điều kiện lọc
      if (status) {
        queryBuilder.andWhere('task.status = :status', { status });
      }

      if (active !== undefined) {
        queryBuilder.andWhere('task.active = :active', { active });
      }

      if (agentId) {
        queryBuilder.andWhere('task.agentId = :agentId', { agentId });
      }

      if (search) {
        queryBuilder.andWhere('(task.taskName ILIKE :search OR task.taskDescription ILIKE :search)', {
          search: `%${search}%`,
        });
      }

      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Áp dụng sắp xếp và phân trang
      const sortColumn = sortBy || TaskSortBy.CREATED_AT;
      queryBuilder
        .orderBy(`task.${sortColumn}`, sortDirection)
        .skip(offset)
        .take(limit);

      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách nhiệm vụ: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_FETCH_FAILED,
        'Lỗi khi lấy danh sách nhiệm vụ'
      );
    }
  }

  /**
   * Tạo mới nhiệm vụ
   * @param task Thông tin nhiệm vụ cần tạo
   * @returns Nhiệm vụ đã tạo
   */
  async createTask(task: Partial<UserTask>): Promise<UserTask> {
    try {
      const newTask = this.create(task);
      return await this.save(newTask);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo nhiệm vụ: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_CREATION_FAILED,
        'Lỗi khi tạo nhiệm vụ'
      );
    }
  }

  /**
   * Cập nhật nhiệm vụ
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @param updateData Dữ liệu cập nhật
   * @returns Nhiệm vụ đã cập nhật
   */
  async updateTask(taskId: string, userId: number, updateData: Partial<UserTask>): Promise<UserTask> {
    try {
      // Kiểm tra nhiệm vụ tồn tại
      const task = await this.findById(taskId, userId);
      if (!task) {
        throw new AppException(
          USER_TASK_ERROR_CODES.USER_TASK_NOT_FOUND,
          'Không tìm thấy nhiệm vụ'
        );
      }

      // Cập nhật thời gian
      updateData.updatedAt = Date.now();

      // Cập nhật nhiệm vụ
      await this.update(taskId, updateData);

      // Trả về nhiệm vụ đã cập nhật
      return await this.findById(taskId, userId) as UserTask;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_UPDATE_FAILED,
        `Lỗi khi cập nhật nhiệm vụ ${taskId}`
      );
    }
  }

  /**
   * Xóa mềm nhiệm vụ
   * @param taskId ID của nhiệm vụ
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công
   */
  async softDeleteTask(taskId: string, userId: number): Promise<boolean> {
    try {
      // Kiểm tra nhiệm vụ tồn tại
      const task = await this.findById(taskId, userId);
      if (!task) {
        throw new AppException(
          USER_TASK_ERROR_CODES.USER_TASK_NOT_FOUND,
          'Không tìm thấy nhiệm vụ'
        );
      }

      // Cập nhật trạng thái đã xóa
      await this.update(taskId, {
        deletedAt: Date.now(),
        active: false,
      });

      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_TASK_ERROR_CODES.USER_TASK_DELETE_FAILED,
        `Lỗi khi xóa nhiệm vụ ${taskId}`
      );
    }
  }
}
