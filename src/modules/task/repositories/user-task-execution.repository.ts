import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserTaskExecution } from '@modules/task/entities';
import { QueryUserTaskExecutionDto, ExecutionSortBy } from '@modules/task/user/dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';
import { USER_EXECUTION_ERROR_CODES } from '@modules/task/user/exceptions';

/**
 * Repository cho entity UserTaskExecution
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng user_task_executions
 */
@Injectable()
export class UserTaskExecutionRepository extends Repository<UserTaskExecution> {
  private readonly logger = new Logger(UserTaskExecutionRepository.name);

  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(UserTaskExecution, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho UserTaskExecution
   * @returns SelectQueryBuilder cho UserTaskExecution
   */
  private createBaseQuery(): SelectQueryBuilder<UserTaskExecution> {
    return this.createQueryBuilder('execution');
  }

  /**
   * Tìm phiên thực thi theo ID
   * @param executionId ID của phiên thực thi
   * @returns Thông tin phiên thực thi hoặc null nếu không tìm thấy
   */
  async findById(executionId: string): Promise<UserTaskExecution | null> {
    try {
      return await this.createBaseQuery()
        .where('execution.taskExecutionId = :executionId', { executionId })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm phiên thực thi theo ID ${executionId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_EXECUTION_ERROR_CODES.USER_EXECUTION_FETCH_FAILED,
        `Lỗi khi tìm phiên thực thi theo ID ${executionId}`
      );
    }
  }

  /**
   * Lấy danh sách phiên thực thi với phân trang và lọc
   * @param queryDto Tham số truy vấn
   * @returns Kết quả phân trang
   */
  async findAll(queryDto: QueryUserTaskExecutionDto): Promise<PaginatedResult<UserTaskExecution>> {
    try {
      const { page, limit, taskId, overallStatus, sortBy, sortDirection } = queryDto;
      const offset = (page - 1) * limit;

      const queryBuilder = this.createBaseQuery();

      // Áp dụng các điều kiện lọc
      if (taskId) {
        queryBuilder.andWhere('execution.taskId = :taskId', { taskId });
      }

      if (overallStatus) {
        queryBuilder.andWhere('execution.overallStatus = :overallStatus', { overallStatus });
      }

      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Áp dụng sắp xếp và phân trang
      const sortColumn = sortBy || ExecutionSortBy.START_TIME;
      queryBuilder
        .orderBy(`execution.${sortColumn}`, sortDirection)
        .skip(offset)
        .take(limit);

      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách phiên thực thi: ${error.message}`, error.stack);
      throw new AppException(
        USER_EXECUTION_ERROR_CODES.USER_EXECUTION_FETCH_FAILED,
        'Lỗi khi lấy danh sách phiên thực thi'
      );
    }
  }
}
