import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserStep } from '@modules/task/entities';
import { QueryUserStepDto, StepSortBy } from '@modules/task/user/dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { USER_STEP_ERROR_CODES } from '@modules/task/user/exceptions';

/**
 * Repository cho entity UserStep
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng user_steps
 */
@Injectable()
export class UserStepRepository extends Repository<UserStep> {
  private readonly logger = new Logger(UserStepRepository.name);

  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(UserStep, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho UserStep
   * @returns SelectQueryBuilder cho UserStep
   */
  private createBaseQuery(): SelectQueryBuilder<UserStep> {
    return this.createQueryBuilder('step');
  }

  /**
   * Tìm bước theo ID
   * @param stepId ID của bước
   * @param taskId ID của nhiệm vụ (tùy chọn)
   * @returns Thông tin bước hoặc null nếu không tìm thấy
   */
  async findById(stepId: string, taskId?: string): Promise<UserStep | null> {
    try {
      const queryBuilder = this.createBaseQuery()
        .where('step.stepId = :stepId', { stepId });

      if (taskId) {
        queryBuilder.andWhere('step.taskId = :taskId', { taskId });
      }

      return await queryBuilder.getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm bước theo ID ${stepId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_FETCH_FAILED,
        `Lỗi khi tìm bước theo ID ${stepId}`
      );
    }
  }

  /**
   * Lấy danh sách bước của nhiệm vụ với phân trang và lọc
   * @param taskId ID của nhiệm vụ
   * @param queryDto Tham số truy vấn
   * @returns Kết quả phân trang
   */
  async findAll(taskId: string, queryDto: QueryUserStepDto): Promise<PaginatedResult<UserStep>> {
    try {
      const { page, limit, search, sortBy, sortDirection, stepType } = queryDto;
      const offset = (page - 1) * limit;

      const queryBuilder = this.createBaseQuery()
        .where('step.taskId = :taskId', { taskId });

      // Áp dụng các điều kiện lọc
      if (stepType) {
        queryBuilder.andWhere('step.stepType = :stepType', { stepType });
      }

      if (search) {
        queryBuilder.andWhere('(step.stepName ILIKE :search OR step.stepDescription ILIKE :search)', {
          search: `%${search}%`,
        });
      }

      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Áp dụng sắp xếp và phân trang
      const sortColumn = sortBy || StepSortBy.ORDER_INDEX;
      queryBuilder
        .orderBy(`step.${sortColumn}`, sortDirection)
        .skip(offset)
        .take(limit);

      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách bước của nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_FETCH_FAILED,
        'Lỗi khi lấy danh sách bước'
      );
    }
  }

  /**
   * Tạo mới bước
   * @param step Thông tin bước cần tạo
   * @returns Bước đã tạo
   */
  async createStep(step: Partial<UserStep>): Promise<UserStep> {
    try {
      const newStep = this.create(step);
      return await this.save(newStep);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo bước: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_CREATION_FAILED,
        'Lỗi khi tạo bước'
      );
    }
  }

  /**
   * Cập nhật bước
   * @param stepId ID của bước
   * @param taskId ID của nhiệm vụ
   * @param updateData Dữ liệu cập nhật
   * @returns Bước đã cập nhật
   */
  async updateStep(stepId: string, taskId: string, updateData: Partial<UserStep>): Promise<UserStep> {
    try {
      // Kiểm tra bước tồn tại
      const step = await this.findById(stepId, taskId);
      if (!step) {
        throw new AppException(
          USER_STEP_ERROR_CODES.USER_STEP_NOT_FOUND,
          'Không tìm thấy bước'
        );
      }

      // Cập nhật thời gian
      updateData.updatedAt = Date.now();

      // Cập nhật bước
      await this.update(stepId, updateData);

      // Trả về bước đã cập nhật
      return await this.findById(stepId, taskId) as UserStep;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật bước ${stepId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_UPDATE_FAILED,
        `Lỗi khi cập nhật bước ${stepId}`
      );
    }
  }

  /**
   * Xóa bước
   * @param stepId ID của bước
   * @param taskId ID của nhiệm vụ
   * @returns true nếu xóa thành công
   */
  async deleteStep(stepId: string, taskId: string): Promise<boolean> {
    try {
      // Kiểm tra bước tồn tại
      const step = await this.findById(stepId, taskId);
      if (!step) {
        throw new AppException(
          USER_STEP_ERROR_CODES.USER_STEP_NOT_FOUND,
          'Không tìm thấy bước'
        );
      }

      // Lưu lại thông tin về bước trước khi xóa
      const deletedStepType = step.stepType;
      const deletedOrderIndex = step.orderIndex;

      // Xóa bước
      await this.delete(stepId);

      // Cập nhật lại orderIndex cho các bước cùng loại có orderIndex lớn hơn
      await this.createQueryBuilder()
        .update(UserStep)
        .set({ orderIndex: () => 'order_index - 1' })
        .where('task_id = :taskId', { taskId })
        .andWhere('step_type = :stepType', { stepType: deletedStepType })
        .andWhere('order_index > :orderIndex', { orderIndex: deletedOrderIndex })
        .execute();

      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa bước ${stepId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_DELETE_FAILED,
        `Lỗi khi xóa bước ${stepId}`
      );
    }
  }

  /**
   * Kiểm tra thứ tự bước đã tồn tại trong nhiệm vụ chưa cho một loại bước cụ thể
   * @param taskId ID của nhiệm vụ
   * @param orderIndex Thứ tự cần kiểm tra
   * @param stepType Loại bước cần kiểm tra
   * @param excludeStepId ID bước cần loại trừ (dùng khi cập nhật)
   * @returns true nếu thứ tự đã tồn tại
   */
  async isOrderIndexExists(taskId: string, orderIndex: number, stepType: string, excludeStepId?: string): Promise<boolean> {
    try {
      const queryBuilder = this.createBaseQuery()
        .where('step.taskId = :taskId', { taskId })
        .andWhere('step.orderIndex = :orderIndex', { orderIndex })
        .andWhere('step.stepType = :stepType', { stepType });

      if (excludeStepId) {
        queryBuilder.andWhere('step.stepId != :excludeStepId', { excludeStepId });
      }

      const count = await queryBuilder.getCount();
      return count > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra thứ tự bước: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_FETCH_FAILED,
        'Lỗi khi kiểm tra thứ tự bước'
      );
    }
  }

  /**
   * Lấy số lượng bước trong nhiệm vụ
   * @param taskId ID của nhiệm vụ
   * @returns Số lượng bước
   */
  async countByTaskId(taskId: string): Promise<number> {
    try {
      return await this.createBaseQuery()
        .where('step.taskId = :taskId', { taskId })
        .getCount();
    } catch (error) {
      this.logger.error(`Lỗi khi đếm số lượng bước trong nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_FETCH_FAILED,
        `Lỗi khi đếm số lượng bước trong nhiệm vụ ${taskId}`
      );
    }
  }

  /**
   * Lấy thứ tự lớn nhất hiện có trong nhiệm vụ cho một loại bước cụ thể
   * @param taskId ID của nhiệm vụ
   * @param stepType Loại bước (không còn sử dụng, giữ lại để tương thích ngược)
   * @returns Thứ tự lớn nhất hoặc 0 nếu không có bước nào
   */
  async getMaxOrderIndex(taskId: string, stepType?: string): Promise<number> {
    try {
      const queryBuilder = this.createBaseQuery()
        .select('MAX(step.orderIndex)', 'maxOrderIndex')
        .where('step.taskId = :taskId', { taskId });

      const result = await queryBuilder.getRawOne();

      return result?.maxOrderIndex !== null && result?.maxOrderIndex !== undefined
        ? Number(result.maxOrderIndex)
        : 0;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thứ tự lớn nhất trong nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        USER_STEP_ERROR_CODES.USER_STEP_FETCH_FAILED,
        `Lỗi khi lấy thứ tự lớn nhất trong nhiệm vụ ${taskId}`
      );
    }
  }
}
