import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminTaskExecution } from '../entities/admin-task-execution.entity';
import { TaskExecutionStatus } from '../enums/task-execution-status.enum';

/**
 * Repository cho entity AdminTaskExecution
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng admin_task_executions
 */
@Injectable()
export class AdminTaskExecutionRepository extends Repository<AdminTaskExecution> {
  /**
   * Logger cho AdminTaskExecutionRepository
   */
  private readonly logger = new Logger(AdminTaskExecutionRepository.name);

  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(AdminTaskExecution, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho AdminTaskExecution
   * @returns SelectQueryBuilder cho AdminTaskExecution
   */
  private createBaseQuery(): SelectQueryBuilder<AdminTaskExecution> {
    return this.createQueryBuilder('execution');
  }

  /**
   * Tìm kiếm lần thực thi theo ID
   * @param executionId ID của lần thực thi
   * @returns Lần thực thi nếu tìm thấy, null nếu không tìm thấy
   */
  async findExecutionById(executionId: string): Promise<AdminTaskExecution | null> {
    this.logger.log(`Tìm kiếm lần thực thi với ID: ${executionId}`);

    return this.createBaseQuery()
      .where('execution.taskExecutionId = :executionId', { executionId })
      .getOne();
  }

  /**
   * Tìm kiếm danh sách lần thực thi với phân trang
   * @param taskId ID của task
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param sortBy Trường sắp xếp
   * @param sortOrder Hướng sắp xếp
   * @param status Trạng thái thực thi (tùy chọn)
   * @param startTimeFrom Thời gian bắt đầu từ (tùy chọn)
   * @param startTimeTo Thời gian bắt đầu đến (tùy chọn)
   * @returns Danh sách lần thực thi và tổng số lượng
   */
  async findExecutionsWithPagination(
    taskId: string,
    page: number,
    limit: number,
    sortBy: string = 'startTime',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
    status?: TaskExecutionStatus,
    startTimeFrom?: number,
    startTimeTo?: number
  ): Promise<[AdminTaskExecution[], number]> {
    this.logger.log(`Tìm kiếm danh sách lần thực thi với phân trang: taskId=${taskId}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, status=${status}, startTimeFrom=${startTimeFrom}, startTimeTo=${startTimeTo}`);

    const query = this.createBaseQuery()
      .where('execution.taskId = :taskId', { taskId });

    if (status) {
      query.andWhere('execution.overallStatus = :status', { status });
    }

    if (startTimeFrom) {
      query.andWhere('execution.startTime >= :startTimeFrom', { startTimeFrom });
    }

    if (startTimeTo) {
      query.andWhere('execution.startTime <= :startTimeTo', { startTimeTo });
    }

    const total = await query.getCount();

    const executions = await query
      .orderBy(`execution.${sortBy}`, sortOrder)
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    return [executions, total];
  }
}
