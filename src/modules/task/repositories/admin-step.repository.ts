import { Injectable, Logger } from '@nestjs/common';
import { DataSource, In, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminStep } from '../entities/admin-step.entity';

/**
 * Repository cho entity AdminStep
 * X<PERSON> lý các thao tác truy vấn dữ liệu liên quan đến bảng admin_steps
 */
@Injectable()
export class AdminStepRepository extends Repository<AdminStep> {
  /**
   * Logger cho AdminStepRepository
   */
  private readonly logger = new Logger(AdminStepRepository.name);

  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(AdminStep, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AdminStep
   * @returns SelectQueryBuilder cho AdminStep
   */
  private createBaseQuery(): SelectQueryBuilder<AdminStep> {
    return this.createQueryBuilder('step');
  }

  /**
   * Tìm kiếm bước theo ID
   * @param stepId ID của bước
   * @returns Bước nếu tìm thấy, null nếu không tìm thấy
   */
  async findStepById(stepId: string): Promise<AdminStep | null> {
    this.logger.log(`Tìm kiếm bước với ID: ${stepId}`);

    return this.createBaseQuery()
      .where('step.stepId = :stepId', { stepId })
      .getOne();
  }

  /**
   * Tìm kiếm danh sách bước theo task ID
   * @param taskId ID của task
   * @returns Danh sách bước
   */
  async findStepsByTaskId(taskId: string): Promise<AdminStep[]> {
    this.logger.log(`Tìm kiếm danh sách bước của task với ID: ${taskId}`);

    return this.createBaseQuery()
      .where('step.taskId = :taskId', { taskId })
      .orderBy('step.orderIndex', 'ASC')
      .getMany();
  }

  /**
   * Tìm kiếm danh sách bước theo danh sách ID
   * @param stepIds Danh sách ID của bước
   * @returns Danh sách bước
   */
  async findStepsByIds(stepIds: string[]): Promise<AdminStep[]> {
    this.logger.log(`Tìm kiếm danh sách bước với IDs: ${stepIds.join(', ')}`);

    return this.find({
      where: {
        stepId: In(stepIds)
      }
    });
  }

  /**
   * Lấy giá trị orderIndex lớn nhất của các bước trong task
   * @param taskId ID của task
   * @returns Giá trị orderIndex lớn nhất, hoặc 0 nếu không có bước nào
   */
  async getMaxOrderIndex(taskId: string): Promise<number> {
    this.logger.log(`Lấy giá trị orderIndex lớn nhất của các bước trong task với ID: ${taskId}`);

    const result = await this.createBaseQuery()
      .select('MAX(step.orderIndex)', 'maxOrderIndex')
      .where('step.taskId = :taskId', { taskId })
      .getRawOne();

    return result?.maxOrderIndex ? Number(result.maxOrderIndex) : 0;
  }

  /**
   * Tạo mới bước
   * @param stepData Dữ liệu bước
   * @returns Bước đã tạo
   */
  async createStep(stepData: Partial<AdminStep>): Promise<AdminStep> {
    this.logger.log(`Tạo mới bước với dữ liệu: ${JSON.stringify(stepData)}`);

    const step = this.create(stepData);
    return this.save(step);
  }

  /**
   * Cập nhật thông tin bước
   * @param stepId ID của bước
   * @param stepData Dữ liệu cập nhật
   * @returns Bước đã cập nhật
   */
  async updateStep(stepId: string, stepData: Partial<AdminStep>): Promise<AdminStep> {
    this.logger.log(`Cập nhật bước với ID: ${stepId}, dữ liệu: ${JSON.stringify(stepData)}`);

    await this.update(stepId, stepData);
    const updatedStep = await this.findStepById(stepId);
    if (!updatedStep) {
      this.logger.error(`Step with ID ${stepId} not found after update`);
      throw new Error(`Step with ID ${stepId} not found after update`);
    }
    return updatedStep;
  }

  /**
   * Cập nhật thứ tự các bước
   * @param stepIds Danh sách ID của bước theo thứ tự mới
   * @returns Danh sách bước đã cập nhật
   */
  async updateStepsOrder(stepIds: string[]): Promise<AdminStep[]> {
    this.logger.log(`Cập nhật thứ tự các bước với IDs: ${stepIds.join(', ')}`);

    // Lấy thông tin về task ID từ bước đầu tiên để sử dụng trong truy vấn
    const firstStep = await this.findStepById(stepIds[0]);
    if (!firstStep) {
      throw new Error(`Không tìm thấy bước với ID: ${stepIds[0]}`);
    }
    const taskId = firstStep.taskId;

    // Sử dụng QueryRunner để thực hiện các thao tác trong một transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Lấy tất cả các bước hiện tại của task
      const existingSteps = await this.findStepsByTaskId(taskId);
      this.logger.log(`Tìm thấy ${existingSteps.length} bước hiện tại của task ${taskId}`);

      // Tạo một bản đồ từ stepId đến orderIndex mới
      const newOrderMap = new Map<string, number>();
      stepIds.forEach((stepId, index) => {
        newOrderMap.set(stepId, index + 1);
      });
      this.logger.log(`Đã tạo bản đồ thứ tự mới cho ${stepIds.length} bước`);

      // Xử lý từng bước trong một câu lệnh SQL duy nhất để tránh xung đột
      this.logger.log('Bắt đầu cập nhật thứ tự các bước với giá trị tạm thời');

      // Sử dụng raw query để cập nhật tất cả các bước cùng lúc
      // Đầu tiên, cập nhật tất cả các bước của task này với giá trị tạm thời cao (10000)
      await queryRunner.query(
        `UPDATE admin_steps SET order_index = order_index + 10000 WHERE task_id = $1`,
        [taskId]
      );
      this.logger.log(`Đã cập nhật tất cả các bước của task ${taskId} với giá trị tạm thời`);

      // Sau đó, cập nhật từng bước với giá trị cuối cùng
      this.logger.log('Bắt đầu cập nhật thứ tự các bước với giá trị cuối cùng');
      for (const stepId of stepIds) {
        const finalOrderIndex = newOrderMap.get(stepId);
        await queryRunner.manager.update(AdminStep, { stepId }, { orderIndex: finalOrderIndex });
        this.logger.log(`Đã cập nhật bước ${stepId} với thứ tự ${finalOrderIndex}`);
      }

      // Commit transaction
      await queryRunner.commitTransaction();

      // Lấy danh sách bước đã cập nhật
      return this.findStepsByIds(stepIds);
    } catch (error) {
      // Rollback transaction nếu có lỗi
      await queryRunner.rollbackTransaction();

      // Ghi log chi tiết về lỗi
      this.logger.error(`Lỗi khi cập nhật thứ tự các bước: ${error.message}`, error.stack);

      // Thêm thông tin chi tiết về lỗi
      if (error.message && error.message.includes('unique_admin_task_order')) {
        this.logger.error('Lỗi xung đột unique constraint khi cập nhật thứ tự các bước');
        // Tạo lỗi mới với thông tin chi tiết hơn
        throw new Error(`Lỗi xung đột khi cập nhật thứ tự các bước. Chi tiết: ${error.message}`);
      }

      throw error;
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  }

  /**
   * Xóa bước
   * @param stepId ID của bước
   */
  async deleteStep(stepId: string): Promise<void> {
    this.logger.log(`Xóa bước với ID: ${stepId}`);

    await this.delete(stepId);
  }

  /**
   * Cập nhật lại thứ tự các bước sau khi xóa một bước
   * @param taskId ID của task
   * @param deletedOrderIndex Thứ tự của bước đã xóa
   */
  async reorderStepsAfterDelete(taskId: string, deletedOrderIndex: number): Promise<void> {
    this.logger.log(`Cập nhật lại thứ tự các bước sau khi xóa bước với orderIndex: ${deletedOrderIndex}`);

    // Cập nhật lại thứ tự các bước có thứ tự lớn hơn bước đã xóa
    await this.createQueryBuilder()
      .update(AdminStep)
      .set({
        orderIndex: () => 'order_index - 1'
      })
      .where('taskId = :taskId', { taskId })
      .andWhere('orderIndex > :deletedOrderIndex', { deletedOrderIndex })
      .execute();
  }
}
