import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserStepConnection } from '@modules/task/entities';
import { QueryUserStepConnectionDto, ConnectionSortBy } from '@modules/task/user/dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { CONNECTION_ERROR_CODES } from '@modules/task/exceptions/connection.exceptions';

/**
 * Repository cho entity UserStepConnection
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng user_step_connections
 */
@Injectable()
export class UserStepConnectionRepository extends Repository<UserStepConnection> {
  private readonly logger = new Logger(UserStepConnectionRepository.name);

  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(UserStepConnection, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho UserStepConnection
   * @returns SelectQueryBuilder cho UserStepConnection
   */
  private createBaseQuery(): SelectQueryBuilder<UserStepConnection> {
    return this.createQueryBuilder('connection');
  }

  /**
   * Tìm kết nối theo ID
   * @param connectionId ID của kết nối
   * @param taskId ID của nhiệm vụ (tùy chọn)
   * @returns Thông tin kết nối hoặc null nếu không tìm thấy
   */
  async findById(connectionId: string, taskId?: string): Promise<UserStepConnection | null> {
    try {
      const queryBuilder = this.createBaseQuery()
        .where('connection.connectionId = :connectionId', { connectionId });

      if (taskId) {
        queryBuilder.andWhere('connection.taskId = :taskId', { taskId });
      }

      return await queryBuilder.getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kết nối theo ID ${connectionId}: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_FETCH_FAILED,
        `Lỗi khi tìm kết nối theo ID ${connectionId}`
      );
    }
  }

  /**
   * Lấy danh sách kết nối với phân trang và lọc
   * @param queryDto Tham số truy vấn
   * @returns Kết quả phân trang
   */
  async findAll(queryDto: QueryUserStepConnectionDto): Promise<PaginatedResult<UserStepConnection>> {
    try {
      const { page, limit, sortBy, sortDirection, taskId, fromStepId, toStepId } = queryDto;
      const offset = (page - 1) * limit;

      const queryBuilder = this.createBaseQuery();

      // Áp dụng các điều kiện lọc
      if (taskId) {
        queryBuilder.andWhere('connection.taskId = :taskId', { taskId });
      }

      if (fromStepId) {
        queryBuilder.andWhere('connection.fromStepId = :fromStepId', { fromStepId });
      }

      if (toStepId) {
        queryBuilder.andWhere('connection.toStepId = :toStepId', { toStepId });
      }

      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Áp dụng sắp xếp và phân trang
      const sortColumn = sortBy || ConnectionSortBy.CREATED_AT;
      queryBuilder
        .orderBy(`connection.${sortColumn}`, sortDirection)
        .skip(offset)
        .take(limit);

      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách kết nối: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_FETCH_FAILED,
        'Lỗi khi lấy danh sách kết nối'
      );
    }
  }

  /**
   * Tạo mới kết nối
   * @param connection Thông tin kết nối cần tạo
   * @returns Kết nối đã tạo
   */
  async createConnection(connection: Partial<UserStepConnection>): Promise<UserStepConnection> {
    try {
      const newConnection = this.create(connection);
      return await this.save(newConnection);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo kết nối: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_CREATION_FAILED,
        'Lỗi khi tạo kết nối'
      );
    }
  }

  /**
   * Cập nhật kết nối
   * @param connectionId ID của kết nối
   * @param taskId ID của nhiệm vụ
   * @param updateData Dữ liệu cập nhật
   * @returns Kết nối đã cập nhật
   */
  async updateConnection(connectionId: string, taskId: string, updateData: Partial<UserStepConnection>): Promise<UserStepConnection> {
    try {
      // Kiểm tra kết nối tồn tại
      const connection = await this.findById(connectionId, taskId);
      if (!connection) {
        throw new AppException(
          CONNECTION_ERROR_CODES.CONNECTION_NOT_FOUND,
          'Không tìm thấy kết nối'
        );
      }

      // Cập nhật kết nối
      await this.update(connectionId, updateData);

      // Trả về kết nối đã cập nhật
      return await this.findById(connectionId, taskId) as UserStepConnection;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật kết nối ${connectionId}: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_UPDATE_FAILED,
        `Lỗi khi cập nhật kết nối ${connectionId}`
      );
    }
  }

  /**
   * Xóa kết nối
   * @param connectionId ID của kết nối
   * @param taskId ID của nhiệm vụ
   * @returns true nếu xóa thành công
   */
  async deleteConnection(connectionId: string, taskId: string): Promise<boolean> {
    try {
      // Kiểm tra kết nối tồn tại
      const connection = await this.findById(connectionId, taskId);
      if (!connection) {
        throw new AppException(
          CONNECTION_ERROR_CODES.CONNECTION_NOT_FOUND,
          'Không tìm thấy kết nối'
        );
      }

      // Xóa kết nối
      await this.delete(connectionId);

      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa kết nối ${connectionId}: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_DELETE_FAILED,
        `Lỗi khi xóa kết nối ${connectionId}`
      );
    }
  }

  /**
   * Kiểm tra kết nối đã tồn tại chưa
   * @param taskId ID của nhiệm vụ
   * @param fromStepId ID của step nguồn
   * @param toStepId ID của step đích
   * @param outputField Tên trường output
   * @param inputField Tên trường input
   * @param excludeConnectionId ID kết nối cần loại trừ (dùng khi cập nhật)
   * @returns true nếu kết nối đã tồn tại
   */
  async isConnectionExists(
    taskId: string,
    fromStepId: string,
    toStepId: string,
    outputField: string,
    inputField: string,
    excludeConnectionId?: string
  ): Promise<boolean> {
    try {
      const queryBuilder = this.createBaseQuery()
        .where('connection.taskId = :taskId', { taskId })
        .andWhere('connection.fromStepId = :fromStepId', { fromStepId })
        .andWhere('connection.toStepId = :toStepId', { toStepId })
        .andWhere('connection.outputField = :outputField', { outputField })
        .andWhere('connection.inputField = :inputField', { inputField });

      if (excludeConnectionId) {
        queryBuilder.andWhere('connection.connectionId != :excludeConnectionId', { excludeConnectionId });
      }

      const count = await queryBuilder.getCount();
      return count > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra kết nối tồn tại: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_FETCH_FAILED,
        'Lỗi khi kiểm tra kết nối tồn tại'
      );
    }
  }

  /**
   * Lấy tất cả kết nối của một nhiệm vụ
   * @param taskId ID của nhiệm vụ
   * @returns Danh sách kết nối
   */
  async findAllByTaskId(taskId: string): Promise<UserStepConnection[]> {
    try {
      return await this.createBaseQuery()
        .where('connection.taskId = :taskId', { taskId })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách kết nối của nhiệm vụ ${taskId}: ${error.message}`, error.stack);
      throw new AppException(
        CONNECTION_ERROR_CODES.CONNECTION_FETCH_FAILED,
        `Lỗi khi lấy danh sách kết nối của nhiệm vụ ${taskId}`
      );
    }
  }
}
