import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder, IsNull, Like } from 'typeorm';
import { AdminTask } from '../entities/admin-task.entity';
import { Employee } from '@modules/employee/entities/employee.entity';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';

/**
 * Repository cho entity AdminTask
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng admin_tasks
 */
@Injectable()
export class AdminTaskRepository extends Repository<AdminTask> {
  /**
   * Logger cho AdminTaskRepository
   */
  private readonly logger = new Logger(AdminTaskRepository.name);

  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   * @param cdnService Dịch vụ CDN để tạo URL avatar
   */
  constructor(
    private dataSource: DataSource,
    private readonly cdnService: CdnService
  ) {
    super(AdminTask, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AdminTask
   * @returns SelectQueryBuilder cho AdminTask
   */
  private createBaseQuery(): SelectQueryBuilder<AdminTask> {
    return this.createQueryBuilder('task');
  }

  /**
   * Tìm kiếm task theo ID
   * @param taskId ID của task
   * @returns Task nếu tìm thấy, null nếu không tìm thấy
   */
  async findTaskById(taskId: string): Promise<AdminTask | null> {
    this.logger.log(`Tìm kiếm task với ID: ${taskId}`);

    return this.createBaseQuery()
      .where('task.taskId = :taskId', { taskId })
      .andWhere('task.deletedAt IS NULL') // Chỉ trả về task chưa bị xóa
      .getOne();
  }

  /**
   * Tìm kiếm task theo ID với thông tin nhân viên
   * @param taskId ID của task
   * @returns Task với thông tin nhân viên nếu tìm thấy, null nếu không tìm thấy
   */
  async findTaskByIdWithEmployeeInfo(taskId: string): Promise<any> {
    this.logger.log(`Tìm kiếm task với ID và thông tin nhân viên: ${taskId}`);

    const query = this.createBaseQuery()
      .where('task.taskId = :taskId', { taskId })
      .andWhere('task.deletedAt IS NULL'); // Chỉ trả về task chưa bị xóa

    // Join với bảng employees cho người tạo
    query.leftJoin(Employee, 'createdByEmployee', 'task.createdBy = createdByEmployee.id')
      .addSelect([
        'createdByEmployee.id as "createdByEmployee_id"',
        'createdByEmployee.fullName as "createdByEmployee_fullName"',
        'createdByEmployee.email as "createdByEmployee_email"',
        'createdByEmployee.avatar as "createdByEmployee_avatar"'
      ]);

    // Join với bảng employees cho người cập nhật
    query.leftJoin(Employee, 'updatedByEmployee', 'task.updatedBy = updatedByEmployee.id')
      .addSelect([
        'updatedByEmployee.id as "updatedByEmployee_id"',
        'updatedByEmployee.fullName as "updatedByEmployee_fullName"',
        'updatedByEmployee.email as "updatedByEmployee_email"',
        'updatedByEmployee.avatar as "updatedByEmployee_avatar"'
      ]);

    const result = await query.getRawAndEntities();

    if (!result.entities || result.entities.length === 0) {
      return null;
    }

    const task = result.entities[0];
    const raw = result.raw[0];

    // Tạo đối tượng kết quả mới thay vì chỉnh sửa trực tiếp entity
    const taskResult: any = {
      ...task
    };

    // Thêm thông tin nhân viên tạo
    if (raw.createdByEmployee_id) {
      // Xử lý URL avatar nếu có
      let createdByAvatar = raw.createdByEmployee_avatar;
      if (createdByAvatar) {
        try {
          createdByAvatar = this.cdnService.generateUrlView(createdByAvatar, TimeIntervalEnum.ONE_HOUR);
        } catch (error) {
          this.logger.warn(`Không thể tạo URL CDN cho avatar của người tạo: ${error.message}`);
        }
      }

      taskResult.createdByInfo = {
        id: raw.createdByEmployee_id,
        fullName: raw.createdByEmployee_fullName,
        email: raw.createdByEmployee_email,
        avatar: createdByAvatar
      };
    }

    // Thêm thông tin nhân viên cập nhật
    if (raw.updatedByEmployee_id) {
      // Xử lý URL avatar nếu có
      let updatedByAvatar = raw.updatedByEmployee_avatar;
      if (updatedByAvatar) {
        try {
          updatedByAvatar = this.cdnService.generateUrlView(updatedByAvatar, TimeIntervalEnum.ONE_HOUR);
        } catch (error) {
          this.logger.warn(`Không thể tạo URL CDN cho avatar của người cập nhật: ${error.message}`);
        }
      }

      taskResult.updatedByInfo = {
        id: raw.updatedByEmployee_id,
        fullName: raw.updatedByEmployee_fullName,
        email: raw.updatedByEmployee_email,
        avatar: updatedByAvatar
      };
    }

    return taskResult;
  }

  /**
   * Tìm kiếm danh sách task với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param sortBy Trường sắp xếp
   * @param sortOrder Hướng sắp xếp
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param agentId ID của agent (tùy chọn)
   * @param active Trạng thái hoạt động (tùy chọn)
   * @returns Danh sách task và tổng số lượng
   */
  async findTasksWithPagination(
    page: number,
    limit: number,
    sortBy: string = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
    search?: string,
    agentId?: string,
    active?: boolean
  ): Promise<[AdminTask[], number]> {
    this.logger.log(`Tìm kiếm danh sách task với phân trang: page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, search=${search}, agentId=${agentId}, active=${active}`);

    const query = this.createBaseQuery()
      .where('task.deletedAt IS NULL');

    if (search) {
      query.andWhere('task.taskName ILIKE :search', { search: `%${search}%` });
    }

    if (agentId) {
      query.andWhere('task.agentId = :agentId', { agentId });
    }

    if (active !== undefined) {
      query.andWhere('task.active = :active', { active });
    }

    const total = await query.getCount();

    const tasks = await query
      .orderBy(`task.${sortBy}`, sortOrder)
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    return [tasks, total];
  }

  /**
   * Tạo mới task
   * @param taskData Dữ liệu task
   * @returns Task đã tạo
   */
  async createTask(taskData: Partial<AdminTask>): Promise<AdminTask> {
    this.logger.log(`Tạo mới task với dữ liệu: ${JSON.stringify(taskData)}`);

    const task = this.create(taskData);
    return this.save(task);
  }

  /**
   * Cập nhật thông tin task
   * @param taskId ID của task
   * @param taskData Dữ liệu cập nhật
   * @returns Task đã cập nhật
   */
  async updateTask(taskId: string, taskData: Partial<AdminTask>): Promise<AdminTask> {
    this.logger.log(`Cập nhật task với ID: ${taskId}, dữ liệu: ${JSON.stringify(taskData)}`);

    await this.update(taskId, taskData);
    const updatedTask = await this.findTaskById(taskId);
    if (!updatedTask) {
      this.logger.error(`Task with ID ${taskId} not found after update`);
      throw new Error(`Task with ID ${taskId} not found after update`);
    }
    return updatedTask;
  }

  /**
   * Xóa task (soft delete)
   * @param taskId ID của task
   * @param employeeId ID của nhân viên xóa
   */
  async softDeleteTask(taskId: string, employeeId: number): Promise<void> {
    this.logger.log(`Xóa task với ID: ${taskId}, employeeId: ${employeeId}`);

    await this.update(taskId, {
      deletedAt: Date.now(),
      deletedBy: employeeId,
      active: false // Cập nhật trạng thái active thành false khi xóa
    });
  }
}
