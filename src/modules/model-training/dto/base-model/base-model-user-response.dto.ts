import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsObject, IsString } from 'class-validator';
import { Exclude } from 'class-transformer';
import { ConfigModelBase } from '../../interfaces/config-model-base.interface';
import { TypeProviderEnum } from '@/modules/model-training/constants/type-provider.enum';
import { BaseModelStatusEnum } from '@/modules/model-training/constants/base-model-status.enum';

/**
 * DTO cho response của base model dành cho user
 * Đã loại bỏ các trường không cần thiết như author, createdBy, providerId, updatedAt từ response
 * Thuộc tính status được giữ lại để sử dụng nội bộ nhưng không hiển thị trong response
 */
export class BaseModelUserResponseDto {
  /**
   * ID của mô hình (thường là ID từ OpenAI hoặc hệ thống khác)
   */
  @ApiProperty({
    description: 'ID của mô hình',
    example: 'gpt-4o-mini-audio-preview',
  })
  @IsString()
  id: string;

  /**
   * Tên hiển thị của mô hình
   */
  @ApiProperty({
    description: 'Tên hiển thị của mô hình',
    example: 'GPT-4O Mini Audio Preview',
  })
  @IsString()
  name: string;

  /**
   * Mô tả chi tiết về mô hình (có thể để trống)
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về mô hình',
    example: 'Mô hình này sử dụng cho preview âm thanh với khả năng xử lý ngữ cảnh.',
    nullable: true,
    required: false,
  })
  @IsString()
  description: string | null;

  /**
   * Loại nhà cung cấp (OPENAI, ANTHROPIC, v.v.)
   */
  @ApiProperty({
    description: 'Loại nhà cung cấp',
    example: 'OPENAI',
    enum: TypeProviderEnum,
  })
  @IsString()
  providerType?: TypeProviderEnum;

  /**
   * Trạng thái của mô hình (chỉ sử dụng nội bộ, không hiển thị trong response)
   * @internal
   */
  @Exclude()
  @IsEnum(BaseModelStatusEnum)
  status: BaseModelStatusEnum;

  /**
   * Cấu hình của mô hình dưới dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình của mô hình dưới dạng JSON',
    example: {
      hasTopP: true,
      hasTopK: true,
      hasFunction: true,
      hasTemperature: true,
      hasText: true,
      hasImage: true,
      hasAudio: true,
      hasVideo: true,
      hasParallelToolCall: true,
      hasReasoningEffort: ['low', 'medium', 'high'],
    },
  })
  @IsObject()
  config: ConfigModelBase;

  /**
   * ID của model base tương ứng trong hệ thống (nếu có)
   */
  @ApiProperty({
    description: 'ID của model base tương ứng trong hệ thống',
    example: 'gpt-4',
    required: false,
    nullable: true,
  })
  @IsString()
  baseModelId?: string;

  /**
   * Thời điểm tạo mô hình (timestamp)
   */
  @ApiProperty({
    description: 'Thời điểm tạo mô hình (đơn vị: milliseconds)',
    example: 1715523521893,
    required: false,
  })
  createdAt: number;
}
