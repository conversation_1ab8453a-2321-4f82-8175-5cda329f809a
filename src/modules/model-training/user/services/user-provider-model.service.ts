import { QueryDto } from '@common/dto';
import { AppException } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { UserProviderModel } from '../../entities/user-provider-model.entity';
import { MODEL_TRAINING_ERROR_CODES } from '../../exceptions';
import { ApiKeyEncryptionHelper } from '../../helpers/api-key-encryption.helper';
import { UserProviderModelRepository } from '../../repositories/user-provider-model.repository';
import { CreateUserProviderModelDto, UpdateUserProviderModelDto, UserProviderModelResponseDto } from '../dto/user-provider-model';

/**
 * Service xử lý logic cho User Provider Model
 */
@Injectable()
export class UserProviderModelService {
  private readonly logger = new Logger(UserProviderModelService.name);

  constructor(
    private readonly userProviderModelRepository: UserProviderModelRepository,
    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper
  ) {
  }

  /**
   * Tạo mới provider model cho user
   * @param userId ID của user
   * @param dto DTO tạo mới
   * @returns Thông báo thành công
   */
  @Transactional()
  async create(userId: number, dto: CreateUserProviderModelDto): Promise<ApiResponseDto<{ message: string }>> {
    try {
      // Kiểm tra tên đã tồn tại chưa
      const isNameExists = await this.userProviderModelRepository.isNameExists(dto.name, userId);
      if (isNameExists) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_NAME_EXISTS);
      }

      // Mã hóa API key
      let encryptedApiKey: string;
      try {
        encryptedApiKey = this.apiKeyEncryptionHelper.encryptUserApiKey(dto.apiKey, userId);
      } catch (error) {
        this.logger.error(`Lỗi mã hóa API key: ${error.message}`);
        throw new AppException(MODEL_TRAINING_ERROR_CODES.ENCRYPTION_ERROR);
      }

      // Tạo provider model mới
      const providerModel = this.userProviderModelRepository.create({
        userId,
        name: dto.name,
        type: dto.type,
        apiKey: encryptedApiKey,
      });

      await this.userProviderModelRepository.save(providerModel);

      return ApiResponseDto.success({
        message: 'Tạo nhà cung cấp thành công'
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi tạo provider model: ${error.message}`);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_CREATE_FAILED);
    }
  }

  /**
   * Lấy danh sách provider model của user có phân trang
   * @param userId ID của user
   * @param queryDto Query parameters
   * @returns Danh sách provider model có phân trang
   */
  async findAll(userId: number, queryDto: QueryDto): Promise<ApiResponseDto<PaginatedResult<UserProviderModelResponseDto>>> {
    try {
      const result = await this.userProviderModelRepository.findPaginatedByUserId(userId, queryDto);

      const items = result.items.map(item => this.mapToResponseDto(item));

      return ApiResponseDto.paginated({
        items,
        meta: result.meta
      });
    } catch (error) {
      this.logger.error(`Lỗi lấy danh sách provider model: ${error.message}`);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR);
    }
  }

  /**
   * Cập nhật provider model của user
   * @param userId ID của user
   * @param id ID của provider model
   * @param dto DTO cập nhật
   * @returns Thông báo thành công
   */
  @Transactional()
  async update(userId: number, id: string, dto: UpdateUserProviderModelDto): Promise<ApiResponseDto<{ message: string }>> {
    try {
      // Kiểm tra provider model tồn tại
      const providerModel = await this.userProviderModelRepository.findByIdAndUserId(id, userId);
      if (!providerModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.USER_PROVIDER_NOT_FOUND);
      }

      // Kiểm tra tên đã tồn tại chưa (nếu có cập nhật tên)
      if (dto.name && dto.name !== providerModel.name) {
        const isNameExists = await this.userProviderModelRepository.isNameExists(dto.name, userId, id);
        if (isNameExists) {
          throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_NAME_EXISTS);
        }
      }

      // Cập nhật thông tin
      const updateData: Partial<UserProviderModel> = {};

      if (dto.name) {
        updateData.name = dto.name;
      }

      if (dto.apiKey) {
        try {
          updateData.apiKey = this.apiKeyEncryptionHelper.encryptUserApiKey(dto.apiKey, userId);
        } catch (error) {
          this.logger.error(`Lỗi mã hóa API key: ${error.message}`);
          throw new AppException(MODEL_TRAINING_ERROR_CODES.ENCRYPTION_ERROR);
        }
      }

      // Cập nhật thời gian
      updateData.updatedAt = Date.now();

      await this.userProviderModelRepository.update(id, updateData);

      return ApiResponseDto.success({ message: 'Cập nhật nhà cung cấp thành công' });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi cập nhật provider model: ${error.message}`);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_UPDATE_FAILED);
    }
  }

  /**
   * Xóa provider model của user
   * @param userId ID của user
   * @param id ID của provider model
   * @returns Thông báo thành công
   */
  @Transactional()
  async remove(userId: number, id: string): Promise<ApiResponseDto<{ message: string }>> {
    try {
      // Kiểm tra provider model tồn tại
      const exists = await this.userProviderModelRepository.isExists(id, userId);
      if (!exists) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.USER_PROVIDER_NOT_FOUND);
      }

      // Xóa các base model liên quan (nếu có)
      // Lưu ý: Cần điều chỉnh logic này tùy theo cách bạn liên kết base model với user provider model
      // await this.baseModelRepository.createQueryBuilder()
      //   .update()
      //   .set({ deletedAt: () => '(EXTRACT(EPOCH FROM now()) * 1000)::bigint' })
      //   .where('user_provider_id = :providerModelId', { providerModelId: id })
      //   .andWhere('deleted_at IS NULL')
      //   .execute();

      // Xóa provider model
      const deleted = await this.userProviderModelRepository.softDeleteProvider(id);
      if (!deleted) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_DELETE_FAILED);
      }

      return ApiResponseDto.success({ message: 'Xóa nhà cung cấp thành công' });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi xóa provider model: ${error.message}`);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_DELETE_FAILED);
    }
  }

  /**
   * Map từ entity sang response DTO
   * @param entity Entity
   * @returns Response DTO
   */
  private mapToResponseDto(entity: UserProviderModel): UserProviderModelResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      type: entity.type,
      createdAt: entity.createdAt,
    };
  }
}
