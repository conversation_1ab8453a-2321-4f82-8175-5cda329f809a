import { AppException } from '@/common';
import { QueryDto, SortDirection } from '@/common/dto';
import { PaginatedResult } from '@/common/response';
import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { BaseModelSortByEnum } from '../constants/base-model-sort-by.enum';
import { BaseModelStatusEnum } from '../constants/base-model-status.enum';
import { TypeProviderEnum } from '../constants/type-provider.enum';
import { BaseModelDeletedQueryDto } from '../dto/base-model/base-model-deleted-query.dto';
import { BaseModelQueryDto } from '../dto/base-model/base-model-query.dto';
import { BaseModelRes, DeletedBaseModelRes } from '../dto/base-model/base-model.dto';
import { BaseModel } from '../entities/base-model.entity';
import { MODEL_TRAINING_ERROR_CODES } from '../exceptions';

@Injectable()
export class BaseModelRepository extends Repository<BaseModel> {
  private readonly logger = new Logger(BaseModelRepository.name);

  constructor(private dataSource: DataSource) {
    super(BaseModel, dataSource.createEntityManager());
  }
  /**
   * Tạo query builder cơ bản cho base model
   * @returns SelectQueryBuilder<BaseModel> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<BaseModel> {
    return this.createQueryBuilder('model');
  }

  /**
   * Kiểm tra xem provider có đang được sử dụng bởi base model nào không
   * @param providerId ID của provider (UUID)
   * @returns true nếu provider đang được sử dụng, false nếu không
   */
  async isProviderUsed(providerId: string): Promise<boolean> {
    const count = await this.createBaseQuery()
      .where('model.provider_id = :providerId', { providerId })
      .andWhere('model.status != :status', { status: BaseModelStatusEnum.DELETED })
      .getCount();

    return count > 0;
  }

  /**
   * Tìm model theo ID
   * @param id ID của model
   * @returns Thông tin model hoặc null nếu không tìm thấy
   */
  async findById(id: string): Promise<BaseModel | null> {
    try {
      // Chỉ lấy các trường cần thiết
      return await this.createBaseQuery()
        .select([
          'model.id',
          'model.modelId',
          'model.description',
          'model.status',
          'model.providerId',
          'model.config',
          'model.createdAt',
          'model.createdBy'
        ])
        .where('model.id = :id', { id })
        .andWhere('model.deleted_at IS NULL') // Chỉ lấy model chưa bị xóa
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi lấy model với ID ${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Lấy thông tin chi tiết của một base model theo ID
   * @param id ID của base model
   * @param includeDeleted Có bao gồm model đã xóa hay không
   * @returns Thông tin chi tiết của base model, trạng thái xóa và thông tin provider
   */
  async getModelDetailById(id: string, includeDeleted: boolean = false): Promise<{
    model: BaseModel | null;
    providerName?: string;
    isDeleted?: boolean;
  }> {
    try {
      // Kiểm tra xem model có tồn tại không (bao gồm cả đã xóa)
      const modelExists = await this.createQueryBuilder('model')
        .select('model.id')
        .addSelect('model.deleted_at IS NOT NULL', 'is_deleted')
        .where('model.id = :id', { id })
        .getRawOne();

      if (!modelExists) {
        return { model: null, isDeleted: false };
      }

      const isDeleted = modelExists.is_deleted;

      // Nếu model đã bị xóa và không yêu cầu bao gồm model đã xóa
      if (isDeleted && !includeDeleted) {
        return { model: null, isDeleted: true };
      }

      // Tạo query builder để lấy thông tin model
      const queryBuilder = this.createQueryBuilder('model')
        .select([
          'model.id',
          'model.modelId',
          'model.description',
          'model.providerId',
          'model.status',
          'model.createdAt',
          'model.updatedAt',
          'model.createdBy',
          'model.updatedBy',
          'model.config'
        ])
        .where('model.id = :id', { id });

      // Nếu không bao gồm model đã xóa, thêm điều kiện deleted_at IS NULL
      if (!includeDeleted) {
        queryBuilder.andWhere('model.deleted_at IS NULL');
      } else if (isDeleted) {
        // Nếu bao gồm model đã xóa và model đã bị xóa, thêm các trường liên quan đến xóa
        queryBuilder.addSelect(['model.deletedAt', 'model.deletedBy']);
      }

      const model = await queryBuilder.getOne();

      if (!model) {
        return { model: null, isDeleted };
      }

      // Lấy thông tin provider name
      const providerResult = await this.dataSource
        .createQueryBuilder()
        .select('provider.name', 'name')
        .from('admin_provider_models', 'provider')
        .where('provider.id = :providerId', { providerId: model.providerId })
        .getRawOne();

      return {
        model,
        providerName: providerResult?.name,
        isDeleted
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết base model ${id}: ${error.message}`, error.stack);
      return { model: null, isDeleted: false };
    }
  }

  /**
   * Kiểm tra xem model đã tồn tại chưa dựa trên modelId và providerId
   * @param modelId ID của model từ provider
   * @param providerId ID của provider
   * @returns Model nếu tồn tại, null nếu không tồn tại
   */
  async findByModelIdAndProviderId(modelId: string, providerId: string): Promise<BaseModel | null> {
    try {
      return await this.createBaseQuery()
        .where('model.model_id = :modelId', { modelId })
        .andWhere('model.provider_id = :providerId', { providerId })
        .andWhere('model.deleted_at IS NULL') // Chỉ lấy model chưa bị xóa
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra model với modelId ${modelId} và providerId ${providerId}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Lấy thông tin provider type dựa trên id
   * @param id ID của model
   * @returns Provider type tương ứng với id hoặc null nếu không tìm thấy
   */
  async getProviderTypeById(id: string): Promise<TypeProviderEnum | null> {
    try {
      // Truy vấn thông tin provider type từ bảng base_models và admin_provider_models
      // Chỉ lấy trường type cần thiết
      const result = await this.dataSource
        .createQueryBuilder()
        .select('provider.type', 'type')
        .from('base_models', 'model')
        .leftJoin('admin_provider_models', 'provider', 'model.provider_id = provider.id')
        .where('model.id = :id', { id })
        .andWhere('model.deleted_at IS NULL') // Chỉ lấy model chưa bị xóa
        .getRawOne();

      if (!result || !result.type) {
        this.logger.warn(`Không tìm thấy thông tin provider type cho model ${id}`);
        return null;
      }

      return result.type as TypeProviderEnum;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin provider type cho model ${id}: ${error.message}`, error.stack);
      return null;
    }
  }
  /**
   * Lấy danh sách model với phân trang
   * @param queryParams Tham số truy vấn và phân trang
   * @returns Danh sách model với phân trang
   */
  async findAllWithPagination(
    queryParams: {
      page?: number;
      limit?: number;
      search?: string;
      sortBy?: string;
      sortDirection?: string;
    }
  ): Promise<PaginatedResult<BaseModel>> {
    const { page = 1, limit = 10, search, sortBy = 'name', sortDirection = 'asc' } = queryParams;
    const skip = (page - 1) * limit;
    // Tạo query builder
    const queryBuilder = this.createBaseQuery();
    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.where('model.name ILIKE :search', { search: `%${search}%` });
    }
    // Áp dụng sắp xếp và phân trang
    queryBuilder
      .orderBy(`model.${sortBy}`, sortDirection.toUpperCase() as 'ASC' | 'DESC')
      .skip(skip)
      .take(limit);
    // Thực hiện truy vấn
    const [items, totalItems] = await queryBuilder.getManyAndCount();
    // Trả về kết quả phân trang
    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page
      }
    };
  }

  /**
   * Tìm tất cả các base model với phân trang và tìm kiếm cho admin
   * @param query Tham số truy vấn cho phân trang và tìm kiếm
   * @returns Kết quả phân trang của base model
   */
  async findAllByAdmin(query: BaseModelQueryDto): Promise<PaginatedResult<BaseModelRes>> {
    const {
      page = query.page ?? 1,
      limit = query.limit ?? 10,
      sortBy = query.sortBy ?? 'createdAt',
      sortDirection = query.sortDirection ?? 'DESC',
      search,
    } = query;

    // Tạo query builder với các trường cần thiết
    const queryBuilder = this.createQueryBuilder('baseModel')
      .select([
        'baseModel.id',         // ID của model (cần thiết)
        'baseModel.modelId',    // ModelId thay thế cho name (cần thiết cho hiển thị)
        'baseModel.description', // Mô tả model (cần thiết cho hiển thị)
        'baseModel.status',     // Trạng thái model (cần thiết cho admin)
        'baseModel.providerId', // ID của provider (cần thiết cho việc lấy provider type)
        'baseModel.config',     // Config của model (cần thiết cho việc sử dụng model)
        'baseModel.createdAt',  // Thời gian tạo (cần thiết cho sắp xếp)
      ])
      .leftJoin('admin_provider_models', 'provider', 'provider.id = baseModel.provider_id')
      .addSelect(['provider.name AS provider_name'])
      .where('baseModel.deleted_at IS NULL'); // Chỉ lấy model chưa bị xóa

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere(
        '(baseModel.model_id ILIKE :search OR baseModel.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }
    // Admin có thể xem tất cả các trạng thái, không cần lọc theo status

    // Sắp xếp
    queryBuilder.orderBy(`baseModel.${sortBy}`, sortDirection);

    // Phân trang
    queryBuilder.skip((page - 1) * limit).take(limit);

    // Thực hiện truy vấn
    const rawItems = await queryBuilder.getRawMany(); // raw chứa full_name và avatar
    const baseModelEntities = await queryBuilder.getMany(); // entity chính

    // Chuyển đổi sang DTO
    const items = baseModelEntities.map((baseModel, index) => {
      const raw = rawItems[index];
      const dto = plainToInstance(BaseModelRes, baseModel);

      // Thêm trường providerName từ kết quả join
      dto.providerName = raw.provider_name;

      // Đảm bảo trường config được ánh xạ đúng cách
      if (baseModel.config) {
        dto.config = baseModel.config;
      }

      return dto;
    });

    // Đếm tổng số items
    const totalItems = await queryBuilder.getCount();

    // Tạo kết quả phân trang
    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }
  /**
   * Lấy tất cả base model theo provider type
   * @param providerType Loại provider cần lọc
   * @returns Danh sách base model thuộc provider type đã chọn và có trạng thái APPROVED
   */
  async findAllByProviderType(providerType: TypeProviderEnum): Promise<BaseModel[]> {
    try {
      // Tạo query builder
      // Chỉ lấy các trường cần thiết: id, config, providerId
      const queryBuilder = this.createQueryBuilder('baseModel')
        .select([
          'baseModel.id',
          'baseModel.config',
          'baseModel.providerId',
          'baseModel.status'
        ])
        .leftJoin('admin_provider_models', 'provider', 'baseModel.provider_id = provider.id')
        .where('provider.type = :providerType', { providerType })
        .andWhere('baseModel.status = :status', { status: BaseModelStatusEnum.APPROVED })
        .andWhere('baseModel.deleted_at IS NULL'); // Chỉ lấy model chưa bị xóa

      return await queryBuilder.getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi lấy base model theo provider type: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Lấy tất cả base models với phân trang và tìm kiếm cho user
   * @param query Tham số truy vấn cho phân trang và tìm kiếm
   * @returns Danh sách phân trang của base models
   */
  async findAllByUser(
    query: QueryDto,
  ): Promise<PaginatedResult<BaseModelRes>> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    // Tạo query builder với các trường cần thiết
    const queryBuilder = this.createQueryBuilder('baseModel')
      .select([
        'baseModel.id',         // ID của model (cần thiết)
        'baseModel.modelId',    // ModelId thay thế cho name (cần thiết cho hiển thị)
        'baseModel.description', // Mô tả model (cần thiết cho hiển thị)
        'baseModel.providerId', // ID của provider (cần thiết cho việc lấy provider type)
        'baseModel.config',     // Config của model (cần thiết cho việc sử dụng model)
        'baseModel.createdAt',  // Thời gian tạo (cần thiết cho sắp xếp)
        'baseModel.updatedAt',  // Thời gian cập nhật (cần thiết cho sắp xếp)
        'baseModel.deletedAt',  // Thời gian xóa (cần thiết cho sắp xếp)
      ])
      .leftJoin('admin_provider_models', 'provider', 'provider.id = baseModel.provider_id')
      .addSelect(['provider.name AS provider_name'])
      .where('baseModel.deleted_at IS NULL'); // Chỉ lấy model chưa bị xóa

    if (query.search) {
      queryBuilder.andWhere(
        '(baseModel.model_id ILIKE :search OR baseModel.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Chỉ lấy những model có status là APPROVED
    queryBuilder.andWhere('baseModel.status = :status', {
      status: BaseModelStatusEnum.APPROVED,
    });

    // Sử dụng enum để xác định trường sắp xếp
    const sortBy = query.sortBy || BaseModelSortByEnum.CREATED_AT;

    // Sử dụng enum để xác định hướng sắp xếp
    const sortDirection = query.sortDirection || SortDirection.DESC;

    queryBuilder.orderBy(`baseModel.${sortBy}`, sortDirection);
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.take(limit);

    // Thực hiện truy vấn
    const rawItems = await queryBuilder.getRawMany(); // raw chứa full_name và avatar
    const baseModelEntities = await queryBuilder.getMany(); // entity chính

    // Chuyển đổi sang DTO
    const items = baseModelEntities.map((baseModel, index) => {
      const raw = rawItems[index];
      const dto = plainToInstance(BaseModelRes, baseModel);

      // Thêm trường providerName từ kết quả join
      dto.providerName = raw.provider_name;

      dto.status = BaseModelStatusEnum.APPROVED; // Mặc định là APPROVED vì đã lọc ở trên

      // Đảm bảo trường config được ánh xạ đúng cách
      if (baseModel.config) {
        dto.config = baseModel.config;
      }

      return dto;
    });

    // Đếm tổng số items
    const totalItems = await queryBuilder.getCount();

    // Tạo kết quả phân trang
    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Phê duyệt một base model, chuyển trạng thái từ DRAFT sang APPROVED
   * @param id ID của base model cần phê duyệt
   * @param userId ID của người thực hiện phê duyệt
   * @returns Thông tin base model sau khi được phê duyệt hoặc null nếu không tìm thấy
   */
  @Transactional()
  async approveModel(id: string, userId: number): Promise<BaseModel | null> {
    try {
      // Kiểm tra sự tồn tại của model với trạng thái DRAFT
      const existingModel: { id: string; status: BaseModelStatusEnum } | null = await this.createQueryBuilder('model')
        .select(['model.id', 'model.status'])
        .where('model.id = :id', { id })
        .andWhere('model.deleted_at IS NULL') // Chỉ lấy model chưa bị xóa
        .getOne();

      if (!existingModel) {
        return null;
      }

      if (existingModel.status !== BaseModelStatusEnum.DRAFT) {
        return null;
      }

      // Thời gian cập nhậ
      const updatedAt = Date.now();

      // Thực hiện update trực tiếp các trường cần thiết
      const updateResult = await this.createQueryBuilder()
        .update(BaseModel)
        .set({
          status: BaseModelStatusEnum.APPROVED,
          updatedAt: updatedAt,
          updatedBy: userId
        })
        .where('id = :id', { id })
        .andWhere('status = :status', { status: BaseModelStatusEnum.DRAFT })
        .execute();

      if (updateResult.affected === 0) {
        return null;
      }

      // Lấy model đã cập nhật để trả về
      return await this.createQueryBuilder('model')
        .select([
          'model.id',
          'model.modelId',
          'model.description',
          'model.status',
          'model.providerId',
          'model.config',
          'model.createdAt',
          'model.updatedAt',
          'model.createdBy',
          'model.updatedBy'
        ])
        .where('model.id = :id', { id })
        .andWhere('model.deleted_at IS NULL') // Chỉ lấy model chưa bị xóa
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi phê duyệt base model ${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Xóa mềm base model (cập nhật trạng thái thành DELETED)
   * @param id ID của base model cần xóa
   * @param userId ID của người thực hiện xóa
   * @returns true nếu xóa thành công, false nếu không tìm thấy hoặc có lỗi
   */
  @Transactional()
  async deleteBaseModel(id: string, userId: number): Promise<boolean> {
    try {
      // Kiểm tra sự tồn tại của model
      const exists = await this.createQueryBuilder('model')
        .select('model.id')
        .where('model.id = :id', { id })
        .andWhere('model.deleted_at IS NULL') // Chỉ lấy model chưa bị xóa
        .getExists();

      if (!exists) {
        return false;
      }

      // Thời gian hiện tại
      const currentTime = Date.now();

      // Thực hiện update trực tiếp trạng thái và thông tin cập nhật
      const result = await this.createQueryBuilder()
        .update(BaseModel)
        .set({
          status: BaseModelStatusEnum.DELETED,
          updatedAt: currentTime,
          updatedBy: userId,
          deletedAt: currentTime, // Thêm thời điểm xóa
          deletedBy: userId // Thêm người xóa
        })
        .where('id = :id', { id })
        .execute();

      return result.affected ? result.affected > 0 : false;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa base model ${id}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Lấy danh sách base model đã xóa với phân trang
   * @param query Tham số truy vấn cho phân trang và tìm kiếm
   * @returns Kết quả phân trang của base model đã xóa
   */
  async findAllDeletedByAdmin(query: BaseModelDeletedQueryDto): Promise<PaginatedResult<DeletedBaseModelRes>> {
    try {
      const {
        page = query.page ?? 1,
        limit = query.limit ?? 10,
        sortBy = query.sortBy ?? 'deletedAt', // Sắp xếp theo thời gian xóa
        sortDirection = query.sortDirection ?? 'DESC',
        search,
      } = query;

      // Tạo query builder với các trường cần thiết
      const queryBuilder = this.createQueryBuilder('baseModel')
        .select([
          'baseModel.id',         // ID của model
          'baseModel.modelId',    // ModelId
          'baseModel.description', // Mô tả model
          'baseModel.providerId', // ID của provider
          'baseModel.config',     // Config của model
          'baseModel.deletedAt',  // Thời gian xóa
          'baseModel.deletedBy',  // Người xóa
        ])
        .leftJoin('admin_provider_models', 'provider', 'provider.id = baseModel.provider_id')
        .addSelect(['provider.name AS provider_name'])
        .where('baseModel.deleted_at IS NOT NULL');

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere(
          '(baseModel.model_id ILIKE :search OR baseModel.description ILIKE :search)',
          { search: `%${search}%` },
        );
      }

      // Sắp xếp
      queryBuilder.orderBy(`baseModel.${sortBy}`, sortDirection);

      // Phân trang
      queryBuilder.skip((page - 1) * limit).take(limit);

      // Thực hiện truy vấn một lần và lấy cả thông tin raw và entity
      const [baseModelEntities, totalItems] = await queryBuilder.getManyAndCount(); // entity chính và tổng số
      const rawItems = await queryBuilder.getRawMany(); // raw chứa provider_name

      // Lấy danh sách ID của người xóa để lấy thông tin
      const deleterIds = baseModelEntities
        .filter(model => model.deletedBy !== null)
        .map(model => model.deletedBy);

      // Lấy thông tin người xóa từ bảng employees
      let deletersInfo: Array<{ employee_id: number, name: string, avatar: string }> = [];
      if (deleterIds.length > 0) {
        try {
          deletersInfo = await this.dataSource
            .createQueryBuilder()
            .select([
              '"e"."id" AS "employee_id"',
              '"e"."full_name" AS "name"',
              '"e"."avatar" AS "avatar"'
            ])
            .from('employees', 'e')
            .where('e.id IN (:...ids)', { ids: deleterIds })
            .getRawMany();
        } catch (error) {
          this.logger.warn(`Lỗi khi lấy thông tin người xóa: ${error.message}`);
          // Tiếp tục xử lý với mảng rỗng nếu có lỗi
        }
      }

      // Tạo map để dễ dàng truy cập thông tin người xóa
      const deletersMap = new Map();
      deletersInfo.forEach(deleter => {
        deletersMap.set(deleter.employee_id, {
          id: deleter.employee_id,
          name: deleter.name,
          avatar: deleter.avatar
        });
      });

      // Chuyển đổi sang DTO
      const items = baseModelEntities.map((baseModel, index) => {
        const raw = rawItems[index];
        const dto = new DeletedBaseModelRes();

        // Ánh xạ các trường cơ bản
        dto.id = baseModel.id;
        dto.description = baseModel.description;
        dto.providerId = baseModel.providerId;
        dto.providerName = raw.provider_name;
        dto.status = BaseModelStatusEnum.DELETED;
        dto.deletedAt = baseModel.deletedAt || undefined;

        // Ánh xạ config nếu có
        if (baseModel.config) {
          dto.config = baseModel.config;
        }

        // Thêm thông tin người xóa nếu có
        if (baseModel.deletedBy && deletersMap.has(baseModel.deletedBy)) {
          dto.deleter = deletersMap.get(baseModel.deletedBy);
        }

        return dto;
      });

      // Tạo kết quả phân trang
      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách base model đã xóa: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
        `Lỗi khi lấy danh sách base model đã xóa: ${error.message}`
      );
    }
  }

  /**
   * Khôi phục base model đã xóa
   * @param id ID của base model cần khôi phục
   * @param userId ID của người thực hiện khôi phục
   * @returns true nếu khôi phục thành công, false nếu không tìm thấy hoặc có lỗi
   */
  @Transactional()
  async restoreBaseModel(id: string, userId: number): Promise<boolean> {
    try {
      // Kiểm tra sự tồn tại của model đã xóa
      const exists = await this.createQueryBuilder('model')
        .select('model.id')
        .where('model.id = :id', { id })
        .andWhere('model.deleted_at IS NOT NULL')
        .getExists();

      if (!exists) {
        return false;
      }

      // Thời gian hiện tại
      const currentTime = Date.now();

      // Thực hiện update trực tiếp trạng thái và thông tin cập nhật
      const result = await this.createQueryBuilder()
        .update(BaseModel)
        .set({
          status: BaseModelStatusEnum.DRAFT, // Khôi phục về trạng thái DRAFT
          updatedAt: currentTime,
          updatedBy: userId,
          deletedAt: null, // Xóa thời điểm xóa
          deletedBy: null  // Xóa người xóa
        })
        .where('id = :id', { id })
        .andWhere('deleted_at IS NOT NULL')
        .execute();

      return result.affected ? result.affected > 0 : false;
    } catch (error) {
      this.logger.error(`Lỗi khi khôi phục base model ${id}: ${error.message}`, error.stack);
      return false;
    }
  }
}