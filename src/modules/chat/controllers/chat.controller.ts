import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ChatService } from '../services/chat.service';
import { CreateRunChatDto, TypeAgentEnum } from '../dto/create-run-chat.dto';
import { RunChatResponseDto } from '../dto/run-chat-response.dto';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto } from '@common/response';
import { AppException } from '@common/exceptions';
import { CHAT_ERROR_CODES } from '../exceptions';

/**
 * Controller xử lý các endpoint liên quan đến chat
 */
@ApiTags('Chat')
@Controller('chat')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  /**
   * Tạo run chat với agent (user hoặc admin)
   * @param userId ID của người dùng
   * @param createRunChatDto DTO chứa thông tin để tạo run chat
   * @returns Thông tin run chat đã tạo
   */
  @Post('run')
  @ApiOperation({ summary: 'Tạo run chat với agent (user hoặc admin)' })
  @ApiResponse({
    status: 201,
    description: 'Tạo run chat thành công',
    type: RunChatResponseDto,
  })
  async createRun(
    @CurrentUser('id') userId: number,
    @Body() createRunChatDto: CreateRunChatDto,
  ) {
    let result: RunChatResponseDto;

    // Phân biệt loại agent dựa trên typeAgent trong DTO
    if (createRunChatDto.typeAgent === TypeAgentEnum.USER) {
      // Xử lý cho agent user
      result = await this.chatService.createRunWithUserAgent(
        createRunChatDto,
        userId,
      );
    } else if (createRunChatDto.typeAgent === TypeAgentEnum.ADMIN) {
      // Xử lý cho agent admin
      result = await this.chatService.createRunWithAdminAgent(
        createRunChatDto,
        userId, // Sử dụng userId thay cho employeeId
      );
    } else {
      throw new AppException(CHAT_ERROR_CODES.INVALID_AGENT_TYPE);
    }

    return ApiResponseDto.success(result, 'Tạo run chat thành công');
  }
}
