import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChatService } from './services/chat.service';
import { ChatController } from './controllers/chat.controller';
import {
  Agent,
  AgentBase,
  AgentSystem,
  AgentUser,
  AgentRole,
  UserMultiAgent
} from '@modules/agent/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Agent,
      AgentBase,
      AgentSystem,
      AgentUser,
      AgentRole,
      UserMultiAgent,
    ]),
  ],
  controllers: [
    ChatController,
  ],
  providers: [
    ChatService,
  ],
  exports: [
    ChatService,
  ],
})
export class ChatModule {}
