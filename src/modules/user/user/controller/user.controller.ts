import { Body, Controller, Get, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserService } from '../service/user.service';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse } from '@common/response/api-response-dto';
import { TwoFactorAuthService } from '../service/two-factor-auth.service';
import {
  ChangePasswordDto,
  ChangePasswordResponseDto,
  GoogleAuthSetupDto,
  ToggleEmailAuthDto,
  ToggleGoogleAuthDto,
  ToggleSmsAuthDto,
  TwoFactorAuthDetailDto,
  TwoFactorAuthStatusDto,
  UserDto,
  VerifyGoogleAuthDto,
  UpdatePersonalInfoDto
} from '@modules/user/dto';

@ApiTags(SWAGGER_API_TAGS.USERS)
@Controller('users')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly twoFactorAuthService: TwoFactorAuthService,
  ) {}

  /**
   * Lấy số point của người dùng hiện tại
   * @returns Số point hiện tại của người dùng
   */
  @Get('points')
  @UseGuards(JwtUserGuard)
  @ApiOperation({ summary: 'Lấy số point của người dùng hiện tại' })
  @ApiResponse({ status: 200, description: 'Lấy số point thành công' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getUserPoints(@CurrentUser() user: JwtPayload) {
    const result = await this.userService.getUserPoints(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Lấy trạng thái xác thực hai lớp của người dùng
   * @param user Thông tin người dùng từ JWT
   * @returns Trạng thái xác thực hai lớp
   */
  @Get('two-factor-auth/status')
  @ApiOperation({ summary: 'Lấy trạng thái xác thực hai lớp' })
  @ApiResponse({
    status: 200,
    description: 'Lấy trạng thái xác thực hai lớp thành công',
    type: TwoFactorAuthStatusDto
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getTwoFactorAuthStatus(@CurrentUser() user: JwtPayload) {
    const result = await this.twoFactorAuthService.getTwoFactorAuthStatus(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Bật/tắt xác thực hai lớp qua SMS
   * @param user Thông tin người dùng từ JWT
   * @param toggleSmsAuthDto Thông tin bật/tắt
   * @returns Trạng thái xác thực hai lớp sau khi cập nhật
   */
  @Post('two-factor-auth/sms')
  @ApiOperation({ summary: 'Bật/tắt xác thực hai lớp qua SMS' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái xác thực hai lớp qua SMS thành công',
    type: TwoFactorAuthStatusDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async toggleSmsAuth(
    @CurrentUser() user: JwtPayload,
    @Body() toggleSmsAuthDto: ToggleSmsAuthDto,
  ) {
    await this.twoFactorAuthService.toggleSmsAuth(user.id, toggleSmsAuthDto.enabled);
    const result = await this.twoFactorAuthService.getTwoFactorAuthStatus(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Bật/tắt xác thực hai lớp qua email
   * @param user Thông tin người dùng từ JWT
   * @param toggleEmailAuthDto Thông tin bật/tắt
   * @returns Trạng thái xác thực hai lớp sau khi cập nhật
   */
  @Post('two-factor-auth/email')
  @ApiOperation({ summary: 'Bật/tắt xác thực hai lớp qua email' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái xác thực hai lớp qua email thành công',
    type: TwoFactorAuthStatusDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async toggleEmailAuth(
    @CurrentUser() user: JwtPayload,
    @Body() toggleEmailAuthDto: ToggleEmailAuthDto,
  ) {
    await this.twoFactorAuthService.toggleEmailAuth(user.id, toggleEmailAuthDto.enabled);
    const result = await this.twoFactorAuthService.getTwoFactorAuthStatus(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Tạo QR code cho Google Authenticator (GET)
   * @param user Thông tin người dùng từ JWT
   * @returns Secret key và URL QR code
   */
  @Get('two-factor-auth/google/setup')
  @ApiOperation({ summary: 'Tạo QR code cho Google Authenticator' })
  @ApiResponse({
    status: 200,
    description: 'Tạo QR code thành công',
    type: GoogleAuthSetupDto
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async setupGoogleAuthenticator(@CurrentUser() user: JwtPayload) {
    const result = await this.twoFactorAuthService.setupGoogleAuthenticator(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Tạo QR code cho Google Authenticator (POST)
   * @param user Thông tin người dùng từ JWT
   * @returns Secret key và URL QR code
   */
  @Post('two-factor-auth/google/setup')
  @ApiOperation({ summary: 'Tạo QR code cho Google Authenticator' })
  @ApiResponse({
    status: 200,
    description: 'Tạo QR code thành công',
    type: GoogleAuthSetupDto
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async setupGoogleAuthenticatorPost(@CurrentUser() user: JwtPayload) {
    const result = await this.twoFactorAuthService.setupGoogleAuthenticator(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Xác nhận cài đặt Google Authenticator
   * @param user Thông tin người dùng từ JWT
   * @param verifyGoogleAuthDto Thông tin xác nhận
   * @returns Trạng thái xác thực hai lớp sau khi cập nhật
   */
  @Post('two-factor-auth/google/verify')
  @ApiOperation({ summary: 'Xác nhận cài đặt Google Authenticator' })
  @ApiResponse({
    status: 200,
    description: 'Xác nhận cài đặt Google Authenticator thành công',
    type: TwoFactorAuthStatusDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc mã xác thực không chính xác' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async verifyGoogleAuthenticator(
    @CurrentUser() user: JwtPayload,
    @Body() verifyGoogleAuthDto: VerifyGoogleAuthDto,
  ) {
    await this.twoFactorAuthService.verifyAndEnableGoogleAuthenticator(user.id, verifyGoogleAuthDto.token);
    const result = await this.twoFactorAuthService.getTwoFactorAuthStatus(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Bật/tắt Google Authenticator
   * @param user Thông tin người dùng từ JWT
   * @param toggleGoogleAuthDto Thông tin bật/tắt
   * @returns Trạng thái xác thực hai lớp sau khi cập nhật
   */
  @Post('two-factor-auth/google')
  @ApiOperation({ summary: 'Bật/tắt Google Authenticator' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái Google Authenticator thành công',
    type: TwoFactorAuthStatusDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc chưa thiết lập Google Authenticator' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async toggleGoogleAuthenticator(
    @CurrentUser() user: JwtPayload,
    @Body() toggleGoogleAuthDto: ToggleGoogleAuthDto,
  ) {
    await this.twoFactorAuthService.toggleGoogleAuthenticator(user.id, toggleGoogleAuthDto.enabled);
    const result = await this.twoFactorAuthService.getTwoFactorAuthStatus(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Lấy thông tin chi tiết của người dùng hiện tại
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin chi tiết của người dùng
   */
  @Get('profile')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết của người dùng hiện tại' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin người dùng thành công',
    schema: AppApiResponse.getSchema(UserDto)
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getUserProfile(@CurrentUser() user: JwtPayload) {
    const result = await this.userService.getUserProfile(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Lấy thông tin chi tiết về cài đặt xác thực hai yếu tố của người dùng
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin chi tiết về cài đặt xác thực hai yếu tố
   */
  @Get('two-factor-auth/detail')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết về cài đặt xác thực hai yếu tố' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết về cài đặt xác thực hai yếu tố thành công',
    schema: AppApiResponse.getSchema(TwoFactorAuthDetailDto)
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getTwoFactorAuthDetail(@CurrentUser() user: JwtPayload) {
    const result = await this.twoFactorAuthService.getTwoFactorAuthDetail(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Cập nhật thông tin cá nhân của người dùng
   * @param user Thông tin người dùng từ JWT
   * @param updatePersonalInfoDto Thông tin cá nhân cần cập nhật
   * @returns Thông tin người dùng sau khi cập nhật
   */
  @Put('profile')
  @ApiOperation({ summary: 'Cập nhật thông tin cá nhân của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thông tin cá nhân thành công',
    schema: AppApiResponse.getSchema(UserDto)
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc số điện thoại đã tồn tại' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async updatePersonalInfo(
    @CurrentUser() user: JwtPayload,
    @Body() updatePersonalInfoDto: UpdatePersonalInfoDto
  ) {
    const result = await this.userService.updatePersonalInfo(user.id, updatePersonalInfoDto);
    return AppApiResponse.success(result, 'Cập nhật thông tin cá nhân thành công');
  }

  /**
   * Đổi mật khẩu cho người dùng
   * @param user Thông tin người dùng từ JWT
   * @param changePasswordDto Thông tin đổi mật khẩu
   * @returns Thông báo kết quả
   */
  @Post('change-password')
  @ApiOperation({ summary: 'Đổi mật khẩu cho người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Đổi mật khẩu thành công',
    schema: AppApiResponse.getSchema(ChangePasswordResponseDto)
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc mật khẩu hiện tại không chính xác' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async changePassword(
    @CurrentUser() user: JwtPayload,
    @Body() changePasswordDto: ChangePasswordDto
  ) {
    const result = await this.userService.changePassword(user.id, changePasswordDto);
    return AppApiResponse.success(result, 'Đổi mật khẩu thành công');
  }
}
