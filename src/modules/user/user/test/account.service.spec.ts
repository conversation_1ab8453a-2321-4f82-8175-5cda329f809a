import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BusinessInfo, User } from '../../entities';
import { UpdateBankInfoDto } from '../../dto/update-bank-info.dto';
import { AppException } from '@common/exceptions/app.exception';
import { AccountService } from '../service';

// Định nghĩa interface cho mock repository
interface MockRepository<T = any> {
  findOne: jest.Mock;
  save: jest.Mock;
  create: jest.Mock;
}

// Tạo mock repository
const createMockRepository = (): MockRepository => ({
  findOne: jest.fn(),
  save: jest.fn(),
  create: jest.fn(),
});

describe('AccountService', () => {
  let service: AccountService;
  let userRepository: MockRepository;
  let businessInfoRepository: MockRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AccountService,
        {
          provide: getRepositoryToken(User),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(BusinessInfo),
          useValue: createMockRepository(),
        },
      ],
    }).compile();

    service = module.get<AccountService>(AccountService);
    userRepository = module.get<MockRepository>(
      getRepositoryToken(User),
    );
    businessInfoRepository = module.get<MockRepository>(
      getRepositoryToken(BusinessInfo),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('updateBankInfo', () => {
    it('should update bank information successfully', async () => {
      // Arrange
      const userId = 1;
      const updateBankInfoDto: UpdateBankInfoDto = {
        bankCode: 'VCB',
        accountNumber: '**********',
        accountHolder: 'Nguyen Van A',
        bankBranch: 'Hà Nội',
      };
      
      const mockUser = {
        id: userId,
        bankCode: 'TCB',
        accountNumber: '**********',
        accountHolder: 'Nguyen Van A',
        bankBranch: 'Hồ Chí Minh',
        updatedAt: Date.now() - 1000,
      };

      const expectedUpdatedUser = {
        ...mockUser,
        bankCode: updateBankInfoDto.bankCode,
        accountNumber: updateBankInfoDto.accountNumber,
        accountHolder: updateBankInfoDto.accountHolder,
        bankBranch: updateBankInfoDto.bankBranch,
        updatedAt: expect.any(Number),
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(expectedUpdatedUser);

      // Act
      const result = await service.updateBankInfo(userId, updateBankInfoDto);

      // Assert
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
      expect(userRepository.save).toHaveBeenCalled();
      expect(result).toEqual(expectedUpdatedUser);
      expect(result.bankCode).toBe(updateBankInfoDto.bankCode);
      expect(result.accountNumber).toBe(updateBankInfoDto.accountNumber);
      expect(result.accountHolder).toBe(updateBankInfoDto.accountHolder);
      expect(result.bankBranch).toBe(updateBankInfoDto.bankBranch);
    });

    it('should throw AppException when user not found', async () => {
      // Arrange
      const userId = 999;
      const updateBankInfoDto: UpdateBankInfoDto = {
        bankCode: 'VCB',
        accountNumber: '**********',
        accountHolder: 'Nguyen Van A',
      };

      userRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.updateBankInfo(userId, updateBankInfoDto)).rejects.toThrow(AppException);
    });

    it('should update bank information without bankBranch when not provided', async () => {
      // Arrange
      const userId = 1;
      const updateBankInfoDto: UpdateBankInfoDto = {
        bankCode: 'VCB',
        accountNumber: '**********',
        accountHolder: 'Nguyen Van A',
        // No bankBranch provided
      };
      
      const mockUser = {
        id: userId,
        bankCode: 'TCB',
        accountNumber: '**********',
        accountHolder: 'Nguyen Van A',
        bankBranch: 'Hồ Chí Minh',
        updatedAt: Date.now() - 1000,
      };

      const expectedUpdatedUser = {
        ...mockUser,
        bankCode: updateBankInfoDto.bankCode,
        accountNumber: updateBankInfoDto.accountNumber,
        accountHolder: updateBankInfoDto.accountHolder,
        bankBranch: 'Hồ Chí Minh', // Should remain unchanged
        updatedAt: expect.any(Number),
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(expectedUpdatedUser);

      // Act
      const result = await service.updateBankInfo(userId, updateBankInfoDto);

      // Assert
      expect(userRepository.save).toHaveBeenCalled();
      expect(result.bankCode).toBe(updateBankInfoDto.bankCode);
      expect(result.accountNumber).toBe(updateBankInfoDto.accountNumber);
      expect(result.accountHolder).toBe(updateBankInfoDto.accountHolder);
      expect(result.bankBranch).toBe('Hồ Chí Minh'); // Should remain unchanged
    });
  });

  describe('getBusinessInfo', () => {
    it('should return business info when found', async () => {
      // Arrange
      const userId = 1;
      const mockBusinessInfo: BusinessInfo = {
        id: 1,
        userId: 1,
        businessName: 'Test Company',
        businessEmail: '<EMAIL>',
        businessPhone: '**********',
        businessRegistrationCertificate: 'CERT123',
        taxCode: 'TAX123456',
        representativePosition: 'CEO',
        representativeName: 'Nguyen Van A',
        status: 'active',
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      businessInfoRepository.findOne.mockResolvedValue(mockBusinessInfo);

      // Act
      const result = await service.getBusinessInfo(userId);

      // Assert
      expect(businessInfoRepository.findOne).toHaveBeenCalledWith({
        where: { userId },
      });
      expect(result).toEqual(mockBusinessInfo);
      if (result) {
        expect(result.businessName).toBe('Test Company');
        expect(result.businessEmail).toBe('<EMAIL>');
        expect(result.taxCode).toBe('TAX123456');
      }
    });

    it('should return null when business info not found', async () => {
      // Arrange
      const userId = 999;
      businessInfoRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await service.getBusinessInfo(userId);

      // Assert
      expect(businessInfoRepository.findOne).toHaveBeenCalledWith({
        where: { userId },
      });
      expect(result).toBeNull();
    });

    it('should call findOne with correct parameter type', async () => {
      // Arrange
      const userId = 123;
      businessInfoRepository.findOne.mockResolvedValue(null);

      // Act
      await service.getBusinessInfo(userId);

      // Assert
      expect(businessInfoRepository.findOne).toHaveBeenCalledWith({
        where: { userId },
      });
    });
  });
}); 