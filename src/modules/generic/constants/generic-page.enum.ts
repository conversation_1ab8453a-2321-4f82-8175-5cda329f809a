/**
 * Enum định nghĩa các trạng thái của trang
 */
export enum GenericPageStatusEnum {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

/**
 * Enum định nghĩa các trạng thái của dữ liệu gửi từ form
 */
export enum GenericPageSubmissionStatusEnum {
  PENDING = 'pending',
  PROCESSED = 'processed',
  REJECTED = 'rejected',
}

/**
 * Enum định nghĩa các trường có thể sắp xếp cho trang
 */
export enum GenericPageSortByEnum {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  PATH = 'path',
  STATUS = 'status',
}

/**
 * Enum định nghĩa các trường có thể sắp xếp cho mẫu trang
 */
export enum GenericPageTemplateSortByEnum {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  CATEGORY = 'category',
}

/**
 * Enum định nghĩa các trường có thể sắp xếp cho dữ liệu gửi từ form
 */
export enum GenericPageSubmissionSortByEnum {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  STATUS = 'status',
}
