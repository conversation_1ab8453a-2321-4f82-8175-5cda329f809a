import { ApiProperty } from '@nestjs/swagger';
import { GenericFormConfig } from '../../interfaces/generic-form-config.interface';

/**
 * DTO cho phản hồi thông tin trang cho người dùng
 */
export class GenericPageResponseDto {
  /**
   * ID của trang
   * @example "f47ac10b-58cc-4372-a567-0e02b2c3d479"
   */
  @ApiProperty({
    description: 'ID của trang',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  id: string;

  /**
   * Tên của trang
   * @example "Trang liên hệ"
   */
  @ApiProperty({
    description: 'Tên của trang',
    example: 'Trang liên hệ',
  })
  name: string;

  /**
   * Mô tả về trang
   * @example "Form liên hệ cho khách hàng"
   */
  @ApiProperty({
    description: 'Mô tả về trang',
    example: 'Form liên hệ cho khách hàng',
    nullable: true,
  })
  description: string | null;

  /**
   * Đường dẫn URL của trang
   * @example "lien-he"
   */
  @ApiProperty({
    description: 'Đường dẫn URL của trang',
    example: 'lien-he',
  })
  path: string;

  /**
   * Cấu hình trang dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình trang dạng JSON',
    example: {
      formId: 'contact-form',
      title: 'Liên hệ với chúng tôi',
      subtitle: 'Hãy để lại thông tin, chúng tôi sẽ liên hệ lại với bạn',
      groups: [],
    },
  })
  config: GenericFormConfig;

  /**
   * Thời điểm xuất bản trang
   * @example 1673363400000
   */
  @ApiProperty({
    description: 'Thời điểm xuất bản trang (Unix timestamp)',
    example: 1673363400000,
  })
  publishedAt: number;
}
