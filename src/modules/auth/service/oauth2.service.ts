import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@modules/user/entities';
import { UserService } from '@/modules/user/user/service/user.service';
import { AppException } from '@/common';
import { AUTH_ERROR_CODE } from '../errors';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { GoogleApiService } from '@shared/services/google/google-api.service';
import { UserRoleService } from '@/modules/user/user/service/user-role.service';
import { SendWithTemplateService } from '@/modules/email/service/send-with-template.service';
import { Transactional } from 'typeorm-transactional';

interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  locale: string;
}

interface FacebookUserInfo {
  id: string;
  email?: string;
  name?: string;
  first_name?: string;
  last_name?: string;
  picture?: {
    data: {
      url: string;
    };
  };
}

@Injectable()
export class OAuth2Service {
  private readonly logger = new Logger(OAuth2Service.name);
  private readonly facebookAppId: string | undefined;
  private readonly facebookAppSecret: string | undefined;
  private readonly facebookRedirectUri: string | undefined;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly googleApiService: GoogleApiService,
    private readonly userService: UserService,
    private readonly userRoleService: UserRoleService,
    private readonly sendWithTemplateService: SendWithTemplateService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {
    this.facebookAppId = this.configService.get<string>('FACEBOOK_APP_ID');
    this.facebookAppSecret = this.configService.get<string>('FACEBOOK_APP_SECRET');
    this.facebookRedirectUri = this.configService.get<string>('FACEBOOK_REDIRECT_URI');
  }

  /**
   * Tạo URL xác thực Google OAuth2
   * @returns URL xác thực Google
   */
  generateGoogleAuthUrl(redirectUri?: string): string {
    const scopes = [
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email',
    ];
    return this.googleApiService.generateAuthUrl(scopes, undefined, redirectUri);
  }

  /**
   * Tạo URL xác thực Facebook OAuth2
   * @returns URL xác thực Facebook
   */
  generateFacebookAuthUrl(redirectUri?: string): string {
    const actualRedirectUri = redirectUri || this.facebookRedirectUri;
    if (!this.facebookAppId || !actualRedirectUri) {
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Thiếu cấu hình Facebook App ID hoặc Redirect URI'
      );
    }

    const scopes = ['email', 'public_profile'].join(',');
    return `https://www.facebook.com/v18.0/dialog/oauth?client_id=${this.facebookAppId}&redirect_uri=${encodeURIComponent(actualRedirectUri)}&scope=${scopes}`;
  }

  /**
   * Xử lý đăng nhập Google OAuth2
   * @param code Authorization code từ Google
   * @param redirectUri URL chuyển hướng (tùy chọn)
   * @returns Thông tin người dùng và token
   */
  @Transactional()
  async handleGoogleLogin(code: string, redirectUri?: string): Promise<User> {
    try {
      // Đổi code lấy token
      const tokens = await this.googleApiService.getToken(code, redirectUri);
      this.googleApiService.setCredentials(tokens);

      // Lấy thông tin người dùng
      const userInfo = await this.googleApiService.getUserInfo() as GoogleUserInfo;

      if (!userInfo || !userInfo.id) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Không thể lấy thông tin người dùng từ Google'
        );
      }

      // Tìm người dùng theo Google ID
      let user = await this.userRepository.findOne({ where: { googleId: userInfo.id } });

      // Nếu không tìm thấy theo Google ID, thử tìm theo email
      if (!user && userInfo.email) {
        user = await this.userService.findByEmail(userInfo.email);
      }

      if (user) {
        // Người dùng đã tồn tại, cập nhật thông tin Google ID nếu chưa có
        if (!user.googleId) {
          user.googleId = userInfo.id;
          user.updatedAt = Date.now();
          user = await this.userRepository.save(user);
        }

        // Kiểm tra trạng thái tài khoản
        if (!user.isActive) {
          throw new AppException(AUTH_ERROR_CODE.ACCOUNT_LOCKED);
        }

        return user;
      } else {
        // Tạo người dùng mới
        return await this.createUserFromGoogle(userInfo);
      }
    } catch (error) {
      this.logger.error(`Google login failed: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED, 'Đăng nhập Google thất bại');
    }
  }

  /**
   * Xử lý đăng nhập Facebook OAuth2
   * @param code Authorization code từ Facebook
   * @param redirectUri URL chuyển hướng (tùy chọn)
   * @returns Thông tin người dùng và token
   */
  @Transactional()
  async handleFacebookLogin(code: string, redirectUri?: string): Promise<User> {
    try {
      const actualRedirectUri = redirectUri || this.facebookRedirectUri;
      
      if (!this.facebookAppId || !this.facebookAppSecret || !actualRedirectUri) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Thiếu cấu hình Facebook App'
        );
      }

      // Đổi code lấy access token
      const tokenUrl = `https://graph.facebook.com/v18.0/oauth/access_token?client_id=${this.facebookAppId}&redirect_uri=${encodeURIComponent(actualRedirectUri)}&client_secret=${this.facebookAppSecret}&code=${code}`;
      const tokenResponse = await lastValueFrom(this.httpService.get(tokenUrl));
      const { access_token } = tokenResponse.data;

      if (!access_token) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Không thể lấy access token từ Facebook'
        );
      }

      // Lấy thông tin người dùng
      const userInfoUrl = `https://graph.facebook.com/v18.0/me?fields=id,name,email,first_name,last_name,picture&access_token=${access_token}`;
      const userInfoResponse = await lastValueFrom(this.httpService.get(userInfoUrl));
      const userInfo: FacebookUserInfo = userInfoResponse.data;

      if (!userInfo || !userInfo.id) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Không thể lấy thông tin người dùng từ Facebook'
        );
      }

      // Tìm người dùng theo Facebook ID
      let user = await this.userRepository.findOne({ where: { facebookId: userInfo.id } });

      // Nếu không tìm thấy theo Facebook ID, thử tìm theo email
      if (!user && userInfo.email) {
        user = await this.userService.findByEmail(userInfo.email);
      }

      if (user) {
        // Người dùng đã tồn tại, cập nhật thông tin Facebook ID nếu chưa có
        if (!user.facebookId) {
          user.facebookId = userInfo.id;
          user.updatedAt = Date.now();
          user = await this.userRepository.save(user);
        }

        // Kiểm tra trạng thái tài khoản
        if (!user.isActive) {
          throw new AppException(AUTH_ERROR_CODE.ACCOUNT_LOCKED);
        }

        return user;
      } else {
        // Tạo người dùng mới
        return await this.createUserFromFacebook(userInfo);
      }
    } catch (error) {
      this.logger.error(`Facebook login failed: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED, 'Đăng nhập Facebook thất bại');
    }
  }

  /**
   * Tạo người dùng mới từ thông tin Google
   * @param userInfo Thông tin người dùng Google
   * @returns Người dùng đã tạo
   */
  private async createUserFromGoogle(userInfo: GoogleUserInfo): Promise<User> {
    const now = Date.now();
    const newUser = this.userRepository.create({
      email: userInfo.email,
      fullName: userInfo.name,
      googleId: userInfo.id,
      isActive: true,
      isVerifyEmail: true,
      createdAt: now,
      updatedAt: now,
    });

    const savedUser = await this.userRepository.save(newUser);

    // Gán quyền cho người dùng
    await this.userRoleService.addUserRole(savedUser.id);

    // Gửi email chào mừng nếu có email
    if (userInfo.email) {
      try {
        await this.sendWithTemplateService.sendACCOUNT_REGISTRATION_SUCCESSFUL(
          userInfo.email,
          savedUser.id
        );
      } catch (error) {
        this.logger.error(`Failed to send welcome email: ${error.message}`, error.stack);
        // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng nhập
      }
    }

    return savedUser;
  }

  /**
   * Tạo người dùng mới từ thông tin Facebook
   * @param userInfo Thông tin người dùng Facebook
   * @returns Người dùng đã tạo
   */
  private async createUserFromFacebook(userInfo: FacebookUserInfo): Promise<User> {
    const now = Date.now();
    const newUser = this.userRepository.create({
      email: userInfo.email,
      fullName: userInfo.name,
      facebookId: userInfo.id,
      isActive: true,
      isVerifyEmail: !!userInfo.email, // Đánh dấu đã xác thực email nếu có email
      createdAt: now,
      updatedAt: now,
    });

    const savedUser = await this.userRepository.save(newUser);

    // Gán quyền cho người dùng
    await this.userRoleService.addUserRole(savedUser.id);

    // Gửi email chào mừng nếu có email
    if (userInfo.email) {
      try {
        await this.sendWithTemplateService.sendACCOUNT_REGISTRATION_SUCCESSFUL(
          userInfo.email,
          savedUser.id
        );
      } catch (error) {
        this.logger.error(`Failed to send welcome email: ${error.message}`, error.stack);
        // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng nhập
      }
    }

    return savedUser;
  }
}
