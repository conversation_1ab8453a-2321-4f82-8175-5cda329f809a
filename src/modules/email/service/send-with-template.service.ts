import { Injectable, Logger } from '@nestjs/common';
import { CategoryTemplateAutoEnum } from '../interface/category-template-auto.enum';
import { EntityProviderData } from '../interface/send-email.dto';
import { TemplateService } from './template.service';

/**
 * Service cung cấp các phương thức gửi email/SMS theo template dựa vào loại email
 * Chuyển đổi từ SendWithTemplateUtils.java
 */
@Injectable()
export class SendWithTemplateService {
  private readonly logger = new Logger(SendWithTemplateService.name);

  constructor(private readonly templateService: TemplateService) {}

  /**
   * HỆ THỐNG
   */

  // Lỗi hệ thống
  public async sendSystemIncident(to: string, userId: number): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
    };
    this.sendTemplateEmail(to, CategoryTemplateAutoEnum.SYSTEM_ERROR, provider);
  }

  // Nâng cấp & b<PERSON>o trì
  public async sendSystemUpgradeAndMaintenance(
    to: string,
    userId: number,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
    };
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.UPGRADE_MAINTENANCE,
      provider,
    );
  }

  // Khắc phục sự cố thành công
  public async sendSystemIncidentResolved(
    to: string,
    userId: number,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
    };
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.SYSTEM_INCIDENT_RESOLVED,
      provider,
    );
  }

  // Cập nhật tính năng mới
  public async sendSystemFeatureUpdate(
    to: string,
    userId: number,
  ): Promise<void> {
    const provider: EntityProviderData = {};
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.SYSTEM_FEATURE_UPDATE,
      provider,
    );
  }

  /**
   * AFFILIATE
   */

  // Yêu cầu rút tiền đã được xử lý (Cá nhân)
  public async sendAffiliateWithdrawalProcessedPersonal(
    to: string,
    userId: number,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
    };
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.AFFILIATE_WITHDRAWAL_PROCESSED_PERSONAL,
      provider,
    );
  }

  // Mở khóa kho bài viết AI Premium trả phí
  public async sendAffiliateUnlockPremiumArticles(
    to: string,
    userId: number,
  ): Promise<void> {
    const provider: EntityProviderData = {};
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.AFFILIATE_UNLOCK_PREMIUM_ARTICLES,
      provider,
    );
  }

  // Đăng ký đối tác bị từ chối
  public async sendAffiliatePartnerRejected(
    to: string,
    userId: number,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
    };
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.AFFILIATE_PARTNER_REJECTED,
      provider,
    );
  }

  // Đăng ký đối tác thành công
  public async sendAffiliatePartnerRegistrationSuccessful(
    to: string,
    userId: number,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
    };
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.AFFILIATE_PARTNER_REGISTRATION_SUCCESSFUL,
      provider,
    );
  }

  // Yêu cầu rút tiền bị từ chối
  public async sendAffiliateWithdrawalRejected(
    to: string,
    userId: number,
    withdrawId: number,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
      withDrawId: withdrawId,
    };
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.AFFILIATE_WITHDRAWAL_REJECTED,
      provider,
    );
  }

  /**
   * TÀI KHOẢN
   */

  // Xác minh 2 lớp (2FA)
  public async sendAccount2FA(
    to: string,
    userId: number,
    code: string,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
      twoFACode: code,
    };
    this.sendTemplateEmail(to, CategoryTemplateAutoEnum.ACCOUNT_2FA, provider);
  }

  // Xác minh 2 lớp (2FA) cho xác thực hai lớp
  public async sendTwoFactorAuthEmail(
    to: string,
    userId: number,
    code: string,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
      twoFACode: code,
    };
    this.sendTemplateEmail(to, CategoryTemplateAutoEnum.ACCOUNT_2FA, provider);
  }

  // Thông báo | Tài khoản | Quên mật khẩu
  public async sendAccountForgotPassword(
    to: string,
    userId: number,
    newPassword: string,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
      newPassword: newPassword,
    };
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.ACCOUNT_FORGOT_PASSWORD,
      provider,
    );
  }

  // Thông báo | Tài khoản | verify email
  public async sendAccountVerificationEmail(
    to: string,
    userId: number,
    otp: string,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
      newPassword: otp, // Sử dụng trường newPassword để chứa OTP
    };
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.ACCOUNT_VERIFICATION_EMAIL,
      provider,
    );
  }

  // Thông báo | Tài khoản | verify email
  public async sendACCOUNT_REGISTRATION_SUCCESSFUL(
    to: string,
    userId: number,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
    };
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.ACCOUNT_REGISTRATION_SUCCESSFUL,
      provider,
    );
  }

  /**
   * HỢP ĐỒNG
   */

  // HĐ CTV - Ký hợp đồng OTP
  public async sendContractSignatureOTP(
    to: string,
    userId: number,
    twoFACode: string,
  ): Promise<void> {
    const provider: EntityProviderData = {
      userId: userId,
      twoFACode: twoFACode,
    };
    this.sendTemplateEmail(
      to,
      CategoryTemplateAutoEnum.CONTRACT_SIGNATURE_OTP,
      provider,
    );
  }

  /**
   * Hàm gửi email template
   * @private
   */
  private async sendTemplateEmail(
    to: string,
    templateEnum: CategoryTemplateAutoEnum,
    provider: EntityProviderData,
  ): Promise<void> {
    try {
      await this.templateService.sendEmailWithEnumTemplate(
        to,
        templateEnum,
        provider,
      );
    } catch (error) {
      this.logger.error(
        `Gửi email thất bại cho template: ${templateEnum}`,
        error.stack,
      );
    }
  }
}
