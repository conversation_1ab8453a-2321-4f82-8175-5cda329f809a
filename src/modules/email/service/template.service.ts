import { Injectable, Logger } from '@nestjs/common';
import { CategoryTemplateAutoEnum } from '../interface/category-template-auto.enum';
import { TemplateEntityData } from '../interface/template-entity.interface';
import { EntityProviderData } from '../interface/send-email.dto';
import { EmailService } from '../services/email.service';
import { InitialEntityService } from './initial-entity.service';
import { PlaceholderInjectorService } from './placeholder-injector.service';
import { AdminTemplateEmailService } from '@/modules/marketing/admin/services/admin-template-email.service';
import { AdminTemplateEmail } from '@/modules/marketing/admin/entities';

/**
 * Service xử lý gửi email từ template
 */
@Injectable()
export class TemplateService {
  private readonly logger = new Logger(TemplateService.name);

  constructor(
    private readonly adminTemplateEmailService: AdminTemplateEmailService,
    private readonly emailService: EmailService,
    private readonly initialEntityService: InitialEntityService,
    private readonly placeholderInjector: PlaceholderInjectorService,
  ) {}

  /**
   * Gửi email sử dụng loại mẫu và provider dữ liệu
   * @param to Địa chỉ email người nhận
   * @param type Loại mẫu email
   * @param provider Provider dữ liệu entity
   */
  async sendEmailWithEnumTemplate(
    to: string,
    type: CategoryTemplateAutoEnum,
    provider: EntityProviderData,
  ): Promise<void> {
    this.logger.log(`Gọi sendEmailWithEnumTemplate với tham số: ${to}, ${type}`);

    try {
      // 1. Tìm template theo category
      const template: AdminTemplateEmail = await this.adminTemplateEmailService.findTemplateAutoByCategory(type);

      const placeholders = template.placeholders || [];

      const templateEntity: TemplateEntityData = await this.initialEntityService.initialTemplateEntity(provider, placeholders);

      const res = await this.placeholderInjector.valueOfKeys(placeholders, templateEntity);

      // 2. Lấy content và subject từ template rồi chèn giá trị
      const content = this.placeholderInjector.injectPlaceholders(template.content, res);
      const subject = this.placeholderInjector.injectPlaceholders(template.subject, res);

      if (!content) {
        this.logger.warn(`Template ID=${template.id} không có nội dung`);
      }

      // 3. Gửi email
      await this.emailService.sendEmail(to, subject, content);

      this.logger.log(`Đã gửi email thành công đến ${to} với template ${type}`);
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email với category ${type}: ${error.message}`, error.stack);
      throw error;
    }
  }
}