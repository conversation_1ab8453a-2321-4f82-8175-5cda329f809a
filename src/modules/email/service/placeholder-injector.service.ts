import { Injectable, Logger } from '@nestjs/common';
import { PlaceholdersEnum } from '../interface/placeholders.enum'; // Import enum
import { TemplateEntityData } from '../interface/template-entity.interface';

@Injectable()
export class PlaceholderInjectorService {
  private readonly logger = new Logger(PlaceholderInjectorService.name);

  /**
   * Thay thế các placeholder trong một chuỗi nội dung bằng các giá trị được cung cấp.
   *
   * @param content Chuỗi nội dung gốc chứa các placeholder (ví dụ: "Chào {{USER_NAME}},...").
   * @param placeholderValues Một đối tượng Record ánh xạ từ PlaceholdersEnum sang giá trị thay thế (string).
   *                          Ví dụ: { [PlaceholdersEnum.USER_NAME]: "John Doe" }
   * @returns Chuỗi nội dung đã được thay thế các placeholder.
   */
  injectPlaceholders(
    content: string | null | undefined,
    placeholderValues: Partial<
      Record<PlaceholdersEnum, string | number | null | undefined>
    >,
  ): string {
    if (!content) {
      return ''; // Trả về chuỗi rỗng nếu content là null hoặc undefined
    }

    let injectedContent = content;

    // Lặp qua các key trong placeholderValues để thay thế
    for (const key in placeholderValues) {
      // Đảm bảo key là một thành viên của PlaceholdersEnum và thuộc tính của placeholderValues
      if (Object.prototype.hasOwnProperty.call(placeholderValues, key)) {
        const enumKey = key as PlaceholdersEnum;
        // Lấy giá trị, chuyển thành chuỗi và xử lý null/undefined thành chuỗi rỗng
        const value = placeholderValues[enumKey];
        const replacementValue =
          value === null || value === undefined ? '' : String(value);

        // Tạo chuỗi placeholder dạng {{KEY}}
        const placeholder = `{{${enumKey}}}`;

        // Sử dụng RegExp để thay thế tất cả các lần xuất hiện (global flag 'g')
        // Hàm escape đơn giản cho các ký tự có thể gây lỗi RegExp
        const escapeRegExp = (string) =>
          string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& nghĩa là toàn bộ chuỗi khớp

        try {
          const escapedPlaceholder = escapeRegExp(placeholder);
          const regex = new RegExp(escapedPlaceholder, 'g'); // Tạo RegExp với flag 'g'
          injectedContent = injectedContent.replace(regex, replacementValue);
        } catch (e) {
          this.logger.error(
            `Error creating or using regex for placeholder: ${placeholder}`,
            e,
          );
          // Bỏ qua placeholder này nếu có lỗi regex
        }
      }
    }

    return injectedContent;
  }

  /**
   * // TODO: Implement valueOfKeys based on Java logic (when available)
   *
   * Phương thức này (khi được triển khai) sẽ lấy giá trị thực tế cho các placeholder
   * dựa trên một entity hoặc đối tượng dữ liệu cụ thể.
   *
   * @param keys Danh sách các PlaceholdersEnum cần lấy giá trị.
   * @param dataEntity Đối tượng chứa dữ liệu (ví dụ: UserEntity, OrderEntity, ...).
   * @returns Partial<Record<PlaceholdersEnum, string | number | null | undefined>>
   */
  async valueOfKeys(
    keys: PlaceholdersEnum[],
    dataEntity: TemplateEntityData, // Kiểu dữ liệu sẽ phụ thuộc vào logic thực tế
  ): Promise<
    Partial<Record<PlaceholdersEnum, string | number | null | undefined>>
  > {
    const values: Partial<
      Record<PlaceholdersEnum, string | number | null | undefined>
    > = {};
    this.logger.warn('valueOfKeys method is not fully implemented yet.');

    // Logic để lấy giá trị cho từng key từ dataEntity sẽ được thêm vào đây
    // Ví dụ:
    for (const key of keys) {
      switch (key) {
        case PlaceholdersEnum.USER_NAME:
          values[key] = dataEntity?.name; // Giả sử dataEntity có thuộc tính name
          break;
        case PlaceholdersEnum.ORDER_ID:
          values[key] = dataEntity?.id; // Giả sử dataEntity có thuộc tính id
          break;
        // ... các case khác ...
      }
    }

    return values;
  }
}
