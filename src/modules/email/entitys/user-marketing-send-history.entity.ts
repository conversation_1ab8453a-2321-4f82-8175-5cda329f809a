import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { MarketingChannel } from './enums/marketing-channel.enum';
import { MarketingStatus } from './enums/marketing-status.enum';



@Entity('user_marketing_send_history')
export class UserMarketingSendHistoryEntity {
  @ApiProperty({
    description: 'ID của lịch sử gửi tin nhắn',
    example: 1
  })
  @PrimaryGeneratedColumn('increment')
  id: number;

  @ApiProperty({
    description: 'ID của người dùng gửi tin nhắn',
    example: 1
  })
  @Column({ name: 'send_by' })
  sendBy: number;

  @ApiProperty({
    description: 'Kênh gửi tin nhắn (SMS, EMAIL, ZALO, API)',
    example: 'SMS',
    enum: MarketingChannel
  })
  @Column({
    type: 'enum',
    enum: MarketingChannel,
    length: 20
  })
  channels: MarketingChannel;

  @ApiProperty({
    description: 'Nội dung của tin nhắn dạng JSON',
    example: { subject: 'Chào mừng', content: 'Xin chào' }
  })
  @Column({
    name: 'message_info_content',
    type: 'json'
  })
  messageInfoContent: Record<string, any>;

  @ApiProperty({
    description: 'Địa chỉ người nhận',
    example: '<EMAIL>'
  })
  @Column({
    name: 'to',
    length: 255
  })
  to: string;

  @ApiProperty({
    description: 'Thời gian gửi tin nhắn',
    example: 1679999999999
  })
  @Column({
    name: 'sent_at',
    type: 'bigint'
  })
  sentAt: number;

  @ApiProperty({
    description: 'Thông tin lỗi nếu có',
    example: 'Không thể kết nối đến server'
  })
  @Column({
    name: 'error_message',
    type: 'text',
    nullable: true
  })
  errorMessage: string;

  @ApiProperty({
    description: 'ID của audience nếu có',
    example: 1
  })
  @Column({
    name: 'audience_id',
    type: 'bigint',
    nullable: true
  })
  audienceId: number;

  @ApiProperty({
    description: 'Trạng thái gửi tin nhắn (SENT, FAILED, QUEUED)',
    example: 'SENT',
    enum: MarketingStatus
  })
  @Column({
    name: 'status',
    type: 'enum',
    enum: MarketingStatus,
  })
  status: MarketingStatus;

} 