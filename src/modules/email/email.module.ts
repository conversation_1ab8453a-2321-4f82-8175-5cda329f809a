import { Global, Module } from "@nestjs/common";
import { HttpModule } from "@nestjs/axios";
import { BullModule } from "@nestjs/bull";
import { PlaceholderInjectorService } from "./service/placeholder-injector.service";
import { SendWithTemplateService } from "./service/send-with-template.service";
import { TemplateService } from "./service/template.service";
import { EmailService } from "./services/email.service";
import { EmailPlaceholderService } from "./services/email-placeholder.service";
import { SystemEmailService } from "./services/system-email.service";
import { SystemEmailProcessor } from "./processors/system-email.processor";
import { SystemEmailTestController } from "./controllers/system-email-test.controller";
import { InitialEntityService } from "./service/initial-entity.service";
import { UserModule } from "../user/user.module";
import { MarketingAdminModule } from "../marketing/admin/marketing-admin.module";
import { QueueName } from "@shared/queue/queue.constants";
import { QueueModule } from "@shared/queue/queue.module";

@Global()
@Module({
  imports: [
    HttpModule,
    UserModule,
    MarketingAdminModule,
    QueueModule,
    BullModule.registerQueue({
      name: QueueName.SEND_SYSTEM_EMAIL,
    }),
  ],
  controllers: [
    SystemEmailTestController
  ],
  providers: [
    PlaceholderInjectorService,
    SendWithTemplateService,
    TemplateService,
    EmailService,
    EmailPlaceholderService,
    SystemEmailService,
    SystemEmailProcessor,
    InitialEntityService,
  ],
  exports: [
    PlaceholderInjectorService,
    SendWithTemplateService,
    TemplateService,
    EmailService,
    EmailPlaceholderService,
    SystemEmailService,
    InitialEntityService,
  ],
})
export class EmailModule {}