import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, catchError } from 'rxjs';
import { AxiosError } from 'axios';
import { QueueName, EmailJobName } from '@shared/queue/queue.constants';
import { EmailJobData, TemplateEmailJobData } from '@shared/queue/queue.types';
import { AppException, ErrorCode } from '@/common';
import { AdminTemplateEmailService } from '@/modules/marketing/admin/services/admin-template-email.service';
import { PlaceholderInjectorService } from '../service/placeholder-injector.service';

/**
 * Processor xử lý các job email từ queue
 */
@Processor(QueueName.EMAIL)
export class EmailProcessor {
  private readonly logger = new Logger(EmailProcessor.name);
  private readonly emailApiBaseUrl: string | undefined;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly adminTemplateEmailService: AdminTemplateEmailService,
    private readonly placeholderInjectorService: PlaceholderInjectorService,
  ) {
    this.emailApiBaseUrl = this.configService.get<string>('EXTERNAL_EMAIL_API_URL');
    
    if (!this.emailApiBaseUrl) {
      this.logger.error('Environment variable EXTERNAL_EMAIL_API_URL is not defined.');
      throw new AppException(ErrorCode.EMAIL_SENDING_ERROR, 'External Email API URL is not configured in environment variables.');
    }
  }

  /**
   * Xử lý job gửi email thông thường
   * @param job Job chứa dữ liệu email cần gửi
   */
  @Process(EmailJobName.SEND_EMAIL)
  async handleSendEmail(job: Job<EmailJobData>) {
    this.logger.log(`Đang xử lý job gửi email #${job.id} đến: ${job.data.to}`);
    
    try {
      await this.sendEmailViaApi({
        to: job.data.to,
        subject: job.data.subject,
        body: job.data.content,
        cc: job.data.cc,
        bcc: job.data.bcc,
        from: job.data.from,
        attachments: job.data.attachments,
      });
      
      this.logger.log(`Đã gửi email thành công cho job #${job.id}`);
      return { success: true, message: 'Email đã được gửi thành công' };
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý job gửi email #${job.id}: ${error.message}`, error.stack);
      throw error; // Ném lỗi để Bull có thể thử lại job
    }
  }

  /**
   * Xử lý job gửi email theo mẫu
   * @param job Job chứa dữ liệu email mẫu cần gửi
   */
  @Process(EmailJobName.SEND_TEMPLATE_EMAIL)
  async handleSendTemplateEmail(job: Job<TemplateEmailJobData>) {
    this.logger.log(`Đang xử lý job gửi email mẫu #${job.id} đến: ${job.data.to}`);
    
    try {
      // Chuyển đổi templateId từ string sang number
      const templateId = parseInt(job.data.templateId, 10);
      
      if (isNaN(templateId)) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, `ID mẫu email không hợp lệ: ${job.data.templateId}`);
      }
      
      // Lấy nội dung mẫu từ database qua templateId
      const template = await this.adminTemplateEmailService.findById(templateId);
      
      if (!template) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, `Không tìm thấy mẫu email với ID: ${templateId}`);
      }
      
      // Đổ dữ liệu vào mẫu
      const content = this.placeholderInjectorService.injectPlaceholders(
        template.content,
        job.data.data
      );
      
      // Gửi email với nội dung đã được đổ dữ liệu
      await this.sendEmailViaApi({
        to: job.data.to,
        subject: template.subject,
        body: content,
        cc: job.data.cc,
        bcc: job.data.bcc,
      });
      
      this.logger.log(`Đã gửi email mẫu thành công cho job #${job.id}`);
      return { success: true, message: 'Email mẫu đã được gửi thành công' };
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý job gửi email mẫu #${job.id}: ${error.message}`, error.stack);
      throw error; // Ném lỗi để Bull có thể thử lại job
    }
  }

  /**
   * Gửi email thông qua API bên ngoài
   * @param emailData Dữ liệu email cần gửi
   */
  private async sendEmailViaApi(emailData: {
    to: string;
    subject: string;
    body: string;
    cc?: string[];
    bcc?: string[];
    from?: string;
    attachments?: any[];
  }): Promise<any> {
    const apiUrl = `${this.emailApiBaseUrl}/a_email/send-email`;
    this.logger.log(`Gửi email qua API: ${apiUrl} tới ${emailData.to}`);

    try {
      const response = await firstValueFrom(
        this.httpService.post(apiUrl, emailData, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': '*/*',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(
              `Lỗi khi gửi email tới ${emailData.to}: ${error.response?.status} ${error.response?.data || error.message}`,
              error.stack,
            );
            throw new AppException(ErrorCode.EMAIL_SENDING_ERROR, `Không thể gửi email qua API: ${error.message}`);
          }),
        ),
      );

      this.logger.log(`Đã gửi email thành công tới ${emailData.to}. Mã phản hồi API: ${response.status}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
} 