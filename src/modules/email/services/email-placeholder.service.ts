import { Injectable, Logger } from '@nestjs/common';
import { EmailService } from './email.service';
import { AppException, ErrorCode } from '@/common';
import {
  AffiliateWithdrawRejectedPlaceholder,
  AccountRegistrationSuccessPlaceholder,
  TwoFAPlaceholder,
  AffiliateRankUpPlaceholder,
  TicketLowRatingPlaceholder,
  PaymentSuccessPlaceholder,
  EmailVerificationPlaceholder
} from '../enum/email-placeholder.enum';

/**
 * Service xử lý các email với placeholder dựa trên các enum
 */
@Injectable()
export class EmailPlaceholderService {
  private readonly logger = new Logger(EmailPlaceholderService.name);

  constructor(private readonly emailService: EmailService) {}

  /**
   * Gửi email thông báo rút tiền Affiliate bị từ chối
   * @param data Object chứa các giá trị tương ứng với AffiliateWithdrawRejectedPlaceholder
   * @returns Promise với job ID
   */
  async sendAffiliateWithdrawRejected(
    data: Partial<Record<keyof typeof AffiliateWithdrawRejectedPlaceholder, string | null | undefined>>
  ): Promise<string | number> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email thông báo rút tiền Affiliate bị từ chối'
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo rút tiền Affiliate bị từ chối'
        );
      }

      this.logger.log(`Chuẩn bị gửi email thông báo rút tiền Affiliate bị từ chối đến: ${data.EMAIL}`);

      // ID của mẫu email trong hệ thống
      const templateId = 'affiliate-withdraw-rejected-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Không validate tất cả các trường bắt buộc vì một số trường có thể null
      this.validateEssentialFields(cleanData, ['EMAIL']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData
      );
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email thông báo rút tiền Affiliate bị từ chối: ${error.message}`, error.stack);
      throw error instanceof AppException
        ? error
        : new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi email thông báo rút tiền Affiliate bị từ chối');
    }
  }

  /**
   * Gửi email đăng ký tài khoản thành công
   * @param data Object chứa các giá trị tương ứng với AccountRegistrationSuccessPlaceholder
   * @returns Promise với job ID
   */
  async sendAccountRegistrationSuccess(
    data: Partial<Record<keyof typeof AccountRegistrationSuccessPlaceholder, string | null | undefined>>
  ): Promise<string | number> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email thông báo đăng ký tài khoản thành công'
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo đăng ký tài khoản thành công'
        );
      }

      this.logger.log(`Chuẩn bị gửi email thông báo đăng ký tài khoản thành công đến: ${data.EMAIL}`);

      // ID của mẫu email trong hệ thống
      const templateId = 'account-registration-success-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData
      );
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email thông báo đăng ký tài khoản thành công: ${error.message}`, error.stack);
      throw error instanceof AppException
        ? error
        : new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi email thông báo đăng ký tài khoản thành công');
    }
  }

  /**
   * Gửi email xác minh 2 lớp
   * @param data Object chứa các giá trị tương ứng với TwoFAPlaceholder
   * @returns Promise với job ID
   */
  async sendTwoFAVerification(
    data: Partial<Record<keyof typeof TwoFAPlaceholder, string | null | undefined>>
  ): Promise<string | number> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email xác minh 2 lớp'
        );
      }

      // Kiểm tra email và mã xác thực có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi xác minh 2 lớp'
        );
      }

      if (!data.TWO_FA_CODE) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu mã xác thực 2FA để gửi email'
        );
      }

      this.logger.log(`Chuẩn bị gửi email xác minh 2 lớp đến: ${data.EMAIL}`);

      // ID của mẫu email trong hệ thống
      const templateId = 'two-fa-verification-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'TWO_FA_CODE']);

      // Sử dụng độ ưu tiên cao hơn cho mã xác thực
      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData
      );
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email xác minh 2 lớp: ${error.message}`, error.stack);
      throw error instanceof AppException
        ? error
        : new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi email xác minh 2 lớp');
    }
  }

  /**
   * Gửi email thông báo thay đổi Rank Affiliate tăng
   * @param data Object chứa các giá trị tương ứng với AffiliateRankUpPlaceholder
   * @returns Promise với job ID
   */
  async sendAffiliateRankUp(
    data: Partial<Record<keyof typeof AffiliateRankUpPlaceholder, string | null | undefined>>
  ): Promise<string | number> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email thông báo thay đổi Rank Affiliate tăng'
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo thay đổi Rank Affiliate tăng'
        );
      }

      this.logger.log(`Chuẩn bị gửi email thông báo thay đổi Rank Affiliate tăng đến: ${data.EMAIL}`);

      // ID của mẫu email trong hệ thống
      const templateId = 'affiliate-rank-up-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData
      );
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email thông báo thay đổi Rank Affiliate tăng: ${error.message}`, error.stack);
      throw error instanceof AppException
        ? error
        : new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi email thông báo thay đổi Rank Affiliate tăng');
    }
  }

  /**
   * Gửi email thông báo đánh giá ticket dưới 3 sao
   * @param data Object chứa các giá trị tương ứng với TicketLowRatingPlaceholder
   * @returns Promise với job ID
   */
  async sendTicketLowRating(
    data: Partial<Record<keyof typeof TicketLowRatingPlaceholder, string | null | undefined>>
  ): Promise<string | number> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email thông báo đánh giá ticket dưới 3 sao'
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo đánh giá ticket dưới 3 sao'
        );
      }

      this.logger.log(`Chuẩn bị gửi email thông báo đánh giá ticket dưới 3 sao đến: ${data.EMAIL}`);

      // ID của mẫu email trong hệ thống
      const templateId = 'ticket-low-rating-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'EMPLOYEE_NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData
      );
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email thông báo đánh giá ticket dưới 3 sao: ${error.message}`, error.stack);
      throw error instanceof AppException
        ? error
        : new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi email thông báo đánh giá ticket dưới 3 sao');
    }
  }

  /**
   * Gửi email thông báo thanh toán thành công
   * @param data Object chứa các giá trị tương ứng với PaymentSuccessPlaceholder
   * @returns Promise với job ID
   */
  async sendPaymentSuccess(
    data: Partial<Record<keyof typeof PaymentSuccessPlaceholder, string | null | undefined>>
  ): Promise<string | number> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email thông báo thanh toán thành công'
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo thanh toán thành công'
        );
      }

      this.logger.log(`Chuẩn bị gửi email thông báo thanh toán thành công đến: ${data.EMAIL}`);

      // ID của mẫu email trong hệ thống
      const templateId = 'payment-success-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME', 'ORDER_ID']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData
      );
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email thông báo thanh toán thành công: ${error.message}`, error.stack);
      throw error instanceof AppException
        ? error
        : new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi email thông báo thanh toán thành công');
    }
  }

  /**
   * Gửi email xác thực tài khoản
   * @param data Object chứa các giá trị tương ứng với EmailVerificationPlaceholder
   * @returns Promise với job ID
   */
  async sendEmailVerification(
    data: Partial<Record<keyof typeof EmailVerificationPlaceholder, string | null | undefined>>
  ): Promise<string | number> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email xác thực tài khoản'
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email xác thực tài khoản'
        );
      }

      // Kiểm tra mã xác thực có tồn tại không
      if (!data.TWO_FA_CODE) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu mã xác thực để gửi email xác thực tài khoản'
        );
      }

      this.logger.log(`Chuẩn bị gửi email xác thực tài khoản đến: ${data.EMAIL}`);

      // ID của mẫu email trong hệ thống
      const templateId = 'email-verification-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME', 'TWO_FA_CODE']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData
      );
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email xác thực tài khoản: ${error.message}`, error.stack);
      throw error instanceof AppException
        ? error
        : new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi email xác thực tài khoản');
    }
  }

  /**
   * Phương thức để xác thực các trường dữ liệu bắt buộc
   * @param data Dữ liệu cần kiểm tra
   * @param requiredFields Danh sách các trường bắt buộc
   */
  private validateEssentialFields(
    data: Record<string, string>,
    requiredFields: string[]
  ): void {
    // Kiểm tra data có tồn tại không
    if (!data) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Dữ liệu email không được để trống'
      );
    }

    // Kiểm tra các trường bắt buộc
    const missingFields: string[] = [];

    for (const field of requiredFields) {
      if (!data[field] || data[field].trim() === '') {
        missingFields.push(field);
      }
    }

    if (missingFields.length > 0) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Thiếu các trường dữ liệu bắt buộc: ${missingFields.join(', ')}`
      );
    }
  }

  /**
   * Phương thức chung để kiểm tra dữ liệu placeholder - không bắt buộc tất cả các trường
   * @param data Dữ liệu placeholder
   * @param enumType Loại enum placeholder
   */
  private validatePlaceholderData<T>(
    data: Record<string, string | null | undefined>,
    enumType: T
  ): void {
    // Kiểm tra tham số data có null không
    if (!data) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Dữ liệu email không được để trống'
      );
    }

    // Kiểm tra các trường bắt buộc
    const requiredFields: string[] = [];

    Object.values(enumType as object).forEach((field) => {
      if (typeof field === 'string') {
        const value = data[field];
        // Xử lý cả null và undefined
        if (value === null || value === undefined || value === '') {
          requiredFields.push(field);
        }
      }
    });

    if (requiredFields.length > 0) {
      this.logger.warn(`Cảnh báo: Có các trường dữ liệu chưa được cung cấp: ${requiredFields.join(', ')}`);
    }
  }

  /**
   * Phương thức để xử lý dữ liệu, loại bỏ các giá trị null và undefined
   * @param data Dữ liệu cần xử lý
   * @returns Dữ liệu đã được xử lý, không còn null/undefined
   */
  private sanitizeData<T extends Record<string, any>>(data: T): Record<string, string> {
    const result: Record<string, string> = {};

    if (!data) {
      return result;
    }

    // Duyệt qua tất cả các thuộc tính của đối tượng data
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key];

        // Kiểm tra giá trị có phải null/undefined hay không
        if (value !== null && value !== undefined) {
          // Nếu là đối tượng hoặc mảng, chuyển đổi thành chuỗi JSON
          if (typeof value === 'object') {
            result[key] = JSON.stringify(value);
          }
          // Nếu là số hoặc boolean, chuyển đổi thành chuỗi
          else if (typeof value === 'number' || typeof value === 'boolean') {
            result[key] = String(value);
          }
          // Nếu là chuỗi, giữ nguyên
          else if (typeof value === 'string') {
            result[key] = value;
          }
          // Các trường hợp khác, chuyển đổi thành chuỗi
          else {
            result[key] = String(value);
          }
        } else {
          // Thay các giá trị null/undefined bằng chuỗi rỗng
          result[key] = '';
        }
      }
    }

    return result;
  }
}