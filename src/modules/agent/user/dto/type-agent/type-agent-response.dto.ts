import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TypeAgentStatus } from '@modules/agent/constants';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';

/**
 * DTO cho thông tin cơ bản của loại agent trong danh sách
 */
export class TypeAgentListItemDto {
  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  id: number;

  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  name: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  description: string | null;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp millis)',
    example: 1672531200000,
  })
  createdAt: number;
}

/**
 * DTO cho thông tin chi tiết của nhóm công cụ
 */
export class GroupToolDto {
  /**
   * ID của nhóm công cụ
   */
  @ApiProperty({
    description: 'ID của nhóm công cụ',
    example: 1,
  })
  id: number;

  /**
   * Tên nhóm công cụ
   */
  @ApiProperty({
    description: 'Tên nhóm công cụ',
    example: 'Basic Tools',
  })
  name: string;

  /**
   * Mô tả chi tiết về nhóm công cụ
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về nhóm công cụ',
    example: 'Các công cụ cơ bản',
  })
  description: string | null;
}

/**
 * DTO cho thông tin chi tiết của loại agent
 */
export class TypeAgentDetailDto extends TypeAgentListItemDto {
  /**
   * Cấu hình mặc định cho loại agent
   */
  @ApiProperty({
    description: 'Cấu hình mặc định cho loại agent',
    example: {
      hasProfile: true,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
    },
  })
  config: TypeAgentConfig;

  /**
   * Thời điểm cập nhật (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật (timestamp millis)',
    example: 1672531200000,
  })
  updatedAt: number;

  /**
   * Trạng thái của loại agent
   */
  @ApiProperty({
    description: 'Trạng thái của loại agent',
    enum: TypeAgentStatus,
    example: TypeAgentStatus.APPROVED,
  })
  status: TypeAgentStatus;

  /**
   * Xác định loại agent là của hệ thống hay của người dùng
   */
  @ApiProperty({
    description: 'Xác định loại agent là của hệ thống (true) hay của người dùng (false)',
    example: true,
  })
  isSystem: boolean;

  /**
   * Danh sách nhóm công cụ
   */
  @ApiProperty({
    description: 'Danh sách nhóm công cụ',
    type: [GroupToolDto],
  })
  groupTools: GroupToolDto[];
}
