import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TypeAgentConfigDto } from './create-type-agent.dto';

/**
 * DTO cho việc cập nhật loại agent
 */
export class UpdateTypeAgentDto {
  /**
   * Tên loại agent
   */
  @ApiPropertyOptional({
    description: 'Tên loại agent',
    example: 'Updated Custom Agent',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  /**
   * <PERSON>ô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent tùy chỉnh đã cập nhật',
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * <PERSON><PERSON><PERSON> hình mặc định cho loại agent
   */
  @ApiPropertyOptional({
    description: '<PERSON>ấu hình mặc định cho loại agent',
    type: TypeAgentConfigDto,
    example: {
      hasProfile: true,
      hasOutput: true,
      hasConversion: true,
      hasResources: true,
    },
  })
  @ValidateNested()
  @Type(() => TypeAgentConfigDto)
  @IsOptional()
  config?: TypeAgentConfigDto;

  /**
   * Danh sách ID của các nhóm công cụ
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của các nhóm công cụ',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  groupToolIds?: number[];
}
