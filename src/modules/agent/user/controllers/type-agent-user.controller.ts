import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { ErrorCode } from '@common/exceptions';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { TypeAgentUserService } from '@modules/agent/user/services';
import {
  CreateTypeAgentDto,
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
  UpdateTypeAgentDto,
} from '../dto';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các API endpoint cho TypeAgent của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_TYPE_AGENT)
@Controller('user/type-agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  TypeAgentListItemDto,
  TypeAgentDetailDto,
  ApiResponseDto
)
export class TypeAgentUserController {
  constructor(private readonly typeAgentUserService: TypeAgentUserService) {}

  /**
   * Lấy danh sách loại agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent có phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách loại agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách loại agent thành công',
    schema: ApiResponseDto.getPaginatedSchema(TypeAgentListItemDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getTypeAgents(
    @CurrentUser('id') userId: number,
    @Query() queryDto: TypeAgentQueryDto,
  ) {
    const result = await this.typeAgentUserService.getTypeAgents(userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy chi tiết loại agent
   * @param userId ID của người dùng
   * @param id ID của loại agent
   * @returns Chi tiết loại agent
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết loại agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết loại agent thành công',
    schema: ApiResponseDto.getSchema(TypeAgentDetailDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getTypeAgentDetail(
    @CurrentUser('id') userId: number,
    @Param('id') id: number,
  ) {
    const result = await this.typeAgentUserService.getTypeAgentDetail(id, userId);
    return ApiResponseDto.success(result, 'Lấy chi tiết loại agent thành công');
  }

  /**
   * Tạo loại agent mới
   * @param userId ID của người dùng
   * @param createDto Thông tin loại agent mới
   * @returns Thông báo thành công
   */
  @Post()
  @ApiOperation({ summary: 'Tạo loại agent mới' })
  @ApiResponse({
    status: 200,
    description: 'Tạo loại agent thành công',
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS,
    AGENT_ERROR_CODES.GROUP_TOOL_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async createTypeAgent(
    @CurrentUser('id') userId: number,
    @Body() createDto: CreateTypeAgentDto,
  ) {
    await this.typeAgentUserService.createTypeAgent(userId, createDto);
    return ApiResponseDto.success(null, 'Tạo loại agent thành công');
  }

  /**
   * Cập nhật loại agent
   * @param userId ID của người dùng
   * @param id ID của loại agent
   * @param updateDto Thông tin cập nhật
   * @returns Thông báo thành công
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật loại agent' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật loại agent thành công',
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS,
    AGENT_ERROR_CODES.GROUP_TOOL_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateTypeAgent(
    @CurrentUser('id') userId: number,
    @Param('id') id: number,
    @Body() updateDto: UpdateTypeAgentDto,
  ) {
    await this.typeAgentUserService.updateTypeAgent(id, userId, updateDto);
    return ApiResponseDto.success(null, 'Cập nhật loại agent thành công');
  }

  /**
   * Xóa loại agent
   * @param userId ID của người dùng
   * @param id ID của loại agent
   * @returns Thông báo thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa loại agent' })
  @ApiResponse({
    status: 200,
    description: 'Xóa loại agent thành công',
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async deleteTypeAgent(
    @CurrentUser('id') userId: number,
    @Param('id') id: number,
  ) {
    await this.typeAgentUserService.deleteTypeAgent(id, userId);
    return ApiResponseDto.success(null, 'Xóa loại agent thành công');
  }
}
