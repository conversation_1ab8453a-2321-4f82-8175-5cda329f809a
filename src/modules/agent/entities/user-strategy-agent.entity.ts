import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { StrategyStatusEnum } from '@modules/strategy/constants';

/**
 * Entity đại diện cho bảng user_strategy_agents trong cơ sở dữ liệu
 * Bảng quản lý quyền sử dụng chiến lược agent của người dùng, xác định người dùng nào có quyền truy cập vào chiến lược và phiên bản chính thức nào
 */
@Entity('user_strategy_agents')
@Unique(['userId', 'strategyAgentId'])
export class UserStrategyAgent {
  /**
   * ID định danh duy nhất cho mỗi bản ghi quyền sử dụng, tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của người dùng được cấp quyền sử dụng chiến lư<PERSON>, tham chiếu đến bảng users
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * ID của chiến lược agent được cấp quyền, tham chiếu đến bảng strategy_agents
   */
  @Column({ name: 'strategy_agent_id', type: 'uuid' })
  strategyAgentId: string;

  /**
   * Thời điểm cấp quyền truy cập cho người dùng, dạng UNIX timestamp với millisecond
   */
  @Column({
    name: 'purchase_date',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  purchaseDate: number;

  /**
   * Trạng thái của hàm: DRAFT, PENDING, APPROVED, REJECTED, DELETE
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: StrategyStatusEnum,
    default: StrategyStatusEnum.APPROVED,
  })
  status: StrategyStatusEnum;
}
