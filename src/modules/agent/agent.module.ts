import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AgentAdminModule } from './admin/agent-admin.module';
import { AgentUserModule } from './user/agent-user.module';
import * as entities from './entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.Agent,
      entities.AgentSystem,
      entities.AgentUser,
      entities.AgentTemplate,
      entities.AgentBase,
      entities.AgentUrl,
      entities.AgentMedia,
      entities.AgentProduct,
      entities.AgentRole,
      entities.TypeAgent,
      entities.AdminGroupToolsTypeAgents,
      entities.MultiAgentsSystem,
      entities.AgentRank
    ]),
    AgentAdminModule,
    AgentUserModule
  ],
  providers: [],
  exports: [
    AgentAdminModule,
    AgentUserModule,
    TypeOrmModule
  ],
})
export class AgentModule {}
