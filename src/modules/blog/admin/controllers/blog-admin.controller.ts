import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse as ApiResponseDoc, ApiTags } from '@nestjs/swagger';
import { BlogAdminService } from '@modules/blog/admin/services';
import { CreateBlogDto, GetAdminBlogsDto, ModerateBlogDto, UpdateBlogMediaDto } from '../../dto';
import { BlogResponseDto, PaginatedBlogResponseDto } from '../../dto';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { AppException } from '@/common';
import { ApiResponseDto } from '@/common/response';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { Roles } from '@modules/auth/decorators';

@ApiTags(SWAGGER_API_TAGS.ADMIN_BLOGS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/blog')
export class BlogAdminController {
  constructor(private readonly blogAdminService: BlogAdminService) {}

  /**
   * Lấy danh sách tất cả bài viết
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách tất cả bài viết',
    description: 'Lấy danh sách tất cả bài viết với phân trang và lọc',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Trang hiện tại',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng bản ghi trên mỗi trang',
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Lọc theo trạng thái',
    type: String,
    example: 'APPROVED',
  })
  @ApiQuery({
    name: 'author_type',
    required: false,
    description: 'Lọc theo loại tác giả',
    type: String,
    example: 'USER',
  })
  // Đã bỏ lọc theo tags
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Tìm kiếm theo tiêu đề',
    type: String,
  })
  @ApiQuery({
    name: 'user_id',
    required: false,
    description: 'Lọc theo ID của người dùng',
    type: Number,
  })
  @ApiQuery({
    name: 'employee_id',
    required: false,
    description: 'Lọc theo ID của nhân viên',
    type: Number,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Danh sách bài viết đã được lấy thành công.',
    type: PaginatedBlogResponseDto,
  })
  @ApiResponseDoc({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    type: AppException,
  })
  async findAll(@Req() request: any): Promise<ApiResponseDto<PaginatedBlogResponseDto>> {
    // Lấy các tham số query trực tiếp từ request
    const query = request.query;

    // Tạo DTO thủ công với các trường hợp lệ
    const dto: GetAdminBlogsDto = {
      page: query.page ? parseInt(query.page) : 1,
      limit: query.limit ? parseInt(query.limit) : 10,
      status: query.status,
      authorType: query.author_type,
      userId: query.user_id ? parseInt(query.user_id) : undefined,
      employeeId: query.employee_id ? parseInt(query.employee_id) : undefined,
      titleSearch: query.search,
      sort: query.sort || 'createdAt',
      order: (query.order as 'ASC' | 'DESC') || 'DESC',
      sortBy: query.sortBy || query.sort || 'createdAt',
      sortDirection: (query.sortDirection as any) || (query.order as any) || 'DESC'
    };

    const result = await this.blogAdminService.findAll(dto);
    return ApiResponseDto.success(result,'Danh sách blog đã được lấy thành công.');
  }

  /**
   * Lấy danh sách bài viết chờ kiểm duyệt
   */
  @Get('pending')
  @ApiOperation({
    summary: 'Lấy danh sách bài viết chờ kiểm duyệt',
    description: 'Lấy danh sách bài viết đang chờ kiểm duyệt với phân trang',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Trang hiện tại',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng bản ghi trên mỗi trang',
    type: Number,
    example: 10,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Danh sách bài viết chờ kiểm duyệt đã được lấy thành công.',
    type: PaginatedBlogResponseDto,
  })
  @ApiResponseDoc({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    type: AppException,
  })
  async findPendingBlogs(@Req() request: any): Promise<ApiResponseDto<PaginatedBlogResponseDto>> {
    // Lấy các tham số query trực tiếp từ request
    const query = request.query;

    // Tạo DTO thủ công với các trường hợp lệ
    const dto: GetAdminBlogsDto = {
      page: query.page ? parseInt(query.page) : 1,
      limit: query.limit ? parseInt(query.limit) : 10,
      userId: query.user_id ? parseInt(query.user_id) : undefined,
      employeeId: query.employee_id ? parseInt(query.employee_id) : undefined,
      titleSearch: query.search,
      sort: query.sort || 'createdAt',
      order: (query.order as 'ASC' | 'DESC') || 'DESC',
      sortBy: query.sortBy || query.sort || 'createdAt',
      sortDirection: (query.sortDirection as any) || (query.order as any) || 'DESC'
    };

    const result = await this.blogAdminService.findPendingBlogs(dto);
    return ApiResponseDto.success(result,'Danh sách blog chờ kiểm duyệt đã được lấy thành công.');
  }

  /**
   * Lấy chi tiết bài viết
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết bài viết',
    description: 'Lấy chi tiết bài viết theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Chi tiết bài viết đã được lấy thành công.',
    type: BlogResponseDto,
  })
  @ApiResponseDoc({
    status: 404,
    description: 'Không tìm thấy bài viết.',
    type: AppException,
  })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<ApiResponseDto<BlogResponseDto>> {
    const result = await this.blogAdminService.findOne(id);
    return ApiResponseDto.success(result,'Chi tiết bài viết đã được lấy thành công.');
  }

  /**
   * Tạo bài viết mới (Bài viết hệ thống)
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo bài viết mới (Bài viết hệ thống)',
    description: 'Tạo bài viết mới với loại tác giả là SYSTEM',
  })
  @ApiBody({ type: CreateBlogDto })
  @ApiResponseDoc({
    status: 201,
    description: 'Bài viết đã được tạo thành công.',
    type: BlogResponseDto,
  })
  @ApiResponseDoc({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    type: AppException,
  })
  async create(@Body() dto: CreateBlogDto): Promise<ApiResponseDto<BlogResponseDto>> {
    const result = await this.blogAdminService.create(dto);
    return ApiResponseDto.created(result,'Bài viết đã được tạo thành công.');
  }

  /**
   * Cập nhật media cho bài viết
   */
  @Put(':id/media')
  @ApiOperation({
    summary: 'Cập nhật media cho bài viết',
    description: 'Cập nhật media (nội dung hoặc thumbnail) cho bài viết',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiBody({ type: UpdateBlogMediaDto })
  @ApiResponseDoc({
    status: 200,
    description: 'URL cho media đã được tạo thành công.',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Media URLs generated successfully' },
        result: {
          type: 'object',
          properties: {
            upload_url: { type: 'string', example: 'https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...' },
          },
        },
      },
    },
  })
  @ApiResponseDoc({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    type: AppException,
  })
  @ApiResponseDoc({
    status: 404,
    description: 'Không tìm thấy bài viết.',
    type: AppException,
  })
  async updateMedia(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateBlogMediaDto,
  ): Promise<ApiResponseDto<{ upload_url: string }>> {
    const result = await this.blogAdminService.updateMedia(id, dto);
    return ApiResponseDto.updated(result);
  }

  /**
   * Xóa bài viết
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa bài viết',
    description: 'Xóa bài viết theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Bài viết đã được xóa thành công.',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Blog deleted successfully' },
        result: { type: 'null', example: null },
      },
    },
  })
  @ApiResponseDoc({
    status: 404,
    description: 'Không tìm thấy bài viết.',
    type: AppException,
  })
  async delete(@Param('id', ParseIntPipe) id: number): Promise<ApiResponseDto<null>> {
    await this.blogAdminService.delete(id);
    return {
      code: 200,
      message: 'Blog deleted successfully',
      result: null,
    };
  }

  /**
   * Phê duyệt/Từ chối bài viết
   */
  @Put(':id/moderate')
  @ApiOperation({
    summary: 'Phê duyệt/Từ chối bài viết',
    description: 'Phê duyệt hoặc từ chối bài viết theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiBody({ type: ModerateBlogDto })
  @ApiResponseDoc({
    status: 200,
    description: 'Bài viết đã được kiểm duyệt thành công.',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Blog moderated successfully' },
        result: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 1 },
            status: { type: 'string', example: 'APPROVED' },
            employeeModerator: { type: 'number', nullable: true, example: null },
          },
        },
      },
    },
  })
  @ApiResponseDoc({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    type: AppException,
  })
  @ApiResponseDoc({
    status: 404,
    description: 'Không tìm thấy bài viết.',
    type: AppException,
  })
  async moderate(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: ModerateBlogDto,
  ): Promise<ApiResponseDto<{ id: number; status: string; employeeModerator: number | null }>> {
    const result = await this.blogAdminService.moderate(id, dto);
    return ApiResponseDto.success(result,'Bài viết đã được kiểm duyệt thành công.');
  }
}
