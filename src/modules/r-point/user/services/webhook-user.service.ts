import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Transaction } from 'typeorm';
import { WebhookRequestDto, WebhookResponseDto } from '../dto/webhook-request.dto';
import { PointPurchaseTransactionRepository } from '@modules/r-point/repositories';
import { TransactionStatus } from '@modules/r-point/enums';
import { User } from '@modules/user/entities';
import { AppException, ErrorCode } from '@common/exceptions';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class WebhookUserService {
  private readonly logger = new Logger(WebhookUserService.name);
  private readonly webhookApiKey: string | undefined;

  constructor(
    private readonly configService: ConfigService,
    private readonly transactionRepository: PointPurchaseTransactionRepository,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {
    this.webhookApiKey = this.configService.get<string>('SEPAY_WEBHOOK_API_KEY');
  }

  /**
   * Xác thực API key từ header
   * @param apiKey API key từ header
   * @returns true nếu API key hợp lệ, false nếu không
   */
  validateApiKey(apiKey: string): boolean {
    if (!this.webhookApiKey) {
      this.logger.warn('SEPAY_WEBHOOK_API_KEY is not configured');
      return false;
    }
    return apiKey === this.webhookApiKey;
  }

  /**
   * Xử lý webhook từ SePay
   * @param webhookRequest Dữ liệu webhook
   * @returns Kết quả xử lý
   */
  @Transactional()
  async processWebhook(webhookRequest: WebhookRequestDto): Promise<WebhookResponseDto> {
    try {
      this.logger.log(`Processing webhook: ${JSON.stringify(webhookRequest)}`);

      // Kiểm tra nội dung chuyển khoản có đúng định dạng không
      const pattern = /REDAI(\d+)SEPAY/;
      const match = webhookRequest.content.match(pattern);

      if (!match) {
        this.logger.warn(`Invalid content format: ${webhookRequest.content}`);
        return {
          success: false,
          message: 'Invalid content format'
        };
      }

      // Lấy ID giao dịch từ nội dung chuyển khoản
      const transactionId = parseInt(match[1]);
      this.logger.log(`Extracted transaction ID: ${transactionId}`);

      // Tìm giao dịch trong database
      const transaction = await this.transactionRepository.findById(transactionId);

      if (!transaction) {
        this.logger.warn(`Transaction not found: ${transactionId}`);
        return {
          success: false,
          message: 'Transaction not found'
        };
      }

      // Kiểm tra trạng thái giao dịch
      if (transaction.status !== TransactionStatus.PENDING) {
        this.logger.warn(`Transaction is not in PENDING status: ${transactionId}, current status: ${transaction.status}`);
        return {
          success: false,
          message: 'Transaction is not in PENDING status'
        };
      }

      // Kiểm tra loại giao dịch và số tiền
      if (webhookRequest.transferType !== 'in' || !this.isValidAmount(webhookRequest.transferAmount, transaction.amount)) {
        this.logger.warn(`Invalid transfer type or amount: type=${webhookRequest.transferType}, amount=${webhookRequest.transferAmount}, expected=${transaction.amount}`);
        return {
          success: false,
          message: 'Invalid transfer type or amount'
        };
      }

      // Tìm người dùng
      const user = await this.userRepository.findOne({ where: { id: transaction.userId } });
      if (!user) {
        this.logger.warn(`User not found: ${transaction.userId}`);
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Cập nhật trạng thái giao dịch
      await this.updateTransaction(transaction, webhookRequest);

      // Cộng điểm cho người dùng
      await this.addPointsToUser(user, transaction.pointsAmount);

      this.logger.log(`Webhook processed successfully for transaction: ${transactionId}`);
      return {
        success: true,
        message: 'Webhook processed successfully'
      };
    } catch (error) {
      this.logger.error(`Error processing webhook: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Error processing webhook: ${error.message}`
      };
    }
  }

  /**
   * Kiểm tra số tiền thanh toán có hợp lệ không
   * @param paidAmount Số tiền đã thanh toán
   * @param expectedAmount Số tiền cần thanh toán
   * @returns true nếu số tiền hợp lệ, false nếu không
   */
  private isValidAmount(paidAmount: number, expectedAmount: number): boolean {
    // Cho phép sai số 1% hoặc 1,000 VND (lấy giá trị nhỏ hơn)
    const tolerance = Math.min(expectedAmount * 0.01, 1000);
    return Math.abs(paidAmount - expectedAmount) <= tolerance;
  }

  /**
   * Cập nhật thông tin giao dịch
   * @param transaction Giao dịch cần cập nhật
   * @param webhookRequest Dữ liệu webhook
   */
  private async updateTransaction(transaction: any, webhookRequest: WebhookRequestDto): Promise<void> {
    transaction.status = TransactionStatus.CONFIRMED;
    transaction.referenceId = webhookRequest.referenceCode;
    transaction.updatedAt = Math.floor(Date.now() / 1000);
    transaction.completedAt = Math.floor(Date.now() / 1000);

    await this.transactionRepository.update(transaction.id, transaction);
  }

  /**
   * Cộng điểm cho người dùng
   * @param user Người dùng cần cộng điểm
   * @param pointsAmount Số điểm cần cộng
   */
  private async addPointsToUser(user: User, pointsAmount: number): Promise<void> {
    // Lấy số dư hiện tại
    const currentBalance = user.pointsBalance || 0;

    // Cộng điểm
    user.pointsBalance = currentBalance + pointsAmount;

    // Cập nhật thời gian
    user.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu vào database
    await this.userRepository.save(user);
  }
}
