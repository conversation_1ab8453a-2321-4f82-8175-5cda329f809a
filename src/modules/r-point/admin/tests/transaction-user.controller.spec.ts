import { Test, TestingModule } from '@nestjs/testing';
import { TransactionUserController } from '../controllers/transaction-user.controller';
import { TransactionAdminService } from '../services';
import { TransactionStatus } from '@modules/r-point/enums';
import { TransactionPaginatedResponseDto, TransactionResponseDto, StatisticsResponseDto } from '../dto';

describe('TransactionUserController', () => {
  let controller: TransactionUserController;
  let service: jest.Mocked<TransactionAdminService>;

  const mockTransactionAdminService = {
    getUserTransactions: jest.fn(),
    getTransactionById: jest.fn(),
    getStatistics: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TransactionUserController],
      providers: [
        { provide: TransactionAdminService, useValue: mockTransactionAdminService },
      ],
    }).compile();

    controller = module.get<TransactionUserController>(TransactionUserController);
    service = module.get(TransactionAdminService) as jest.Mocked<TransactionAdminService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getUserTransactions', () => {
    it('should return paginated transactions', async () => {
      // Mock data
      const mockPaginatedResponse: TransactionPaginatedResponseDto = {
        items: [
          {
            id: 1,
            userId: 1,
            pointId: 1,
            amount: 100000,
            pointsAmount: 100,
            status: TransactionStatus.CONFIRMED,
            user: { id: 1, fullName: 'User 1', email: '<EMAIL>', phone: '**********' },
            point: { id: 1, name: 'Point 1', cash: 100000, rate: 1000 },
            paymentMethod: 'VNPAY',
            referenceId: 'VNP123456',

            balanceBefore: 0,
            balanceAfter: 100,
            couponId: 1,
            couponAmount: 0,
            createdAt: 1625097600000,
            completedAt: 1625097660000
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      // Mock service response
      service.getUserTransactions.mockResolvedValue(mockPaginatedResponse);

      // Call the controller method
      const result = await controller.getUserTransactions({
        page: 1,
        limit: 10,
      });

      // Assertions
      expect(service.getUserTransactions).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
      });
      expect(result.code).toBe(200);
      expect(result.result).toEqual(mockPaginatedResponse);
    });
  });

  describe('getTransactionById', () => {
    it('should return transaction details', async () => {
      // Mock data
      const mockTransaction: TransactionResponseDto = {
        id: 1,
        userId: 1,
        pointId: 1,
        amount: 100000,
        pointsAmount: 100,
        status: TransactionStatus.CONFIRMED,
        user: { id: 1, fullName: 'User 1', email: '<EMAIL>', phone: '**********' },
        point: { id: 1, name: 'Point 1', cash: 100000, rate: 1000 },
        paymentMethod: 'VNPAY',
        referenceId: 'VNP123456',

        balanceBefore: 0,
        balanceAfter: 100,
        couponId: 1,
        couponAmount: 0,
        createdAt: 1625097600000,
        completedAt: 1625097660000
      };

      // Mock service response
      service.getTransactionById.mockResolvedValue(mockTransaction);

      // Call the controller method
      const result = await controller.getTransactionById(1);

      // Assertions
      expect(service.getTransactionById).toHaveBeenCalledWith(1);
      expect(result.code).toBe(200);
      expect(result.result).toEqual(mockTransaction);
    });
  });

  describe('getStatistics', () => {
    it('should return statistics', async () => {
      // Mock data
      const mockStatistics: StatisticsResponseDto = {
        overview: {
          totalTransactions: 2,
          totalAmount: 300000,
          totalPointsSold: 300,
          uniqueUsers: 2,
          averageTransactionValue: 150000,
        },
        details: {
          byStatus: [
            { status: TransactionStatus.CONFIRMED.toString(), count: 2, totalAmount: 300000 },
          ],
          byPaymentMethod: [
            { paymentMethod: 'VNPAY', count: 1, totalAmount: 100000 },
            { paymentMethod: 'MOMO', count: 1, totalAmount: 200000 },
          ],
          byPointPackage: [
            { pointId: 1, pointName: 'Point 1', count: 1, totalAmount: 100000, totalPoints: 100 },
            { pointId: 2, pointName: 'Point 2', count: 1, totalAmount: 200000, totalPoints: 200 },
          ],
          byCoupon: [
            { couponId: '1', couponCode: 'COUPON1', usageCount: 1, totalDiscountAmount: 10000 },
          ],
        },
      };

      // Mock service response
      service.getStatistics.mockResolvedValue(mockStatistics);

      // Call the controller method
      const result = await controller.getStatistics(1625097600000, 1625184000000);

      // Assertions
      expect(service.getStatistics).toHaveBeenCalledWith(1625097600000, 1625184000000);
      expect(result.code).toBe(200);
      expect(result.result).toEqual(mockStatistics);
    });
  });
});
