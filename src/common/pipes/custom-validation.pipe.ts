import { ValidationPipe, ValidationError, BadRequestException } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions';
import { TOOLS_ERROR_CODES } from '@modules/tools/exceptions';

/**
 * Custom ValidationPipe để chuyển đổi lỗi validation thành AppException
 */
export class CustomValidationPipe extends ValidationPipe {
  constructor() {
    super({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      exceptionFactory: (errors: ValidationError[]) => {
        // Tìm lỗi liên quan đến toolName và pattern
        const toolNamePatternError = this.findToolNamePatternError(errors);
        
        // Nếu có lỗi toolName pattern, ném AppException với mã lỗi cụ thể
        if (toolNamePatternError) {
          throw new AppException(
            TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
            'Tên tool chỉ được chứa a-z, A-<PERSON>, 0-9, hoặc dấu gạch dưới',
            { property: 'toolName', errors: toolNamePatternError }
          );
        }
        
        // Nếu không phải lỗi toolName pattern, sử dụng xử lý mặc định
        return new BadRequestException(this.formatErrors(errors));
      },
    });
  }

  /**
   * Tìm lỗi liên quan đến toolName và pattern
   * @param errors Danh sách lỗi validation
   * @returns Lỗi toolName pattern nếu tìm thấy, null nếu không tìm thấy
   */
  private findToolNamePatternError(errors: ValidationError[]): string[] | null {
    for (const error of errors) {
      if (error.property === 'toolName' && error.constraints && error.constraints.matches) {
        return [error.constraints.matches];
      }
      
      // Tìm kiếm đệ quy trong các lỗi con
      if (error.children && error.children.length > 0) {
        const childError = this.findToolNamePatternError(error.children);
        if (childError) {
          return childError;
        }
      }
    }
    
    return null;
  }

  /**
   * Format lỗi validation thành dạng dễ đọc
   * @param errors Danh sách lỗi validation
   * @returns Danh sách lỗi đã được format
   */
  private formatErrors(errors: ValidationError[]): string[] {
    return errors.map(error => {
      if (error.constraints) {
        return Object.values(error.constraints).map(constraint => 
          `${error.property} - ${constraint}`
        );
      }
      
      if (error.children && error.children.length > 0) {
        const childErrors = this.formatErrors(error.children);
        return childErrors.map(childError => 
          `${error.property}.${childError}`
        );
      }
      
      return [`${error.property} - Invalid value`];
    }).flat();
  }
}
