import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueueService } from './queue.service';
import { QueueName, DEFAULT_JOB_OPTIONS } from './queue.constants';

/**
 * Module quản lý hệ thống queue của ứng dụng
 */
@Module({
  imports: [
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const redisUrl = configService.get<string>('REDIS_URL');
        
        return {
          // Cấu hình kết nối Redis dựa trên URL thay vì các tham số riêng lẻ
          redis: redisUrl,
          defaultJobOptions: DEFAULT_JOB_OPTIONS,
        };
      },
    }),
    /**
     * <PERSON><PERSON>ng ký các queue cụ thể ở đây
     * Mỗi queue là một module con trong hệ thống
     */
    BullModule.registerQueue(
      {
        name: QueueName.EMAIL, // Queue xử lý email
      },
      {
        name: QueueName.SMS, // Queue xử lý SMS
      },
      {
        name: QueueName.NOTIFICATION, // Queue xử lý thông báo
      },
      {
        name: QueueName.DATA_PROCESS, // Queue xử lý dữ liệu
      },
      {
        name: QueueName.SEND_SYSTEM_EMAIL, // Queue xử lý dữ liệu
      },
    ),
  ],
  providers: [QueueService],
  exports: [QueueService, BullModule],
})
export class QueueModule {} 