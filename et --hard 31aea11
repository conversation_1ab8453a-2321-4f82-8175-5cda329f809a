[33m70c1bf1[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mfeature/admin-task[m[33m)[m after merge
[33m31aea11[m[33m ([m[1;31morigin/feature/admin-task[m[33m)[m Hoàn thiện admin module task + viết test
[33m4c04989[m[33m ([m[1;31morigin/feature/task[m[33m, [m[1;32mfeature/task[m[33m)[m update
[33mdcf6f76[m merged
[33m2b9209c[m[33m ([m[1;32mfeature/knowledge-file-admin-rag[m[33m, [m[1;32mdevelop[m[33m)[m Merge branch 'develop' of https://github.com/redai-redon/redai-v201-be-app into develop
[33mf375697[m Hoà<PERSON> thành, test tay phần admin cho module task
[33m07c2f4a[m update integration
[33mf990d06[m update integration
[33m0593598[m[33m ([m[1;31morigin/feature/chat[m[33m)[m update integration
[33mfbcfd7b[m update facebook integration
[33mce82183[m Sửa api xóa, xóa liên kết cho module media
[33m6180efa[m update feature api for user step connection
[33m9f9b8bf[m fix
[33m3a91f6b[m fix
[33m24de07b[m fix
[33m18ad5e5[m update api for user step
[33m59139e4[m fix lỗi sau khi a nhật sửa cái enum và lỗi media
[33m85a6b14[m fix lỗi sau khi a nhật sửa cái enum
[33m8578f5e[m Commit để sửa media
[33mbb849ae[m create feature api for user step
[33m14fd193[m fix
[33mdc14808[m update
[33mcbc989e[m create feature api for task execution
[33mcabc30a[m create feature api for user task
[33m372e980[m update admin agent uesr
[33mf96a1d8[m Merge branch 'develop' into feature/admin-task
[33m213f412[m for save
[33mfff04ff[m fix
[33mafb1e85[m Merge branch 'develop' into feature/chat
[33mbf46924[m Merge branch 'develop' into feature/task
[33m3970d33[m update
[33m53335e5[m fix
[33m5e19c9c[m fix
[33mc313852[m update
[33m2a5443a[m Merge branch 'develop' into feature/task
[33m861b8a9[m update
[33m47c8710[m update task
[33m14ee2ed[m Merge branch 'develop' of https://github.com/redai-redon/redai-v201-be-app into develop
[33m26bee8b[m update task
[33m55a4b88[m update
[33m931e6a6[m update rules
[33m0eb93fa[m Fix
[33m371ceb6[m Fix
[33m3f48160[m docs task
[33mc8fe5a6[m initial module task
[33m8e7e27d[m update
[33m9ac919f[m update strategy
[33m02d5e9d[m fix
[33m176ac0a[m fix
[33m1185975[m update
[33m5f507d3[m Fix lỗi sau pull
[33m3a12768[m Merge branch 'develop' of https://github.com/redai-redon/redai-v201-be-app into develop
[33me13338c[m Tinh chỉnh logic show content, Permission cho admin module blog
[33m5916081[m fix
[33m267910e[m fix
[33m591ac50[m Merge pull request #36 from redai-redon/develop
[33m4acba6b[m Tinh chỉnh logic show content, Permission cho admin module blog
[33m7845b16[m Merge branch 'develop' of https://github.com/redai-redon/redai-v201-be-app into develop
[33m32371ce[m Xong logic user module blog
[33m4951f71[m fix
[33m36005d2[m fix
[33m75442bf[m fix
[33m5ac0236[m fix
[33m5c74f05[m Chỉnh role, permission cho admin module media, sửa lỗi nhỏ
[33m6a3bd04[m Chỉnh role, permission cho admin module media
[33m4a677ac[m fix
[33m095a2c3[m fix
[33maf3751b[m tinh tỉnh api get, get chi tiết, sửa lỗi api mua blog
[33ma80d75c[m fix
[33m4c880ee[m fix
[33m7ac24c4[m Fix
[33mb8f55da[m update
[33mc5d8bea[m update agent template
[33m9e90088[m update agent base
[33mc2bd9ab[m fix
[33m458a79e[m update
[33md1d7a75[m Merge branch 'feature/chat' into develop
[33mc4b2a5c[m update agent_role agent_system
[33mdd8ed70[m Merge branch 'develop' of https://github.com/redai-redon/redai-v201-be-app into develop
[33mba643c0[m fix xong api lấy chi tiết blog
[33mc9d25c1[m Merge branch 'develop' of https://github.com/redai-redon/redai-v201-be-app into develop
[33md233d1f[m update provider and base_model
[33m571a7e7[m Merge branch 'develop' of https://github.com/redai-redon/redai-v201-be-app into develop
[33ma15c831[m tinh chỉnh logic api get của blog
[33mb82cfa3[m update
[33m029d168[m Merge pull request #34 from redai-redon/feature/business-products
[33m530eff4[m[33m ([m[1;32mfeature/business-products[m[33m)[m for create pull request
[33m115f4cf[m modify provider
[33m8ee8d3f[m Hoan thanh phan admin module business sau merge
[33mdf69899[m update chat
[33m0f621b5[m update
[33md267352[m Merge admin và user module business thàng công, test full api user
[33m3c2f410[m update feature payment marrketplace
[33mf93cab0[m update feature payment marrketplace
[33meb6ffab[m Merge branch 'fix/media' into develop
[33me6abe93[m[33m ([m[1;31morigin/fix/media[m[33m)[m update feature api total data
[33m7d9ce3e[m Merge branch 'develop' of https://github.com/redai-redon/redai-v201-be-app into develop
[33m68734fd[m fix no get ownedBy
[33m21dad68[m fix
[33m1708d5f[m Fix
[33m0d97121[m Fix
[33m21ff516[m resolve conflic
[33m016b80c[m Merge branch 'develop' of https://github.com/redai-redon/redai-v201-be-app into develop
[33m11e908c[m update status in media get all
[33m4118596[m fix
[33mf2971a7[m for merge
[33mb0b674a[m fix
[33m7f9fce4[m fix
[33mb6ba02d[m update swagger tags
[33m471958f[m fix merge
[33me7db439[m[33m ([m[1;31morigin/feature/fix-tools[m[33m)[m update
[33mc49a910[m update tool list
[33md487626[m test + fix lỗi cho test repository phần user module business
[33me7bc0f4[m[33m ([m[1;31morigin/feature/business-products-admin[m[33m, [m[1;32mfeature/business-products-admin[m[33m)[m Hoàn thiện phần file, folder cho admin của module business + viết test
[33mfa79ace[m update
[33me2c0531[m fix
[33m026db23[m update feature for files
[33m47b7544[m Hoàn thiện phần file, folder cho admin của module business + viết test
[33md7c3fc3[m fix
[33m8b5769a[m fix
[33m1798056[m feature
[33m8c3cfb7[m update
[33m28baef1[m create api physical warehouse and inventory
[33meaca2de[m fix conflix
[33mfe6d14c[m merge finish
[33m6d973d3[m commit
[33m923ed9f[m for merge
[33m25b3bff[m fix
[33m51e1146[m feature:for warehouse, warehouse custom field
[33m89af617[m Merge branch 'develop' into feature/business-products-admin
[33mbef897f[m Bổ sung 6 api get cho user-convert, user-convert-customer, và user-oder + viết test cho module business
[33m09fa7d8[m fix
[33m1a42094[m fix
[33m467bed9[m fix base model repo
[33m5203ea0[m saveMay131733
[33m888db8c[m saveMay131707
[33m317cd56[m update
[33m817292d[m update
[33m0399f17[m 19-buildModel-finetune
[33m68b6e21[m saveMay131631
[33mf25cf1b[m update by rule for user convert, user convert customer, user order
[33m6853eb1[m update feature for user convert, user convert customer, user order
[33m7e10daf[m merge develop
[33m6f79718[m update response common
[33me4e4ca2[m update merge
[33m4567ac3[m step 1
[33m10f98b4[m merge
[33m7cbe1fc[m merge
[33m5e957ce[m fix : Fix bug when create product
[33m5e8c232[m for merge
[33m0dbaccd[m fix
[33m1492dcd[m Tinh chỉnh tối ưu code, tránh truy vấn nhiều với db
[33m01ccfdd[m tool build in base
[33m74260a4[m[33m ([m[1;31morigin/feature/payment[m[33m)[m fix merge
[33m8da2e90[m update
[33m4b176c8[m fix code follow rules
[33m462a0d4[m Hoàn thiện, tinh nghiệp vụ cho admin module business + viết test
[33m8055315[m fix
[33me5afcfd[m for save
[33m42ffb88[m for save
[33m6b13915[m merge develop
[33me65c3d0[m[33m ([m[1;31morigin/feature/fix-agent[m[33m)[m updaet agent user
[33m84731cf[m Tinh chỉnh 1 chút
[33m59125e7[m saveMay120848
[33mb86024b[m[33m ([m[1;31morigin/feature/email-system[m[33m)[m fix
[33m0191967[m update feature api
[33md253b65[m utils list model
[33m6bfacab[m fix NV 1 2
[33m35d2aca[m Hoàn thiện, tinh chỉnh phần admin module business
[33m4e72ab8[m add feature bien the
[33md7b35b8[m Hoàn thiện, tinh chỉnh phần admin module business
[33m2b5d750[m saveMay101026
[33mcd03aa4[m Hoàn thiện, tinh chỉnh phần admin module business
[33m46d059c[m Sửa lỗi Type 'number | null' is not assignable to type 'number | undefined' trong AuthVerificationLogRepository
[33m9d71fad[m Hoàn thiện, tinh chỉnh phần admin module business
