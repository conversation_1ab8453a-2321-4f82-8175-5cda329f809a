-- <PERSON><PERSON><PERSON> để thêm cột original_version_id vào bảng user_tool_versions
-- <PERSON><PERSON>y script này nếu database chưa có cột original_version_id

-- Thê<PERSON> cột original_version_id
ALTER TABLE user_tool_versions 
ADD COLUMN IF NOT EXISTS original_version_id UUID NULL;

-- Thê<PERSON> comment cho cột
COMMENT ON COLUMN user_tool_versions.original_version_id 
IS 'ID của phiên bản gốc từ admin tool version, tham chiếu đến admin_tool_versions.id';

-- C<PERSON><PERSON> nhật unique constraint (xóa constraint cũ nếu có)
ALTER TABLE user_tool_versions 
DROP CONSTRAINT IF EXISTS unique_user_function_version;

-- Thêm unique constraint mới
ALTER TABLE user_tool_versions 
ADD CONSTRAINT unique_user_function_version 
UNIQUE (user_id, original_function_id, original_version_id);

-- <PERSON><PERSON><PERSON> thị cấu trúc bảng để kiểm tra
\d user_tool_versions;
